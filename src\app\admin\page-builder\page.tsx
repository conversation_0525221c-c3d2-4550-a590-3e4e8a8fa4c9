'use client'

import React, { useState, useEffect } from 'react'
import { usePageBuilderStore } from '@/lib/page-builder/store'
import ComponentLibrary from '@/components/page-builder/ComponentLibrary'
import Canvas from '@/components/page-builder/Canvas'
import PropertyPanel from '@/components/page-builder/PropertyPanel'
import Toolbar from '@/components/page-builder/Toolbar'
import PageManager from '@/components/page-builder/PageManager'
import KeyboardShortcuts from '@/components/page-builder/KeyboardShortcuts'
import ExportDialog from '@/components/page-builder/ExportDialog'
import { showSuccess, showError } from '@/lib/page-builder/notifications'
import { 
  Bars3Icon,
  XMarkIcon,
  EyeIcon,
  DevicePhoneMobileIcon,
  DeviceTabletIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline'

const PageBuilderPage: React.FC = () => {
  const [showPageManager, setShowPageManager] = useState(false)
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isPublishing, setIsPublishing] = useState(false)
  
  const {
    mode,
    viewport,
    leftPanelOpen,
    rightPanelOpen,
    leftPanelWidth,
    rightPanelWidth,
    setMode,
    setViewport,
    toggleLeftPanel,
    toggleRightPanel,
    currentPage,
    isDirty,
    components,
    setDirty,
    addComponent
  } = usePageBuilderStore()
  
  // Handle unsaved changes warning
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isDirty) {
        e.preventDefault()
        e.returnValue = ''
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [isDirty])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+S or Cmd+S - Save
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        if (isDirty && currentPage) {
          handleSavePage()
        }
      }

      // Ctrl+Shift+P or Cmd+Shift+P - Publish
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
        e.preventDefault()
        if (currentPage) {
          handlePublishPage()
        }
      }

      // Ctrl+Shift+E or Cmd+Shift+E - Export
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'E') {
        e.preventDefault()
        setShowExportDialog(true)
      }

      // Ctrl+Shift+M or Cmd+Shift+M - Page Manager
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'M') {
        e.preventDefault()
        setShowPageManager(true)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isDirty, currentPage])
  
  const viewportIcons = {
    desktop: ComputerDesktopIcon,
    tablet: DeviceTabletIcon,
    mobile: DevicePhoneMobileIcon
  }

  // Save page functionality
  const handleSavePage = async () => {
    if (!currentPage) {
      alert('No page selected to save')
      return
    }

    setIsSaving(true)
    try {
      const response = await fetch(`/api/page-builder/pages/${currentPage.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          layout: components,
          createVersion: true // Create a new version when saving
        })
      })

      if (!response.ok) {
        throw new Error('Failed to save page')
      }

      setDirty(false)
      showSuccess('Page Saved', 'Your page has been saved successfully!')

    } catch (error) {
      console.error('Error saving page:', error)
      showError('Save Failed', 'Failed to save page. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  // Publish page functionality
  const handlePublishPage = async () => {
    if (!currentPage) {
      alert('No page selected to publish')
      return
    }

    setIsPublishing(true)
    try {
      // First save the current state
      if (isDirty) {
        await handleSavePage()
      }

      // Then publish the page
      const response = await fetch(`/api/page-builder/pages/${currentPage.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isPublished: true
        })
      })

      if (!response.ok) {
        throw new Error('Failed to publish page')
      }

      showSuccess('Page Published', `"${currentPage.title}" has been published successfully!`)

      // Open preview in new tab
      window.open(`/preview/${currentPage.slug}`, '_blank')

    } catch (error) {
      console.error('Error publishing page:', error)
      showError('Publish Failed', 'Failed to publish page. Please try again.')
    } finally {
      setIsPublishing(false)
    }
  }

  // Test function to add a component
  const addTestComponent = () => {
    const testComponent = {
      id: `test-${Date.now()}`,
      type: 'text',
      name: 'Test Text Component',
      content: {
        text: 'Hello! This is a test component to verify the page builder is working.',
        tag: 'h2'
      },
      styles: {
        color: '#333333',
        fontSize: '24px',
        fontWeight: 'bold',
        textAlign: 'center',
        padding: '20px',
        backgroundColor: '#f0f9ff',
        border: '2px solid #3b82f6',
        borderRadius: '8px'
      },
      position: {
        x: 50,
        y: 50,
        width: 400,
        height: 100
      },
      order: 1,
      isVisible: true,
      isLocked: false
    }

    addComponent(testComponent)
  }
  
  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Top toolbar */}
      <div className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-4">
        {/* Left section */}
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowPageManager(true)}
            className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            title="Manage Pages (Ctrl+Shift+M)"
          >
            <Bars3Icon className="h-4 w-4" />
            <span>Pages</span>
          </button>
          
          <div className="text-sm text-gray-600">
            {currentPage ? (
              <div className="flex items-center space-x-2">
                <span>
                  Editing: <span className="font-medium">{currentPage.title}</span>
                  {isDirty && <span className="text-orange-500 ml-1">*</span>}
                </span>
                <div className="flex items-center space-x-1">
                  {currentPage.isPublished ? (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Published
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      Draft
                    </span>
                  )}
                  {currentPage.isHomePage && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Home
                    </span>
                  )}
                </div>
              </div>
            ) : (
              <span>No page selected - Click "Pages" to select or create a page</span>
            )}
          </div>
        </div>
        
        {/* Center section - Mode and viewport controls */}
        <div className="flex items-center space-x-4">
          {/* Mode toggle */}
          <div className="flex rounded-md border border-gray-300">
            <button
              onClick={() => setMode('edit')}
              className={`px-3 py-2 text-sm font-medium rounded-l-md ${
                mode === 'edit'
                  ? 'bg-blue-50 text-blue-700 border-blue-200'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              Edit
            </button>
            <button
              onClick={() => setMode('preview')}
              className={`px-3 py-2 text-sm font-medium rounded-r-md border-l ${
                mode === 'preview'
                  ? 'bg-blue-50 text-blue-700 border-blue-200'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              <EyeIcon className="h-4 w-4 mr-1 inline" />
              Preview
            </button>
          </div>
          
          {/* Viewport selector */}
          <div className="flex rounded-md border border-gray-300">
            {Object.entries(viewportIcons).map(([size, Icon]) => (
              <button
                key={size}
                onClick={() => setViewport(size as any)}
                className={`px-3 py-2 text-sm font-medium ${
                  size === 'desktop' ? 'rounded-l-md' : 
                  size === 'mobile' ? 'rounded-r-md border-l' : 'border-l'
                } ${
                  viewport === size
                    ? 'bg-blue-50 text-blue-700 border-blue-200'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                title={`${size.charAt(0).toUpperCase() + size.slice(1)} view`}
              >
                <Icon className="h-4 w-4" />
              </button>
            ))}
          </div>
        </div>
        
        {/* Right section */}
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleLeftPanel}
            className={`p-2 rounded-md ${
              leftPanelOpen 
                ? 'bg-blue-50 text-blue-700' 
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            title="Toggle component library"
          >
            <Bars3Icon className="h-4 w-4" />
          </button>
          
          <button
            onClick={toggleRightPanel}
            className={`p-2 rounded-md ${
              rightPanelOpen 
                ? 'bg-blue-50 text-blue-700' 
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            title="Toggle properties panel"
          >
            <Bars3Icon className="h-4 w-4 transform rotate-180" />
          </button>
          
          <div className="w-px h-6 bg-gray-300" />
          
          <button
            onClick={handleSavePage}
            disabled={!isDirty || isSaving}
            className={`px-4 py-2 text-sm font-medium rounded-md ${
              isDirty && !isSaving
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
            title={isDirty ? 'Save Page (Ctrl+S)' : 'No changes to save'}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </button>
          
          <button
            onClick={() => setShowExportDialog(true)}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            title="Export Page (Ctrl+Shift+E)"
          >
            Export
          </button>

          <button
            onClick={handlePublishPage}
            disabled={!currentPage || isPublishing}
            className={`px-4 py-2 text-sm font-medium rounded-md ${
              currentPage && !isPublishing
                ? 'text-white bg-green-600 hover:bg-green-700'
                : 'text-gray-500 bg-gray-300 cursor-not-allowed'
            }`}
            title={currentPage ? 'Publish Page (Ctrl+Shift+P)' : 'Select a page to publish'}
          >
            {isPublishing ? 'Publishing...' : 'Publish'}
          </button>
        </div>
      </div>
      
      {/* Main content area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left panel - Component Library */}
        {leftPanelOpen && (
          <div 
            className="bg-white border-r border-gray-200 flex-shrink-0"
            style={{ width: leftPanelWidth }}
          >
            <ComponentLibrary />
          </div>
        )}
        
        {/* Center panel - Canvas */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Toolbar */}
          {mode === 'edit' && <Toolbar />}
          
          {/* Canvas container */}
          <div className="flex-1 overflow-auto bg-gray-50 p-4">
            <div className="max-w-full mx-auto">
              {currentPage ? (
                <div>
                  {/* Debug info */}
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded text-sm">
                    <div className="flex items-center justify-between">
                      <div>
                        <strong>Page:</strong> {currentPage.title} |
                        <strong> Components:</strong> {components.length} |
                        <strong> Layout:</strong> {Array.isArray(currentPage.layout) ? currentPage.layout.length : 'Not array'}
                      </div>
                      <button
                        onClick={addTestComponent}
                        className="px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700"
                      >
                        Add Test Component
                      </button>
                    </div>
                  </div>
                  <Canvas className="shadow-lg rounded-lg overflow-hidden" />
                </div>
              ) : (
                <div className="flex items-center justify-center h-full min-h-[500px]">
                  <div className="text-center max-w-md">
                    <div className="text-6xl mb-6">🎨</div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Welcome to Page Builder</h2>
                    <p className="text-gray-600 mb-6">
                      Get started by selecting an existing page or creating a new one.
                    </p>
                    <div className="space-y-3">
                      <button
                        onClick={() => setShowPageManager(true)}
                        className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium"
                      >
                        <Bars3Icon className="h-5 w-5 mr-2" />
                        Manage Pages
                      </button>
                      <div className="text-sm text-gray-500">
                        Or use keyboard shortcut: <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+Shift+M</kbd>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Right panel - Properties */}
        {rightPanelOpen && (
          <div 
            className="bg-white border-l border-gray-200 flex-shrink-0"
            style={{ width: rightPanelWidth }}
          >
            <PropertyPanel />
          </div>
        )}
      </div>
      
      {/* Page Manager Modal */}
      {showPageManager && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Page Manager
                  </h3>
                  <button
                    onClick={() => setShowPageManager(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
                
                <PageManager onClose={() => setShowPageManager(false)} />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Keyboard Shortcuts Helper */}
      <KeyboardShortcuts />

      {/* Export Dialog */}
      <ExportDialog
        isOpen={showExportDialog}
        onClose={() => setShowExportDialog(false)}
      />
    </div>
  )
}

export default PageBuilderPage
