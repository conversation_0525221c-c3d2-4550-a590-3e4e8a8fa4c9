'use client'

import React, { useState, useEffect } from 'react'
import { usePageBuilderStore } from '@/lib/page-builder/store'
import ComponentLibrary from '@/components/page-builder/ComponentLibrary'
import Canvas from '@/components/page-builder/Canvas'
import PropertyPanel from '@/components/page-builder/PropertyPanel'
import Toolbar from '@/components/page-builder/Toolbar'
import PageManager from '@/components/page-builder/PageManager'
import KeyboardShortcuts from '@/components/page-builder/KeyboardShortcuts'
import ExportDialog from '@/components/page-builder/ExportDialog'
import DataBindingPanel from '@/components/page-builder/DataBindingPanel'
import PageTemplates from '@/components/page-builder/PageTemplates'
import { showSuccess, showError } from '@/lib/page-builder/notifications'
import {
  Bars3Icon,
  XMarkIcon,
  EyeIcon,
  DevicePhoneMobileIcon,
  DeviceTabletIcon,
  ComputerDesktopIcon,
  CogIcon,
  DocumentTextIcon,
  PaintBrushIcon,
  CircleStackIcon,
  RectangleStackIcon,
  PlayIcon,
  StopIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  DocumentDuplicateIcon,
  CloudArrowUpIcon,
  ShareIcon
} from '@heroicons/react/24/outline'

const PageBuilderPage: React.FC = () => {
  const [showPageManager, setShowPageManager] = useState(false)
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showDataBinding, setShowDataBinding] = useState(false)
  const [showTemplates, setShowTemplates] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isPublishing, setIsPublishing] = useState(false)
  const [activePanel, setActivePanel] = useState<'components' | 'properties' | 'data' | 'templates'>('components')

  const {
    mode,
    viewport,
    leftPanelOpen,
    rightPanelOpen,
    leftPanelWidth,
    rightPanelWidth,
    setMode,
    setViewport,
    toggleLeftPanel,
    toggleRightPanel,
    currentPage,
    isDirty,
    components,
    setDirty,
    addComponent,
    undo,
    redo,
    canUndo,
    canRedo
  } = usePageBuilderStore()

  const viewportIcons = {
    desktop: ComputerDesktopIcon,
    tablet: DeviceTabletIcon,
    mobile: DevicePhoneMobileIcon
  }

  // Handle unsaved changes warning
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isDirty) {
        e.preventDefault()
        e.returnValue = ''
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [isDirty])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+S or Cmd+S - Save
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        if (isDirty && currentPage) {
          handleSavePage()
        }
      }

      // Ctrl+Shift+P or Cmd+Shift+P - Publish
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
        e.preventDefault()
        if (currentPage) {
          handlePublishPage()
        }
      }

      // Ctrl+Shift+E or Cmd+Shift+E - Export
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'E') {
        e.preventDefault()
        setShowExportDialog(true)
      }

      // Ctrl+Shift+M or Cmd+Shift+M - Page Manager
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'M') {
        e.preventDefault()
        setShowPageManager(true)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isDirty, currentPage])

  // Save page functionality
  const handleSavePage = async () => {
    if (!currentPage) {
      alert('No page selected to save')
      return
    }

    setIsSaving(true)
    try {
      const response = await fetch(`/api/page-builder/pages/${currentPage.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          layout: components,
          createVersion: true // Create a new version when saving
        })
      })

      if (!response.ok) {
        throw new Error('Failed to save page')
      }

      setDirty(false)
      showSuccess('Page Saved', 'Your page has been saved successfully!')

    } catch (error) {
      console.error('Error saving page:', error)
      showError('Save Failed', 'Failed to save page. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  // Publish page functionality
  const handlePublishPage = async () => {
    if (!currentPage) {
      alert('No page selected to publish')
      return
    }

    setIsPublishing(true)
    try {
      // First save the current state
      if (isDirty) {
        await handleSavePage()
      }

      // Then publish the page
      const response = await fetch(`/api/page-builder/pages/${currentPage.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isPublished: true
        })
      })

      if (!response.ok) {
        throw new Error('Failed to publish page')
      }

      showSuccess('Page Published', `"${currentPage.title}" has been published successfully!`)

      // Open preview in new tab
      window.open(`/preview/${currentPage.slug}`, '_blank')

    } catch (error) {
      console.error('Error publishing page:', error)
      showError('Publish Failed', 'Failed to publish page. Please try again.')
    } finally {
      setIsPublishing(false)
    }
  }

  // Test function to add a component
  const addTestComponent = () => {
    const testComponent = {
      id: `test-${Date.now()}`,
      type: 'text',
      name: 'Test Text Component',
      content: {
        text: 'Hello! This is a test component to verify the page builder is working.',
        tag: 'h2'
      },
      styles: {
        color: '#333333',
        fontSize: '24px',
        fontWeight: 'bold',
        textAlign: 'center',
        padding: '20px',
        backgroundColor: '#f0f9ff',
        border: '2px solid #3b82f6',
        borderRadius: '8px'
      },
      position: {
        x: 50,
        y: 50,
        width: 400,
        height: 100
      },
      order: 1,
      isVisible: true,
      isLocked: false
    }

    addComponent(testComponent)
  }
  
  return (
    <div className="h-screen flex flex-col bg-gray-50">
      <KeyboardShortcuts />

      {/* Page Manager Modal */}
      {showPageManager && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-900 bg-opacity-50 transition-opacity backdrop-blur-sm" onClick={() => setShowPageManager(false)} />

            <div className="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
              <div className="bg-white px-6 pt-6 pb-4 sm:p-8 sm:pb-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Page Manager</h3>
                  <button
                    onClick={() => setShowPageManager(false)}
                    className="text-gray-400 hover:text-gray-600 p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
                <PageManager onClose={() => setShowPageManager(false)} />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Data Binding Panel Modal */}
      {showDataBinding && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-900 bg-opacity-50 transition-opacity backdrop-blur-sm" onClick={() => setShowDataBinding(false)} />

            <div className="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-6 pt-6 pb-4 sm:p-8 sm:pb-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Data Binding</h3>
                  <button
                    onClick={() => setShowDataBinding(false)}
                    className="text-gray-400 hover:text-gray-600 p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
                <DataBindingPanel onClose={() => setShowDataBinding(false)} />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Templates Panel Modal */}
      {showTemplates && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-900 bg-opacity-50 transition-opacity backdrop-blur-sm" onClick={() => setShowTemplates(false)} />

            <div className="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
              <div className="bg-white px-6 pt-6 pb-4 sm:p-8 sm:pb-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Page Templates</h3>
                  <button
                    onClick={() => setShowTemplates(false)}
                    className="text-gray-400 hover:text-gray-600 p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
                <PageTemplates onClose={() => setShowTemplates(false)} />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Export Dialog */}
      {showExportDialog && (
        <ExportDialog onClose={() => setShowExportDialog(false)} />
      )}

      {/* Modern Top Toolbar */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Left Section */}
            <div className="flex items-center space-x-6">
              {/* Logo/Brand */}
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <PaintBrushIcon className="h-5 w-5 text-white" />
                </div>
                <h1 className="text-lg font-semibold text-gray-900">Page Builder</h1>
              </div>

              {/* Page Selector */}
              <button
                onClick={() => setShowPageManager(true)}
                className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 hover:border-gray-300 transition-colors"
                title="Manage Pages (Ctrl+Shift+M)"
              >
                <DocumentTextIcon className="h-4 w-4 mr-2 text-gray-500" />
                {currentPage ? (
                  <div className="flex items-center space-x-2">
                    <span>{currentPage.title}</span>
                    {isDirty && <span className="text-orange-500">*</span>}
                    <div className="flex items-center space-x-1">
                      {currentPage.isPublished ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Published
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Draft
                        </span>
                      )}
                      {currentPage.isHomePage && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Home
                        </span>
                      )}
                    </div>
                  </div>
                ) : (
                  'Select Page'
                )}
              </button>

              {/* Mode Toggle */}
              <div className="flex items-center bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setMode('edit')}
                  className={`px-4 py-2 text-sm font-medium rounded-md transition-all ${
                    mode === 'edit'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <PaintBrushIcon className="h-4 w-4 mr-2 inline" />
                  Edit
                </button>
                <button
                  onClick={() => setMode('preview')}
                  className={`px-4 py-2 text-sm font-medium rounded-md transition-all ${
                    mode === 'preview'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <EyeIcon className="h-4 w-4 mr-2 inline" />
                  Preview
                </button>
              </div>
            </div>

            {/* Center Section - Viewport Controls */}
            <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewport('desktop')}
                className={`p-3 rounded-md transition-all ${
                  viewport === 'desktop'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
                title="Desktop View"
              >
                <ComputerDesktopIcon className="h-5 w-5" />
              </button>
              <button
                onClick={() => setViewport('tablet')}
                className={`p-3 rounded-md transition-all ${
                  viewport === 'tablet'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
                title="Tablet View"
              >
                <DeviceTabletIcon className="h-5 w-5" />
              </button>
              <button
                onClick={() => setViewport('mobile')}
                className={`p-3 rounded-md transition-all ${
                  viewport === 'mobile'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
                title="Mobile View"
              >
                <DevicePhoneMobileIcon className="h-5 w-5" />
              </button>
            </div>

            {/* Right Section */}
            <div className="flex items-center space-x-3">
              {/* Undo/Redo */}
              <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={undo}
                  disabled={!canUndo}
                  className={`p-2 rounded-md transition-all ${
                    canUndo
                      ? 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      : 'text-gray-400 cursor-not-allowed'
                  }`}
                  title="Undo (Ctrl+Z)"
                >
                  <ArrowUturnLeftIcon className="h-4 w-4" />
                </button>

                <button
                  onClick={redo}
                  disabled={!canRedo}
                  className={`p-2 rounded-md transition-all ${
                    canRedo
                      ? 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      : 'text-gray-400 cursor-not-allowed'
                  }`}
                  title="Redo (Ctrl+Y)"
                >
                  <ArrowUturnRightIcon className="h-4 w-4" />
                </button>
              </div>

              {/* Action Buttons */}
              <button
                onClick={handleSavePage}
                disabled={!currentPage || !isDirty || isSaving}
                className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all ${
                  currentPage && isDirty && !isSaving
                    ? 'text-white bg-blue-600 hover:bg-blue-700 shadow-sm'
                    : 'text-gray-500 bg-gray-200 cursor-not-allowed'
                }`}
                title={currentPage ? 'Save Page (Ctrl+S)' : 'Select a page to save'}
              >
                <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                {isSaving ? 'Saving...' : 'Save'}
              </button>

              <button
                onClick={handlePublishPage}
                disabled={!currentPage || isPublishing}
                className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all ${
                  currentPage && !isPublishing
                    ? 'text-white bg-green-600 hover:bg-green-700 shadow-sm'
                    : 'text-gray-500 bg-gray-200 cursor-not-allowed'
                }`}
                title={currentPage ? 'Publish Page (Ctrl+Shift+P)' : 'Select a page to publish'}
              >
                {isPublishing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Publishing...
                  </>
                ) : (
                  <>
                    <ShareIcon className="h-4 w-4 mr-2" />
                    Publish
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Secondary Toolbar */}
        <div className="px-6 py-3 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {/* Left Panel Controls */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setActivePanel('components')}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all ${
                  activePanel === 'components'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Components Library"
              >
                <Bars3Icon className="h-4 w-4 mr-2" />
                Components
              </button>

              <button
                onClick={() => setActivePanel('templates')}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all ${
                  activePanel === 'templates'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Page Templates"
              >
                <RectangleStackIcon className="h-4 w-4 mr-2" />
                Templates
              </button>

              <button
                onClick={() => setActivePanel('data')}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all ${
                  activePanel === 'data'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Data Binding"
              >
                <CircleStackIcon className="h-4 w-4 mr-2" />
                Data
              </button>
            </div>

            {/* Right Panel Controls */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setActivePanel('properties')}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all ${
                  activePanel === 'properties'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Properties Panel"
              >
                <CogIcon className="h-4 w-4 mr-2" />
                Properties
              </button>

              <button
                onClick={toggleLeftPanel}
                className={`p-2 rounded-lg transition-all ${
                  leftPanelOpen
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Toggle Left Panel"
              >
                <Bars3Icon className="h-4 w-4" />
              </button>

              <button
                onClick={toggleRightPanel}
                className={`p-2 rounded-lg transition-all ${
                  rightPanelOpen
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Toggle Right Panel"
              >
                <Bars3Icon className="h-4 w-4 transform rotate-180" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left panel - Dynamic Content */}
        {leftPanelOpen && (
          <div
            className="bg-white border-r border-gray-200 flex-shrink-0 shadow-sm"
            style={{ width: leftPanelWidth }}
          >
            <div className="h-full flex flex-col">
              {/* Panel Header */}
              <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                <h3 className="text-sm font-semibold text-gray-900 capitalize">
                  {activePanel === 'components' && 'Components Library'}
                  {activePanel === 'templates' && 'Page Templates'}
                  {activePanel === 'data' && 'Data Binding'}
                </h3>
              </div>

              {/* Panel Content */}
              <div className="flex-1 overflow-hidden">
                {activePanel === 'components' && <ComponentLibrary />}
                {activePanel === 'templates' && (
                  <div className="p-4">
                    <button
                      onClick={() => setShowTemplates(true)}
                      className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                    >
                      <RectangleStackIcon className="h-5 w-5 mr-2" />
                      Browse Templates
                    </button>
                  </div>
                )}
                {activePanel === 'data' && (
                  <div className="p-4">
                    <button
                      onClick={() => setShowDataBinding(true)}
                      className="w-full flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium"
                    >
                      <CircleStackIcon className="h-5 w-5 mr-2" />
                      Bind Data
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Center panel - Canvas */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Enhanced Toolbar */}
          {mode === 'edit' && (
            <div className="bg-white border-b border-gray-200 px-4 py-2">
              <Toolbar />
            </div>
          )}

          {/* Canvas container */}
          <div className="flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-gray-100 p-6">
            <div className="max-w-full mx-auto">
              {currentPage ? (
                <div className="space-y-4">
                  {/* Page Info Bar */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{currentPage.title}</h3>
                          <p className="text-sm text-gray-500">/{currentPage.slug}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-600">Components: {components.length}</span>
                          <span className="text-gray-300">•</span>
                          <span className="text-sm text-gray-600">
                            Mode: <span className="font-medium capitalize">{mode}</span>
                          </span>
                          <span className="text-gray-300">•</span>
                          <span className="text-sm text-gray-600">
                            Viewport: <span className="font-medium capitalize">{viewport}</span>
                          </span>
                        </div>
                      </div>
                      <button
                        onClick={addTestComponent}
                        className="flex items-center px-3 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
                      >
                        <PlayIcon className="h-4 w-4 mr-2" />
                        Add Test Component
                      </button>
                    </div>
                  </div>

                  {/* Canvas */}
                  <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                    <Canvas className="min-h-[600px]" />
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full min-h-[600px]">
                  <div className="text-center max-w-lg bg-white rounded-xl shadow-lg border border-gray-200 p-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                      <PaintBrushIcon className="h-8 w-8 text-white" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Welcome to Page Builder</h2>
                    <p className="text-gray-600 mb-8">
                      Create stunning web pages with our intuitive drag-and-drop interface.
                      Get started by selecting an existing page or creating a new one.
                    </p>
                    <div className="space-y-4">
                      <button
                        onClick={() => setShowPageManager(true)}
                        className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium transition-colors"
                      >
                        <DocumentTextIcon className="h-5 w-5 mr-2" />
                        Manage Pages
                      </button>
                      <button
                        onClick={() => setShowTemplates(true)}
                        className="w-full flex items-center justify-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium transition-colors"
                      >
                        <RectangleStackIcon className="h-5 w-5 mr-2" />
                        Browse Templates
                      </button>
                      <div className="text-sm text-gray-500">
                        Or use keyboard shortcut: <kbd className="px-2 py-1 bg-gray-100 rounded text-xs font-mono">Ctrl+Shift+M</kbd>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right panel - Properties */}
        {rightPanelOpen && (
          <div
            className="bg-white border-l border-gray-200 flex-shrink-0 shadow-sm"
            style={{ width: rightPanelWidth }}
          >
            <div className="h-full flex flex-col">
              {/* Panel Header */}
              <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                <h3 className="text-sm font-semibold text-gray-900">Properties</h3>
              </div>

              {/* Panel Content */}
              <div className="flex-1 overflow-hidden">
                <PropertyPanel />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default PageBuilderPage
