// Notification utility for page builder
export type NotificationType = 'success' | 'error' | 'warning' | 'info'

export interface NotificationOptions {
  type: NotificationType
  title: string
  message?: string
  duration?: number
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
}

export const showNotification = (options: NotificationOptions) => {
  const {
    type,
    title,
    message,
    duration = 3000,
    position = 'top-right'
  } = options

  // Create notification element
  const notification = document.createElement('div')
  notification.className = `
    fixed z-50 max-w-sm w-full bg-white border border-gray-200 rounded-lg shadow-lg pointer-events-auto
    ${getPositionClasses(position)}
  `

  // Set notification content
  notification.innerHTML = `
    <div class="flex p-4">
      <div class="flex-shrink-0">
        ${getIcon(type)}
      </div>
      <div class="ml-3 w-0 flex-1">
        <p class="text-sm font-medium text-gray-900">
          ${title}
        </p>
        ${message ? `
          <p class="mt-1 text-sm text-gray-500">
            ${message}
          </p>
        ` : ''}
      </div>
      <div class="ml-4 flex-shrink-0 flex">
        <button class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" onclick="this.parentElement.parentElement.parentElement.remove()">
          <span class="sr-only">Close</span>
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  `

  // Add to DOM
  document.body.appendChild(notification)

  // Auto remove after duration
  if (duration > 0) {
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove()
      }
    }, duration)
  }

  return notification
}

const getPositionClasses = (position: string): string => {
  switch (position) {
    case 'top-right':
      return 'top-4 right-4'
    case 'top-left':
      return 'top-4 left-4'
    case 'bottom-right':
      return 'bottom-4 right-4'
    case 'bottom-left':
      return 'bottom-4 left-4'
    default:
      return 'top-4 right-4'
  }
}

const getIcon = (type: NotificationType): string => {
  switch (type) {
    case 'success':
      return `
        <svg class="h-6 w-6 text-green-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      `
    case 'error':
      return `
        <svg class="h-6 w-6 text-red-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      `
    case 'warning':
      return `
        <svg class="h-6 w-6 text-yellow-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      `
    case 'info':
      return `
        <svg class="h-6 w-6 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      `
    default:
      return ''
  }
}

// Convenience functions
export const showSuccess = (title: string, message?: string) => {
  showNotification({ type: 'success', title, message })
}

export const showError = (title: string, message?: string) => {
  showNotification({ type: 'error', title, message })
}

export const showWarning = (title: string, message?: string) => {
  showNotification({ type: 'warning', title, message })
}

export const showInfo = (title: string, message?: string) => {
  showNotification({ type: 'info', title, message })
}
