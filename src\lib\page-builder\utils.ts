import { Component, ComponentType } from './store'
import { componentRegistry } from './component-registry'

// Grid utilities
export const snapToGrid = (value: number, gridSize: number): number => {
  return Math.round(value / gridSize) * gridSize
}

export const snapPositionToGrid = (
  position: { x: number; y: number },
  gridSize: number
): { x: number; y: number } => {
  return {
    x: snapToGrid(position.x, gridSize),
    y: snapToGrid(position.y, gridSize)
  }
}

export const snapSizeToGrid = (
  size: { width: number; height: number },
  gridSize: number
): { width: number; height: number } => {
  return {
    width: Math.max(gridSize, snapToGrid(size.width, gridSize)),
    height: Math.max(gridSize, snapToGrid(size.height, gridSize))
  }
}

// Component utilities
export const generateComponentId = (): string => {
  return `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

export const isValidParentChild = (parentType: ComponentType, childType: ComponentType): boolean => {
  const parentConfig = componentRegistry[parentType]
  const childConfig = componentRegistry[childType]
  
  // Check if parent can have children
  if (!parentConfig.hasChildren) {
    return false
  }
  
  // Check if parent has restrictions on allowed children
  if (parentConfig.allowedChildren && !parentConfig.allowedChildren.includes(childType)) {
    return false
  }
  
  // Check if child has restrictions on allowed parents
  if (childConfig.allowedParents && !childConfig.allowedParents.includes(parentType)) {
    return false
  }
  
  return true
}

export const canDropComponent = (
  draggedComponent: Component,
  targetComponent: Component | null,
  components: Component[]
): boolean => {
  // Can't drop on itself
  if (targetComponent && draggedComponent.id === targetComponent.id) {
    return false
  }
  
  // Can't drop on its own children
  if (targetComponent && isDescendant(draggedComponent.id, targetComponent.id, components)) {
    return false
  }
  
  // If no target, can drop on canvas
  if (!targetComponent) {
    return true
  }
  
  // Check parent-child compatibility
  return isValidParentChild(targetComponent.type, draggedComponent.type)
}

export const isDescendant = (ancestorId: string, descendantId: string, components: Component[]): boolean => {
  const descendant = components.find(c => c.id === descendantId)
  if (!descendant || !descendant.parentId) {
    return false
  }
  
  if (descendant.parentId === ancestorId) {
    return true
  }
  
  return isDescendant(ancestorId, descendant.parentId, components)
}

// Layout utilities
export const getComponentBounds = (component: Component) => {
  return {
    left: component.position.x,
    top: component.position.y,
    right: component.position.x + component.position.width,
    bottom: component.position.y + component.position.height,
    width: component.position.width,
    height: component.position.height
  }
}

export const getComponentCenter = (component: Component) => {
  return {
    x: component.position.x + component.position.width / 2,
    y: component.position.y + component.position.height / 2
  }
}

export const isPointInComponent = (point: { x: number; y: number }, component: Component): boolean => {
  const bounds = getComponentBounds(component)
  return point.x >= bounds.left && 
         point.x <= bounds.right && 
         point.y >= bounds.top && 
         point.y <= bounds.bottom
}

export const getComponentsAtPoint = (point: { x: number; y: number }, components: Component[]): Component[] => {
  return components.filter(component => 
    component.isVisible && isPointInComponent(point, component)
  ).sort((a, b) => b.order - a.order) // Sort by order (higher order = on top)
}

export const getComponentsInArea = (
  area: { x: number; y: number; width: number; height: number },
  components: Component[]
): Component[] => {
  return components.filter(component => {
    if (!component.isVisible) return false
    
    const bounds = getComponentBounds(component)
    return !(bounds.right < area.x || 
             bounds.left > area.x + area.width ||
             bounds.bottom < area.y || 
             bounds.top > area.y + area.height)
  })
}

// Alignment utilities
export const alignComponents = (components: Component[], alignment: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom'): Component[] => {
  if (components.length < 2) return components
  
  const bounds = components.map(getComponentBounds)
  
  switch (alignment) {
    case 'left': {
      const leftmost = Math.min(...bounds.map(b => b.left))
      return components.map(component => ({
        ...component,
        position: { ...component.position, x: leftmost }
      }))
    }
    case 'center': {
      const leftmost = Math.min(...bounds.map(b => b.left))
      const rightmost = Math.max(...bounds.map(b => b.right))
      const centerX = (leftmost + rightmost) / 2
      return components.map(component => ({
        ...component,
        position: { ...component.position, x: centerX - component.position.width / 2 }
      }))
    }
    case 'right': {
      const rightmost = Math.max(...bounds.map(b => b.right))
      return components.map(component => ({
        ...component,
        position: { ...component.position, x: rightmost - component.position.width }
      }))
    }
    case 'top': {
      const topmost = Math.min(...bounds.map(b => b.top))
      return components.map(component => ({
        ...component,
        position: { ...component.position, y: topmost }
      }))
    }
    case 'middle': {
      const topmost = Math.min(...bounds.map(b => b.top))
      const bottommost = Math.max(...bounds.map(b => b.bottom))
      const centerY = (topmost + bottommost) / 2
      return components.map(component => ({
        ...component,
        position: { ...component.position, y: centerY - component.position.height / 2 }
      }))
    }
    case 'bottom': {
      const bottommost = Math.max(...bounds.map(b => b.bottom))
      return components.map(component => ({
        ...component,
        position: { ...component.position, y: bottommost - component.position.height }
      }))
    }
    default:
      return components
  }
}

export const distributeComponents = (components: Component[], direction: 'horizontal' | 'vertical'): Component[] => {
  if (components.length < 3) return components
  
  const sorted = [...components].sort((a, b) => 
    direction === 'horizontal' ? a.position.x - b.position.x : a.position.y - b.position.y
  )
  
  const first = sorted[0]
  const last = sorted[sorted.length - 1]
  
  if (direction === 'horizontal') {
    const totalWidth = last.position.x + last.position.width - first.position.x
    const availableSpace = totalWidth - sorted.reduce((sum, c) => sum + c.position.width, 0)
    const spacing = availableSpace / (sorted.length - 1)
    
    let currentX = first.position.x
    return sorted.map(component => {
      const newComponent = { ...component, position: { ...component.position, x: currentX } }
      currentX += component.position.width + spacing
      return newComponent
    })
  } else {
    const totalHeight = last.position.y + last.position.height - first.position.y
    const availableSpace = totalHeight - sorted.reduce((sum, c) => sum + c.position.height, 0)
    const spacing = availableSpace / (sorted.length - 1)
    
    let currentY = first.position.y
    return sorted.map(component => {
      const newComponent = { ...component, position: { ...component.position, y: currentY } }
      currentY += component.position.height + spacing
      return newComponent
    })
  }
}

// Style utilities
export const convertStyleToCSS = (styles: Record<string, any>): React.CSSProperties => {
  const cssStyles: React.CSSProperties = {}
  
  Object.entries(styles).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      // Convert camelCase to kebab-case for CSS custom properties
      const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase()
      cssStyles[key as keyof React.CSSProperties] = value
    }
  })
  
  return cssStyles
}

export const mergeStyles = (...styleObjects: Record<string, any>[]): Record<string, any> => {
  return styleObjects.reduce((merged, styles) => ({ ...merged, ...styles }), {})
}

// Responsive utilities
export const getResponsiveValue = (
  value: any,
  viewport: 'desktop' | 'tablet' | 'mobile'
): any => {
  if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
    return value[viewport] || value.desktop || value
  }
  return value
}

export const setResponsiveValue = (
  currentValue: any,
  newValue: any,
  viewport: 'desktop' | 'tablet' | 'mobile'
): any => {
  if (viewport === 'desktop') {
    return newValue
  }
  
  const responsiveValue = typeof currentValue === 'object' && currentValue !== null && !Array.isArray(currentValue)
    ? { ...currentValue }
    : { desktop: currentValue }
  
  responsiveValue[viewport] = newValue
  return responsiveValue
}

// Export utilities
export const exportToJSON = (components: Component[]): string => {
  return JSON.stringify(components, null, 2)
}

export const importFromJSON = (json: string): Component[] => {
  try {
    const parsed = JSON.parse(json)
    if (Array.isArray(parsed)) {
      return parsed.filter(item => 
        item && 
        typeof item === 'object' && 
        item.id && 
        item.type && 
        item.position
      )
    }
    return []
  } catch (error) {
    console.error('Failed to import JSON:', error)
    return []
  }
}

// Validation utilities
export const validateComponent = (component: Partial<Component>): string[] => {
  const errors: string[] = []
  
  if (!component.id) {
    errors.push('Component must have an ID')
  }
  
  if (!component.type) {
    errors.push('Component must have a type')
  }
  
  if (!component.position) {
    errors.push('Component must have position data')
  } else {
    if (typeof component.position.x !== 'number' || component.position.x < 0) {
      errors.push('Component X position must be a non-negative number')
    }
    if (typeof component.position.y !== 'number' || component.position.y < 0) {
      errors.push('Component Y position must be a non-negative number')
    }
    if (typeof component.position.width !== 'number' || component.position.width <= 0) {
      errors.push('Component width must be a positive number')
    }
    if (typeof component.position.height !== 'number' || component.position.height <= 0) {
      errors.push('Component height must be a positive number')
    }
  }
  
  return errors
}

// Keyboard shortcuts
export const handleKeyboardShortcut = (
  event: KeyboardEvent,
  selectedComponents: Component[],
  actions: {
    copy: () => void
    paste: () => void
    delete: () => void
    undo: () => void
    redo: () => void
    selectAll: () => void
    duplicate: () => void
  }
) => {
  const { ctrlKey, metaKey, key } = event
  const isModifierPressed = ctrlKey || metaKey
  
  if (!isModifierPressed) {
    switch (key) {
      case 'Delete':
      case 'Backspace':
        if (selectedComponents.length > 0) {
          actions.delete()
          event.preventDefault()
        }
        break
    }
    return
  }
  
  switch (key.toLowerCase()) {
    case 'c':
      if (selectedComponents.length > 0) {
        actions.copy()
        event.preventDefault()
      }
      break
    case 'v':
      actions.paste()
      event.preventDefault()
      break
    case 'z':
      if (event.shiftKey) {
        actions.redo()
      } else {
        actions.undo()
      }
      event.preventDefault()
      break
    case 'y':
      actions.redo()
      event.preventDefault()
      break
    case 'a':
      actions.selectAll()
      event.preventDefault()
      break
    case 'd':
      if (selectedComponents.length > 0) {
        actions.duplicate()
        event.preventDefault()
      }
      break
  }
}
