'use client'

import { useState, useEffect } from 'react'

export interface Service {
  id: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category: {
    id: string
    name: string
    description?: string
  }
  serviceOptions: ServiceOption[]
  projects?: Project[]
  _count: {
    projects: number
    orderDetails: number
    serviceOptions: number
  }
}

export interface ServiceOption {
  id: string
  name: string
  description?: string
  price?: number
  isActive: boolean
  features: ServiceOptionFeature[]
}

export interface ServiceOptionFeature {
  id: string
  name: string
  description?: string
  isIncluded: boolean
}

export interface Project {
  id: string
  name: string
  description?: string
  status: string
  imageUrl?: string
  projectUrl?: string
  githubUrl?: string
  client: {
    id: string
    companyName: string
  }
}

export interface Testimonial {
  id: string
  clientName: string
  clientTitle?: string
  clientCompany?: string
  content: string
  rating: number
}

export interface ServicesResponse {
  success: boolean
  data: Service[]
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface ServiceDetailResponse {
  success: boolean
  data: {
    service: Service
    relatedServices: Service[]
    testimonials: Testimonial[]
  }
}

// Hook for fetching multiple services
export function useServices(params?: {
  page?: number
  limit?: number
  search?: string
  categoryId?: string
}) {
  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<any>(null)

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true)
        setError(null)

        const searchParams = new URLSearchParams()
        if (params?.page) searchParams.set('page', params.page.toString())
        if (params?.limit) searchParams.set('limit', params.limit.toString())
        if (params?.search) searchParams.set('search', params.search)
        if (params?.categoryId) searchParams.set('categoryId', params.categoryId)

        const response = await fetch(`/api/services?${searchParams.toString()}`)
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result: ServicesResponse = await response.json()
        
        if (result.success) {
          setServices(result.data)
          setPagination(result.pagination)
        } else {
          throw new Error('Failed to fetch services')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        setServices([])
      } finally {
        setLoading(false)
      }
    }

    fetchServices()
  }, [params?.page, params?.limit, params?.search, params?.categoryId])

  return { services, loading, error, pagination }
}

// Hook for fetching a single service by slug
export function useServiceBySlug(slug: string) {
  const [service, setService] = useState<Service | null>(null)
  const [relatedServices, setRelatedServices] = useState<Service[]>([])
  const [testimonials, setTestimonials] = useState<Testimonial[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!slug) return

    const fetchService = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/services/slug/${slug}`)
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result: ServiceDetailResponse = await response.json()
        
        if (result.success) {
          setService(result.data.service)
          setRelatedServices(result.data.relatedServices)
          setTestimonials(result.data.testimonials)
        } else {
          throw new Error('Failed to fetch service')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        setService(null)
        setRelatedServices([])
        setTestimonials([])
      } finally {
        setLoading(false)
      }
    }

    fetchService()
  }, [slug])

  return { service, relatedServices, testimonials, loading, error }
}

// Hook for fetching a single service by ID
export function useService(id: string) {
  const [service, setService] = useState<Service | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!id) return

    const fetchService = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/services/${id}`)
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        
        if (result.success) {
          setService(result.data)
        } else {
          throw new Error('Failed to fetch service')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        setService(null)
      } finally {
        setLoading(false)
      }
    }

    fetchService()
  }, [id])

  return { service, loading, error }
}
