import { NextRequest, NextResponse } from 'next/server'
import { ZodError, ZodSchema } from 'zod'
import { Prisma } from '@prisma/client'

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T = any> extends ApiResponse<T> {
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Error handling
export class ApiError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// Success response helper
export function successResponse<T>(
  data: T,
  message?: string,
  statusCode: number = 200
): NextResponse<ApiResponse<T>> {
  // Serialize BigInt values in the data
  const serializedData = serializeBigInt(data)

  return NextResponse.json(
    {
      success: true,
      data: serializedData,
      message,
    },
    { status: statusCode }
  )
}

// Error response helper
export function errorResponse(
  error: string | Error,
  statusCode: number = 500,
  code?: string
): NextResponse<ApiResponse> {
  const message = error instanceof Error ? error.message : error
  
  return NextResponse.json(
    {
      success: false,
      error: message,
      code,
    },
    { status: statusCode }
  )
}

// Paginated response helper
// Helper function to serialize BigInt values
function serializeBigInt(obj: any): any {
  if (obj === null || obj === undefined) return obj

  if (typeof obj === 'bigint') {
    return obj.toString()
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeBigInt)
  }

  if (typeof obj === 'object') {
    const serialized: any = {}
    for (const [key, value] of Object.entries(obj)) {
      serialized[key] = serializeBigInt(value)
    }
    return serialized
  }

  return obj
}

export function paginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
  message?: string
): NextResponse<PaginatedResponse<T[]>> {
  const totalPages = Math.ceil(total / limit)

  // Serialize BigInt values in the data
  const serializedData = serializeBigInt(data)

  return NextResponse.json({
    success: true,
    data: serializedData,
    message,
    pagination: {
      page,
      limit,
      total,
      totalPages,
    },
  })
}

// Validation middleware
export function validateRequest<T>(schema: ZodSchema<T>) {
  return async (request: NextRequest): Promise<T> => {
    try {
      const body = await request.json()
      return schema.parse(body)
    } catch (error) {
      if (error instanceof ZodError) {
        const errorMessages = error.errors.map(err => 
          `${err.path.join('.')}: ${err.message}`
        ).join(', ')
        throw new ApiError(`Validation error: ${errorMessages}`, 400, 'VALIDATION_ERROR')
      }
      throw new ApiError('Invalid request body', 400, 'INVALID_BODY')
    }
  }
}

// Query parameter helpers
export function getQueryParams(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  
  return {
    page: parseInt(searchParams.get('page') || '1'),
    limit: Math.min(parseInt(searchParams.get('limit') || '10'), 100), // Max 100 items per page
    search: searchParams.get('search') || undefined,
    sortBy: searchParams.get('sortBy') || undefined,
    sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
    filter: searchParams.get('filter') || undefined,
  }
}

// Pagination helpers
export function getPaginationParams(page: number, limit: number) {
  const skip = (page - 1) * limit
  return { skip, take: limit }
}

// Error handler wrapper for API routes
export function withErrorHandler(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      return await handler(request, context)
    } catch (error) {
      console.error('API Error:', error)
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.statusCode, error.code)
      }
      
      if (error instanceof ZodError) {
        const errorMessages = error.errors.map(err => 
          `${err.path.join('.')}: ${err.message}`
        ).join(', ')
        return errorResponse(`Validation error: ${errorMessages}`, 400, 'VALIDATION_ERROR')
      }
      
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            return errorResponse('A record with this data already exists', 409, 'DUPLICATE_RECORD')
          case 'P2025':
            return errorResponse('Record not found', 404, 'NOT_FOUND')
          case 'P2003':
            return errorResponse('Foreign key constraint failed', 400, 'FOREIGN_KEY_ERROR')
          default:
            return errorResponse('Database error occurred', 500, 'DATABASE_ERROR')
        }
      }
      
      return errorResponse('Internal server error', 500, 'INTERNAL_ERROR')
    }
  }
}

// Method validation helper
export function validateMethod(request: NextRequest, allowedMethods: string[]) {
  if (!allowedMethods.includes(request.method)) {
    throw new ApiError(`Method ${request.method} not allowed`, 405, 'METHOD_NOT_ALLOWED')
  }
}

// Authentication helper using NextAuth
export async function requireAuth(request: NextRequest) {
  const { getServerSession } = await import('next-auth/next')
  const { authOptions } = await import('./auth')

  const session = await getServerSession(authOptions)

  if (!session || !session.user) {
    throw new ApiError('Authentication required', 401, 'UNAUTHORIZED')
  }

  return {
    id: session.user.id,
    email: session.user.email!,
    role: session.user.role as 'ADMIN' | 'USER' | 'CLIENT',
    name: session.user.name,
  }
}

// Admin authorization helper
export async function requireAdmin(request: NextRequest) {
  const user = await requireAuth(request)

  if (user.role !== 'ADMIN') {
    throw new ApiError('Admin access required', 403, 'FORBIDDEN')
  }

  return user
}

// Slug generation helper
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim()
}

// File upload helpers
export function validateFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type)
}

export function validateFileSize(file: File, maxSizeInBytes: number): boolean {
  return file.size <= maxSizeInBytes
}

// Search helpers
export function buildSearchQuery(searchTerm: string | undefined, fields: string[]): any {
  if (!searchTerm) return {}

  // For case-insensitive search, we'll use the original search term
  // Prisma will handle case sensitivity based on the database collation
  return {
    OR: fields.map(field => ({
      [field]: {
        contains: searchTerm,
        mode: 'insensitive' // This works with PostgreSQL and MySQL, for SQLite it's ignored but still works
      },
    })),
  }
}

// Sort helpers
export function buildSortQuery(sortBy?: string, sortOrder: 'asc' | 'desc' = 'desc') {
  if (!sortBy) return { createdat: sortOrder }

  return { [sortBy]: sortOrder }
}
