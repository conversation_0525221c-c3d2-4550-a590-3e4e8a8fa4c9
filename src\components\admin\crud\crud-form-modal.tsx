'use client'

import React from 'react'
import { AnimatePresence } from 'framer-motion'
import { useCrud } from './crud-context'
import { CrudForm } from './crud-form'
import { CrudConfig } from './types'

interface CrudFormModalProps<T> {
  config: CrudConfig<T>
}

export function CrudFormModal<T>({ config }: CrudFormModalProps<T>) {
  const { state, actions } = useCrud<T>()

  const handleCreateSubmit = async (data: any) => {
    try {
      await actions.createItem(data)
      actions.closeCreateModal()
    } catch (error) {
      console.error('Create error:', error)
      throw error
    }
  }

  const handleEditSubmit = async (data: any) => {
    try {
      if (state.editingItem && (state.editingItem as any).id) {
        await actions.updateItem((state.editingItem as any).id, data)
        actions.closeEditModal()
      }
    } catch (error) {
      console.error('Update error:', error)
      throw error
    }
  }

  return (
    <AnimatePresence>
      {state.isCreateModalOpen && (
        <CrudForm
          fields={config.fields}
          onSubmit={handleCreateSubmit}
          onCancel={actions.closeCreateModal}
          title={`Create ${config.title.slice(0, -1)}`} // Remove 's' from plural
          submitLabel="Create"
          isLoading={state.loading}
        />
      )}
      
      {state.isEditModalOpen && state.editingItem && (
        <CrudForm
          fields={config.fields}
          initialData={state.editingItem}
          onSubmit={handleEditSubmit}
          onCancel={actions.closeEditModal}
          title={`Edit ${config.title.slice(0, -1)}`} // Remove 's' from plural
          submitLabel="Update"
          isLoading={state.loading}
        />
      )}
    </AnimatePresence>
  )
}
