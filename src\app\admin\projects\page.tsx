'use client';

import { ProjectsManager } from '@/components/admin/projects/projects-manager';
import { CrudConfig } from '@/components/admin/crud/types';

interface Project {
  id: string
  name: string
  description: string
  projgoals?: string
  projmanager?: string
  clientid?: string
  orderid: string
  imageurl?: string
  projecturl?: string
  githuburl?: string
  tags?: string
  projstartdate?: string
  projcompletiondate?: string
  estimatecost?: number
  estimatetime?: string
  estimateeffort?: string
  status?: string
  isfeatured?: boolean
  ispublic?: boolean
  displayorder: number
  createdat: string
  updatedat?: string
  clients?: {
    id: string
    companyname: string
    contactname: string
  }
  teammembers?: {
    id: string
    name: string
  }
  _count?: {
    tasks: number
    projectdocuments: number
    messages: number
  }
  [key: string]: any
}

const projectConfig: CrudConfig<Project> = {
  title: 'Projects',
  description: 'Manage your projects, track progress, and monitor budgets',
  endpoint: 'projects', // API endpoint

  columns: [
    {
      key: 'name',
      label: 'Project Name',
      sortable: true,
      searchable: true,
      renderType: 'text',
      width: '250px',
      hideable: false, // Always visible
      defaultVisible: true
    },
    {
      key: 'client',
      label: 'Client',
      sortable: true,
      searchable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      renderType: 'status',
      renderProps: {
        statusColors: {
          'PLANNING': 'bg-yellow-100 text-yellow-800',
          'IN_PROGRESS': 'bg-blue-100 text-blue-800',
          'COMPLETED': 'bg-green-100 text-green-800',
          'ON_HOLD': 'bg-gray-100 text-gray-800',
          'CANCELLED': 'bg-red-100 text-red-800'
        }
      },
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'budget',
      label: 'Budget',
      sortable: true,
      renderType: 'currency',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'timeline',
      label: 'Timeline',
      sortable: true,
      renderType: 'date',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'flags',
      label: 'Flags',
      sortable: false,
      renderType: 'custom',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'manager',
      label: 'Manager',
      sortable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: false
    },
    {
      key: 'updatedat',
      label: 'Last Updated',
      sortable: true,
      renderType: 'date',
      hideable: true,
      defaultVisible: true
    }
  ],

  // Action buttons for each row
  actions: [
    {
      action: 'view',
      label: 'View',
      icon: 'EyeIcon',
      variant: 'secondary',
      tooltip: 'View project details'
    },
    {
      action: 'edit',
      label: 'Edit',
      icon: 'PencilIcon',
      variant: 'primary',
      tooltip: 'Edit project'
    },
    {
      action: 'toggle-status',
      label: 'Toggle Visibility',
      icon: 'PowerIcon',
      variant: 'warning',
      tooltip: 'Make public/private'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      tooltip: 'Delete project'
    }
  ],

  fields: [
    {
      key: 'name',
      label: 'Project Name',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'Enter project name'
    },
    {
      key: 'description',
      label: 'Description',
      type: 'textarea',
      required: true,
      searchable: true,
      placeholder: 'Enter project description',
      rows: 3
    },
    {
      key: 'projgoals',
      label: 'Project Goals',
      type: 'textarea',
      searchable: true,
      placeholder: 'Enter project goals and objectives',
      rows: 2
    },
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'PLANNING', label: 'Planning' },
        { value: 'IN_PROGRESS', label: 'In Progress' },
        { value: 'COMPLETED', label: 'Completed' },
        { value: 'ON_HOLD', label: 'On Hold' },
        { value: 'CANCELLED', label: 'Cancelled' },
      ],
      defaultValue: 'PLANNING',
      searchable: false,
    },
    {
      key: 'estimatecost',
      label: 'Estimated Cost',
      type: 'number',
      searchable: false,
      placeholder: '0.00'
    },
    {
      key: 'estimatetime',
      label: 'Estimated Time',
      type: 'text',
      searchable: false,
      placeholder: 'e.g., 3 months'
    },
    {
      key: 'estimateeffort',
      label: 'Estimated Effort',
      type: 'text',
      searchable: false,
      placeholder: 'e.g., 200 hours'
    },
    {
      key: 'projstartdate',
      label: 'Start Date',
      type: 'date',
      searchable: false,
    },
    {
      key: 'projcompletiondate',
      label: 'Completion Date',
      type: 'date',
      searchable: false,
    },
    {
      key: 'imageurl',
      label: 'Project Image',
      type: 'url',
      searchable: false,
      placeholder: 'https://example.com/image.jpg'
    },
    {
      key: 'projecturl',
      label: 'Project URL',
      type: 'url',
      searchable: false,
      placeholder: 'https://project.com'
    },
    {
      key: 'githuburl',
      label: 'GitHub URL',
      type: 'url',
      searchable: false,
      placeholder: 'https://github.com/user/repo'
    },
    {
      key: 'tags',
      label: 'Tags',
      type: 'text',
      searchable: true,
      placeholder: 'React, TypeScript, Node.js'
    },
    {
      key: 'orderid',
      label: 'Order ID',
      type: 'text',
      required: true,
      searchable: false,
      placeholder: 'Enter order ID'
    },
    {
      key: 'displayorder',
      label: 'Display Order',
      type: 'number',
      defaultValue: 0,
      searchable: false,
      placeholder: '0'
    },
    {
      key: 'ispublic',
      label: 'Public Project',
      type: 'boolean',
      defaultValue: true,
      searchable: false,
    },
    {
      key: 'isfeatured',
      label: 'Featured Project',
      type: 'boolean',
      defaultValue: false,
      searchable: false,
    },
  ],

  filters: [
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: '', label: 'All Statuses' },
        { value: 'PLANNING', label: 'Planning' },
        { value: 'IN_PROGRESS', label: 'In Progress' },
        { value: 'COMPLETED', label: 'Completed' },
        { value: 'ON_HOLD', label: 'On Hold' },
        { value: 'CANCELLED', label: 'Cancelled' },
      ],
    },
    {
      key: 'ispublic',
      label: 'Visibility',
      type: 'select',
      options: [
        { value: '', label: 'All' },
        { value: 'true', label: 'Public' },
        { value: 'false', label: 'Private' },
      ],
    },
  ],

  bulkActions: [
    {
      label: 'Make Public',
      action: 'make-public',
      variant: 'success'
    },
    {
      label: 'Make Private',
      action: 'make-private',
      variant: 'warning'
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search projects by name, description, client...',
  defaultSort: { field: 'updatedat', direction: 'desc' }, // Sort by Last Updated (most recent)
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['name', 'client', 'status', 'budget', 'timeline', 'flags', 'updatedat']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 3,
    sections: [
      {
        title: 'Basic Information',
        fields: ['name', 'description', 'projgoals', 'status']
      },
      {
        title: 'Project Details',
        fields: ['estimatecost', 'estimatetime', 'estimateeffort']
      },
      {
        title: 'Timeline',
        fields: ['projstartdate', 'projcompletiondate']
      },
      {
        title: 'URLs and Media',
        fields: ['imageurl', 'projecturl', 'githuburl']
      },
      {
        title: 'Additional Information',
        fields: ['tags', 'orderid', 'displayorder', 'ispublic', 'isfeatured']
      }
    ]
  }
};

export default function ProjectsPage() {
  return <ProjectsManager config={projectConfig} />;
}

