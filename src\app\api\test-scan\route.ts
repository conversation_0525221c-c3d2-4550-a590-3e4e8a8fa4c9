import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

// Simple test endpoint to verify scanning works
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing page scan functionality...')
    
    const appPath = 'src/app'
    const results = {
      appPathExists: false,
      foundDirectories: [] as string[],
      foundPageFiles: [] as string[],
      errors: [] as string[]
    }

    // Check if app directory exists
    try {
      results.appPathExists = fs.existsSync(appPath)
      console.log(`📁 App path exists: ${results.appPathExists}`)
    } catch (error) {
      results.errors.push(`Failed to check app path: ${error}`)
    }

    if (results.appPathExists) {
      try {
        // List directories in app
        const items = fs.readdirSync(appPath, { withFileTypes: true })
        
        for (const item of items) {
          if (item.isDirectory()) {
            results.foundDirectories.push(item.name)
            
            // Check for page.tsx files
            const dirPath = path.join(appPath, item.name)
            try {
              const dirItems = fs.readdirSync(dirPath, { withFileTypes: true })
              for (const dirItem of dirItems) {
                if (dirItem.name === 'page.tsx') {
                  results.foundPageFiles.push(`${item.name}/page.tsx`)
                }
              }
            } catch (dirError) {
              results.errors.push(`Failed to read directory ${item.name}: ${dirError}`)
            }
          } else if (item.name === 'page.tsx') {
            results.foundPageFiles.push('page.tsx')
          }
        }
        
        console.log(`📂 Found directories: ${results.foundDirectories.join(', ')}`)
        console.log(`📄 Found page files: ${results.foundPageFiles.join(', ')}`)
        
      } catch (error) {
        results.errors.push(`Failed to read app directory: ${error}`)
      }
    }

    // Test reading a specific page file
    if (results.foundPageFiles.length > 0) {
      try {
        const firstPagePath = path.join(appPath, results.foundPageFiles[0])
        const content = fs.readFileSync(firstPagePath, 'utf-8')
        console.log(`📖 Successfully read ${results.foundPageFiles[0]} (${content.length} characters)`)
      } catch (error) {
        results.errors.push(`Failed to read page file: ${error}`)
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Scan test completed',
      results,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Test scan failed:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Test scan failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
