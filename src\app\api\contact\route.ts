import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  validateMethod,
  requireAdmin
} from '@/lib/api-utils'
import { createContactFormSchema, updateContactFormSchema } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/contact - List all contact form submissions with pagination and search (admin only)
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search) {
    Object.assign(where, buildSearchQuery(search, [
      'name', 
      'email', 
      'company', 
      'subject', 
      'message'
    ]))
  }
  
  // Add filter for contact status
  if (filter && ['NEW', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'].includes(filter)) {
    where.status = filter
  }

  // Get total count for pagination
  const total = await prisma.contactforms.count({ where })

  // Get contact forms with pagination
  const contactForms = await prisma.contactforms.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          firstname: true,
          lastname: true,
          email: true,
        },
      },
    },
    orderBy: buildSortQuery(sortBy, sortOrder),
    skip,
    take,
  })

  return paginatedResponse(contactForms, page, limit, total)
})

// POST /api/contact - Submit a new contact form
export const POST = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['POST'])
  
  const validate = validateRequest(createContactFormSchema)
  const data = await validate(request)

  // If userId is provided, check if the user exists
  if (data.userId) {
    const user = await prisma.userss.findUnique({
      where: { id: data.userId },
    })

    if (!user) {
      throw new Error('User not found')
    }
  }

  const contactForm = await prisma.contactforms.create({
    data,
    include: {
      user: {
        select: {
          id: true,
          firstname: true,
          lastname: true,
          email: true,
        },
      },
    },
  })

  // TODO: Send email notification to admin
  // TODO: Send confirmation email to user

  return successResponse(contactForm, 'Contact form submitted successfully', 201)
})

// PUT /api/contact - Bulk update contact forms (admin only)
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid contact form IDs provided')
  }

  const validate = validateRequest(updateContactFormSchema)
  const updateData = await validate({ json: () => data } as NextRequest)

  const updatedForms = await prisma.contactforms.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: updateData,
  })

  return successResponse(
    { count: updatedForms.count },
    `${updatedForms.count} contact forms updated successfully`
  )
})

// DELETE /api/contact - Bulk delete contact forms (admin only)
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid contact form IDs provided')
  }

  const deletedForms = await prisma.contactforms.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedForms.count },
    `${deletedForms.count} contact forms deleted successfully`
  )
})
