'use client';

import { ClientsManager } from '@/components/admin/clients/clients-manager';
import { CrudConfig } from '@/components/admin/crud/types';

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  country?: string
  zipCode?: string
  logoUrl?: string
  isActive: boolean
  notes?: string
  createdAt: string
  updatedAt: string
  projects?: any[]
  testimonials?: any[]
  _count?: {
    projects: number
    testimonials: number
  }
}

const clientConfig: CrudConfig<Client> = {
  title: 'Clients',
  description: 'Manage your client relationships and contact information',
  endpoint: 'clients', // API endpoint

  columns: [
    {
      key: 'companyName',
      label: 'Company',
      sortable: true,
      searchable: true,
      renderType: 'company',
      defaultVisible: true
    },
    {
      key: 'contactName',
      label: 'Contact Person',
      sortable: true,
      searchable: true,
      defaultVisible: true
    },
    {
      key: 'contactEmail',
      label: 'Email',
      sortable: true,
      searchable: true,
      renderType: 'email',
      defaultVisible: true
    },
    {
      key: 'contactPhone',
      label: 'Phone',
      sortable: false,
      searchable: false,
      defaultVisible: true
    },

    {
      key: 'city',
      label: 'City',
      sortable: true,
      searchable: true,
      defaultVisible: false
    },
    {
      key: 'state',
      label: 'State',
      sortable: true,
      searchable: true,
      defaultVisible: false
    },
    {
      key: 'country',
      label: 'Country',
      sortable: true,
      searchable: true,
      defaultVisible: false
    },
    {
      key: 'website',
      label: 'Website',
      sortable: false,
      searchable: true,
      defaultVisible: false
    },
    {
      key: 'updatedAt',
      label: 'Last Active',
      sortable: true,
      searchable: false,
      renderType: 'date',
      defaultVisible: true
    },
    {
      key: 'isActive',
      label: 'Status',
      sortable: true,
      searchable: false,
      renderType: 'status',
      renderProps: {
        trueLabel: 'Active',
        falseLabel: 'Inactive',
        statusColors: {
          true: 'bg-green-100 text-green-800',
          false: 'bg-red-100 text-red-800'
        }
      },
      defaultVisible: true
    }
  ],

  // Action buttons for each row
  actions: [
    {
      action: 'view',
      label: 'View',
      icon: 'EyeIcon',
      variant: 'secondary',
      tooltip: 'View client details'
    },
    {
      action: 'edit',
      label: 'Edit',
      icon: 'PencilIcon',
      variant: 'primary',
      tooltip: 'Edit client'
    },
    {
      action: 'toggle-status',
      label: 'Toggle Status',
      icon: 'PowerIcon',
      variant: 'warning',
      tooltip: 'Activate/Deactivate client'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      tooltip: 'Delete client'
    }
  ],

  fields: [
    {
      name: 'companyName',
      label: 'Company Name',
      type: 'text',
      required: true,
      placeholder: 'Enter company name'
    },
    {
      name: 'contactName',
      label: 'Contact Person',
      type: 'text',
      required: true,
      placeholder: 'Primary contact name'
    },
    {
      name: 'contactEmail',
      label: 'Contact Email',
      type: 'email',
      required: true,
      placeholder: '<EMAIL>'
    },
    {
      name: 'contactPhone',
      label: 'Contact Phone',
      type: 'text',
      placeholder: '+****************'
    },
    {
      name: 'website',
      label: 'Website',
      type: 'text',
      placeholder: 'https://company.com'
    },

    {
      name: 'address',
      label: 'Address',
      type: 'text',
      placeholder: 'Street address'
    },
    {
      name: 'city',
      label: 'City',
      type: 'text',
      placeholder: 'City'
    },
    {
      name: 'state',
      label: 'State/Province',
      type: 'text',
      placeholder: 'State or Province'
    },
    {
      name: 'zipCode',
      label: 'ZIP/Postal Code',
      type: 'text',
      placeholder: 'ZIP or Postal Code'
    },
    {
      name: 'country',
      label: 'Country',
      type: 'text',
      placeholder: 'Country'
    },
    {
      name: 'logoUrl',
      label: 'Company Logo',
      type: 'text',
      placeholder: 'Enter logo path or click Upload to select file'
    },
    {
      name: 'notes',
      label: 'Notes',
      type: 'textarea',
      placeholder: 'Additional notes about the client...'
    },
    {
      name: 'isActive',
      label: 'Active Client',
      type: 'checkbox',
      defaultValue: true
    }
  ],

  filters: [
    {
      name: 'isActive',
      label: 'Status',
      type: 'select',
      options: [
        { value: '', label: 'All' },
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' },
      ],
    },
  ],

  bulkActions: [
    {
      label: 'Activate Selected',
      action: 'activate',
      variant: 'success'
    },
    {
      label: 'Deactivate Selected',
      action: 'deactivate',
      variant: 'warning'
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search clients by company, contact, email...',
  defaultSort: { field: 'updatedAt', direction: 'desc' }, // Sort by Last Active (most recent)
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['companyName', 'contactName', 'contactEmail', 'contactPhone', 'updatedAt', 'isActive']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 2,
    sections: [
      {
        title: 'Company Information',
        fields: ['companyName', 'website', 'logoUrl']
      },
      {
        title: 'Contact Details',
        fields: ['contactName', 'contactEmail', 'contactPhone']
      },
      {
        title: 'Address Information',
        fields: ['address', 'city', 'state', 'zipCode', 'country']
      },
      {
        title: 'Additional Information',
        fields: ['notes', 'isActive']
      }
    ]
  }
};

export default function ClientsPage() {
  return <ClientsManager config={clientConfig} />;
}


