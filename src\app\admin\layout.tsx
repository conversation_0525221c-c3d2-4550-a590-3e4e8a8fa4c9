'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import AuthGuard from '@/components/admin/auth-guard';
import { NotificationProvider } from '@/components/admin/ui/notification';
import {
  HomeIcon,
  UserGroupIcon,
  DocumentTextIcon,
  CogIcon,
  ChartBarIcon,
  BriefcaseIcon,
  UserIcon,
  DocumentDuplicateIcon,
  ChatBubbleLeftRightIcon,
  CloudArrowUpIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  MagnifyingGlassIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';

const navigation = [
  {
    name: 'Dashboard',
    href: '/admin',
    icon: HomeIcon,
    section: 'Main'
  },
  {
    name: 'Content Management',
    section: 'Content',
    items: [
      { name: 'Hero Sections', href: '/admin/hero-sections', icon: DocumentTextIcon },
      { name: 'About Pages', href: '/admin/about-pages', icon: DocumentTextIcon },
      { name: 'Services', href: '/admin/services', icon: CogIcon },
      { name: 'Team Members', href: '/admin/team-members', icon: UserGroupIcon },
      { name: 'Technologies', href: '/admin/technologies', icon: CogIcon },
      { name: 'Testimonials', href: '/admin/testimonials', icon: ChatBubbleLeftRightIcon },
      { name: 'Blog Posts', href: '/admin/blog', icon: DocumentTextIcon },
      { name: 'Legal Pages', href: '/admin/legal-pages', icon: DocumentDuplicateIcon },
    ]
  },
  {
    name: 'Business',
    section: 'Business',
    items: [
      { name: 'Jobs', href: '/admin/jobs', icon: BriefcaseIcon },
      { name: 'Clients', href: '/admin/clients', icon: UserIcon },
      { name: 'Projects', href: '/admin/projects', icon: BriefcaseIcon },
      { name: 'Invoices', href: '/admin/invoices', icon: DocumentTextIcon },
      { name: 'Contact Forms', href: '/admin/contact-forms', icon: ChatBubbleLeftRightIcon },
    ]
  },
  {
    name: 'System',
    section: 'System',
    items: [
      { name: 'Page Builder', href: '/admin/page-builder', icon: DocumentTextIcon },
      { name: 'Data Upload', href: '/admin/data-upload', icon: CloudArrowUpIcon },
      { name: 'Chatbot', href: '/admin/chatbot', icon: ChatBubbleLeftRightIcon },
      { name: 'Users', href: '/admin/users', icon: UserGroupIcon },
      { name: 'Settings', href: '/admin/settings', icon: CogIcon },
    ]
  }
];

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();

  const isActive = (href: string) => {
    if (href === '/admin') {
      return pathname === '/admin';
    }
    return pathname.startsWith(href);
  };

  return (
    <AuthGuard requireAdmin={true}>
      <NotificationProvider>
        <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <AnimatePresence>
        {sidebarOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
              onClick={() => setSidebarOpen(false)}
            />
            <motion.div
              initial={{ x: '-100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl lg:hidden"
            >
              <div className="flex h-16 items-center justify-between px-4 border-b border-gray-200">
                <div className="flex items-center space-x-2">
                  <div className="h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">T</span>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900">Technoloway</div>
                    <div className="text-xs text-gray-500">Admin Panel</div>
                  </div>
                </div>
                <button
                  onClick={() => setSidebarOpen(false)}
                  className="p-2 rounded-md text-gray-400 hover:text-gray-500"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
              <nav className="mt-5 px-2">
                {navigation.map((item) => (
                  <div key={item.name} className="mb-6">
                    {item.href ? (
                      <Link
                        href={item.href}
                        className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                          isActive(item.href)
                            ? 'bg-blue-100 text-blue-900'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                        }`}
                        onClick={() => setSidebarOpen(false)}
                      >
                        <item.icon className="mr-3 h-6 w-6" />
                        {item.name}
                      </Link>
                    ) : (
                      <>
                        <div className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2">
                          {item.section}
                        </div>
                        {item.items?.map((subItem) => (
                          <Link
                            key={subItem.name}
                            href={subItem.href}
                            className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                              isActive(subItem.href)
                                ? 'bg-blue-100 text-blue-900'
                                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                            }`}
                            onClick={() => setSidebarOpen(false)}
                          >
                            <subItem.icon className="mr-3 h-5 w-5" />
                            {subItem.name}
                          </Link>
                        ))}
                      </>
                    )}
                  </div>
                ))}
              </nav>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200">
          <div className="flex h-16 flex-shrink-0 items-center px-4 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">T</span>
              </div>
              <div>
                <div className="text-lg font-bold text-gray-900">Technoloway</div>
                <div className="text-xs text-gray-500">Admin Panel</div>
              </div>
            </div>
          </div>
          <div className="flex flex-1 flex-col overflow-y-auto pt-5 pb-4">
            <nav className="mt-5 flex-1 px-2 space-y-6">
              {navigation.map((item) => (
                <div key={item.name}>
                  {item.href ? (
                    <Link
                      href={item.href}
                      className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                        isActive(item.href)
                          ? 'bg-blue-100 text-blue-900'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <item.icon className="mr-3 h-6 w-6" />
                      {item.name}
                    </Link>
                  ) : (
                    <>
                      <div className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2">
                        {item.section}
                      </div>
                      <div className="space-y-1">
                        {item.items?.map((subItem) => (
                          <Link
                            key={subItem.name}
                            href={subItem.href}
                            className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                              isActive(subItem.href)
                                ? 'bg-blue-100 text-blue-900'
                                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                            }`}
                          >
                            <subItem.icon className="mr-3 h-5 w-5" />
                            {subItem.name}
                          </Link>
                        ))}
                      </div>
                    </>
                  )}
                </div>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64 flex flex-col flex-1">
        {/* Top navigation */}
        <div className="sticky top-0 z-10 flex h-16 flex-shrink-0 bg-white shadow">
          <button
            type="button"
            className="border-r border-gray-200 px-4 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>
          <div className="flex flex-1 justify-between px-4">
            <div className="flex flex-1">
              <div className="flex w-full md:ml-0">
                <div className="relative w-full text-gray-400 focus-within:text-gray-600">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center">
                    <MagnifyingGlassIcon className="h-5 w-5" />
                  </div>
                  <input
                    className="block h-full w-full border-transparent py-2 pl-8 pr-3 text-gray-900 placeholder-gray-500 focus:border-transparent focus:placeholder-gray-400 focus:outline-none focus:ring-0 sm:text-sm"
                    placeholder="Search..."
                    type="search"
                  />
                </div>
              </div>
            </div>
            <div className="ml-4 flex items-center md:ml-6">
              <button
                type="button"
                className="rounded-full bg-white p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <BellIcon className="h-6 w-6" />
              </button>
              <div className="relative ml-3">
                <button
                  type="button"
                  className="flex max-w-xs items-center rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                    <span className="text-white text-sm font-medium">A</span>
                  </div>
                </button>
              </div>
              <button
                type="button"
                className="ml-3 rounded-full bg-white p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <ArrowRightOnRectangleIcon className="h-6 w-6" />
              </button>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1">
          {children}
        </main>
      </div>
      </div>
      </NotificationProvider>
    </AuthGuard>
  );
}
