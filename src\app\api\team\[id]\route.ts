import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/team/[id] - Get a specific team member
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const { id } = await params

  const teamMember = await prisma.teammembers.findUnique({
    where: { id },
    include: {
      tasks: {
        select: {
          id: true,
          description: true,
          status: true,
          projstartdate: true,
          endDate: true,
          workHours: true,
          payRate: true,
          project: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
        },
        orderBy: {
          createdat: 'desc',
        },
      },
      payrollRecords: {
        select: {
          id: true,
          payDate: true,
          workHours: true,
          payPeriod: true,
          baseSalary: true,
          grossPay: true,
          netPay: true,
          status: true,
        },
        orderBy: {
          payDate: 'desc',
        },
        take: 10, // Limit to recent payroll records
      },
      _count: {
        select: {
          tasks: true,
          payrollRecords: true,
        },
      },
    },
  })

  if (!teamMember) {
    throw new ApiError('Team member not found', 404)
  }

  return successResponse(teamMember)
})

// PUT /api/team/[id] - Update a team member
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params
  const validate = validateRequest(schemas.teamMember.update)
  const data = await validate(request)

  // Check if team member exists
  const existingMember = await prisma.teammembers.findUnique({
    where: { id },
  })

  if (!existingMember) {
    throw new ApiError('Team member not found', 404)
  }

  // Check if email is being changed and if it conflicts with another team member
  if (data.email && data.email !== existingMember.email) {
    const emailConflict = await prisma.teammembers.findFirst({
      where: {
        email: data.email,
        id: { not: id }
      },
    })

    if (emailConflict) {
      throw new ApiError('A team member with this email already exists', 400)
    }
  }

  const teamMember = await prisma.teammembers.update({
    where: { id },
    data,
    include: {
      _count: {
        select: {
          tasks: true,
          payrollRecords: true,
        },
      },
    },
  })

  return successResponse(teamMember, 'Team member updated successfully')
})

// DELETE /api/team/[id] - Delete a team member
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params

  // Check if team member exists
  const existingMember = await prisma.teammembers.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          tasks: true,
          payrollRecords: true,
        },
      },
    },
  })

  if (!existingMember) {
    throw new ApiError('Team member not found', 404)
  }

  // Check if team member has related data
  const hasRelatedData = existingMember._count.tasks > 0 || 
                        existingMember._count.payrollRecords > 0

  if (hasRelatedData) {
    throw new ApiError('Cannot delete team member with related tasks or payroll records. Please remove them first.', 400)
  }

  await prisma.teammembers.delete({
    where: { id },
  })

  return successResponse(null, 'Team member deleted successfully')
})

// PATCH /api/team/[id] - Partial update (e.g., toggle status)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params
  const body = await request.json()

  // Check if team member exists
  const existingMember = await prisma.teammembers.findUnique({
    where: { id },
  })

  if (!existingMember) {
    throw new ApiError('Team member not found', 404)
  }

  // Only allow specific fields for PATCH
  const allowedFields = ['isactive', 'displayorder', 'notes']
  const updateData: any = {}

  for (const field of allowedFields) {
    if (body[field] !== undefined) {
      if (field === 'isactive') {
        updateData[field] = body[field] === true || body[field] === 'true'
      } else if (field === 'displayorder') {
        updateData[field] = parseInt(body[field])
      } else {
        updateData[field] = body[field]
      }
    }
  }

  if (Object.keys(updateData).length === 0) {
    throw new ApiError('No valid fields to update', 400)
  }

  const teamMember = await prisma.teammembers.update({
    where: { id },
    data: updateData,
    include: {
      _count: {
        select: {
          tasks: true,
          payrollRecords: true,
        },
      },
    },
  })

  return successResponse(teamMember, 'Team member updated successfully')
})
