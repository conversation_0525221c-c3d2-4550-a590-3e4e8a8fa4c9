import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { 
  withError<PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  ApiError
} from '@/lib/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// POST /api/upload/service-icon - Upload service icon
export const POST = withError<PERSON>andler(async (request: NextRequest) => {
  await requireAdmin(request)

  try {
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      throw new ApiError('No file provided', 400)
    }

    // Validate file type - prefer SVG and PNG for icons
    const allowedTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      throw new ApiError('Invalid file type. Only SVG, PNG, JPEG, and WebP files are allowed.', 400)
    }

    // Validate file size (2MB max for icons)
    const maxSize = 2 * 1024 * 1024 // 2MB
    if (file.size > maxSize) {
      throw new ApiError('File too large. Maximum size is 2MB.', 400)
    }

    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'service-icons')
    try {
      await mkdir(uploadDir, { recursive: true })
    } catch (error) {
      // Directory might already exist, ignore error
    }

    // Generate unique filename
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const extension = file.name.split('.').pop()
    const filename = `service-icon-${timestamp}-${randomString}.${extension}`

    // Convert file to buffer and save
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    const filepath = join(uploadDir, filename)
    
    await writeFile(filepath, buffer)

    // Return the public URL
    const publicUrl = `/uploads/service-icons/${filename}`

    return successResponse({
      url: publicUrl,
      filename,
      size: file.size,
      type: file.type
    }, 'Service icon uploaded successfully')

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Failed to upload service icon', 500)
  }
})

// GET /api/upload/service-icon - Get upload info
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  return successResponse({
    maxSize: '2MB',
    allowedTypes: ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg', 'image/webp'],
    uploadPath: '/uploads/service-icons/',
    recommendations: {
      format: 'SVG preferred for scalability, PNG for detailed icons',
      size: '64x64px to 128x128px recommended',
      style: 'Simple, clean icons work best'
    }
  })
})
