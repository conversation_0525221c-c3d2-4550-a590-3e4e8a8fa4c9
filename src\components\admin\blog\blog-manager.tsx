'use client'

import React, { useState, useEffect, useRef } from 'react'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  DocumentTextIcon,
  CalendarIcon,
  TagIcon
} from '@heroicons/react/24/outline'
import { BlogModal } from './blog-modal'
import { BlogAvatar } from './blog-avatar'
import { CrudConfig } from '../crud/types'
import { motion, AnimatePresence } from 'framer-motion'

interface BlogPost {
  id: string
  title: string
  slug: string
  content: string
  excerpt?: string
  featuredImageUrl?: string
  authorId?: string
  isPublished: boolean
  publishedAt?: string
  categories?: string
  tags?: string
  createdAt: string
  updatedAt: string
  [key: string]: any
}

interface BlogManagerProps {
  config: CrudConfig<BlogPost>
}

export function BlogManager({ config }: BlogManagerProps) {
  // State management
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [sortBy, setSortBy] = useState(config.defaultSort?.field || 'updatedAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(config.defaultSort?.direction || 'desc')
  const [selectedPosts, setSelectedPosts] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'cards'>(config.defaultViewSettings?.mode || 'list')
  const [density, setDensity] = useState<'compact' | 'comfortable' | 'spacious'>(config.defaultViewSettings?.density || 'comfortable')
  const [visibleColumns, setVisibleColumns] = useState<string[]>(config.defaultViewSettings?.visibleColumns || [])
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnSelector, setShowColumnSelector] = useState(false)
  const [showDensitySelector, setShowDensitySelector] = useState(false)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null)

  // Refs
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
      setCurrentPage(1) // Reset to first page when searching
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch blog posts
  const fetchBlogPosts = async (preserveFocus = false) => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: (config.pageSize || 10).toString(),
        search: debouncedSearchQuery,
        sortBy,
        sortOrder,
      })

      console.log('Fetching blog posts with params:', params.toString()) // Debug log

      const response = await fetch(`/api/admin/${config.endpoint}?${params}`)
      if (!response.ok) throw new Error('Failed to fetch blog posts')

      const data = await response.json()
      console.log('Received blog posts data:', data) // Debug log

      setBlogPosts(data.data || [])
      setTotalPages(Math.ceil((data.total || 0) / (config.pageSize || 10)))
      setError(null) // Clear any previous errors on successful fetch
    } catch (err) {
      console.error('Error fetching blog posts:', err) // Debug log
      setError(err instanceof Error ? err.message : 'Failed to fetch blog posts')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Preserve focus when searching
    const isSearching = debouncedSearchQuery !== ''
    fetchBlogPosts(isSearching)
  }, [currentPage, debouncedSearchQuery, sortBy, sortOrder])

  // Handle create
  const handleCreate = async (formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to create blog post')

      setIsCreateModalOpen(false)
      fetchBlogPosts()
      alert('Blog post created successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create blog post'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle update
  const handleUpdate = async (id: string, formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to update blog post')

      setIsEditModalOpen(false)
      setEditingPost(null)
      fetchBlogPosts()
      alert('Blog post updated successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update blog post'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) throw new Error('Failed to delete blog post')

      fetchBlogPosts()
      alert('Blog post deleted successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post'
      setError(errorMessage)
      alert(errorMessage)
    }
  }

  // Handle toggle published status
  const handleTogglePublished = async (id: string, newStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isPublished: newStatus }),
      })
      
      if (!response.ok) throw new Error('Failed to update blog post published status')

      fetchBlogPosts()
      alert(`Blog post ${newStatus ? 'published' : 'unpublished'} successfully!`)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update blog post published status'
      setError(errorMessage)
      alert(errorMessage)
    }
  }

  // Handle bulk actions
  const handleBulkAction = async (action: string) => {
    if (selectedPosts.length === 0) {
      alert('Please select blog posts to perform bulk actions')
      return
    }

    try {
      let endpoint = `/api/admin/${config.endpoint}`
      let method = 'PUT'
      let body: any = { ids: selectedPosts }

      switch (action) {
        case 'publish':
          body.data = { isPublished: true }
          break
        case 'unpublish':
          body.data = { isPublished: false }
          break
        case 'delete':
          if (!window.confirm(`Are you sure you want to delete ${selectedPosts.length} blog posts? This action cannot be undone.`)) {
            return
          }
          method = 'DELETE'
          break
        default:
          throw new Error(`Unknown bulk action: ${action}`)
      }

      const response = await fetch(endpoint, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      })

      if (!response.ok) throw new Error(`Failed to ${action} blog posts`)

      setSelectedPosts([])
      fetchBlogPosts()
      alert(`Bulk ${action} completed successfully!`)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `Failed to ${action} blog posts`
      setError(errorMessage)
      alert(errorMessage)
    }
  }

  // Handle individual actions
  const handleAction = async (action: string, item: BlogPost) => {
    const actionKey = `${action}-${item.id}`

    try {
      setActionLoading(actionKey)

      switch (action) {
        case 'view':
          // Open blog post details in new tab or modal
          window.open(`/admin/blog/${item.id}`, '_blank')
          break

        case 'edit':
          setEditingPost(item)
          setIsEditModalOpen(true)
          break

        case 'toggle-published':
          await handleTogglePublished(item.id, !item.isPublished)
          break

        case 'delete':
          const deleteAction = config.actions?.find(a => a.action === 'delete')
          const confirmMessage = deleteAction?.confirmationMessage || 'Are you sure you want to delete this blog post? This action cannot be undone.'

          if (window.confirm(confirmMessage)) {
            await handleDelete(item.id)
          }
          break

        default:
          console.warn(`Unknown action: ${action}`)
      }
    } finally {
      setActionLoading(null)
    }
  }

  // Handle sorting
  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
    setCurrentPage(1)
  }

  // Handle selection
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedPosts(blogPosts.map(post => post.id))
    } else {
      setSelectedPosts([])
    }
  }

  const handleSelectPost = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedPosts([...selectedPosts, id])
    } else {
      setSelectedPosts(selectedPosts.filter(postId => postId !== id))
    }
  }

  // Get visible fields for table
  const getVisibleFields = () => {
    if (visibleColumns.length > 0) {
      return config.fields?.filter(field => visibleColumns.includes(field.key)) || []
    }
    return config.fields || []
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  // Truncate text
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  return (
    <div className="space-y-6 mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
          <p className="text-gray-600">{config.description}</p>
        </div>

        {config.permissions?.create && (
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            Add Blog Post
          </button>
        )}
      </div>

      {/* Search and Controls */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search Bar */}
          {config.enableSearch && (
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder={config.searchPlaceholder || 'Search blog posts...'}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {searchQuery && (
                  <button
                    onClick={() => {
                      setSearchQuery('')
                      searchInputRef.current?.focus()
                    }}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Controls */}
          <div className="flex items-center gap-2">
            {/* Filters */}
            {config.enableFilters && (
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`inline-flex items-center px-3 py-2 border rounded-lg text-sm font-medium transition-colors ${
                  showFilters
                    ? 'border-blue-500 text-blue-700 bg-blue-50'
                    : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                }`}
              >
                <FunnelIcon className="w-4 h-4 mr-2" />
                Filters
              </button>
            )}

            {/* View Controls */}
            {config.enableViewControls && (
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-50'}`}
                  title="List View"
                >
                  <ListBulletIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-50'}`}
                  title="Grid View"
                >
                  <Squares2X2Icon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('cards')}
                  className={`p-2 ${viewMode === 'cards' ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-50'}`}
                  title="Card View"
                >
                  <RectangleStackIcon className="w-4 h-4" />
                </button>
              </div>
            )}

            {/* Density Controls */}
            {config.enableDensityControls && (
              <div className="relative">
                <button
                  onClick={() => setShowDensitySelector(!showDensitySelector)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <AdjustmentsHorizontalIcon className="w-4 h-4 mr-2" />
                  {density.charAt(0).toUpperCase() + density.slice(1)}
                  <ChevronDownIcon className="w-4 h-4 ml-2" />
                </button>

                {showDensitySelector && (
                  <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                    <div className="py-1">
                      {['compact', 'comfortable', 'spacious'].map((option) => (
                        <button
                          key={option}
                          onClick={() => {
                            setDensity(option as any)
                            setShowDensitySelector(false)
                          }}
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 ${
                            density === option ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                          }`}
                        >
                          {option.charAt(0).toUpperCase() + option.slice(1)}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Column Visibility */}
            {config.enableColumnVisibility && viewMode === 'list' && (
              <div className="relative">
                <button
                  onClick={() => setShowColumnSelector(!showColumnSelector)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <EyeIcon className="w-4 h-4 mr-2" />
                  Columns
                  <ChevronDownIcon className="w-4 h-4 ml-2" />
                </button>

                {showColumnSelector && (
                  <div className="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                    <div className="p-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-3">Show Columns</h4>
                      <div className="space-y-2">
                        {config.fields?.map((field) => (
                          <label key={field.key} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={visibleColumns.includes(field.key)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setVisibleColumns([...visibleColumns, field.key])
                                } else {
                                  setVisibleColumns(visibleColumns.filter(col => col !== field.key))
                                }
                              }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">{field.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Bulk Actions */}
        {config.enableBulkActions && selectedPosts.length > 0 && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-700">
                {selectedPosts.length} blog post(s) selected
              </span>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => handleBulkAction('publish')}
                  className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                >
                  Publish
                </button>
                <button
                  onClick={() => handleBulkAction('unpublish')}
                  className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
                >
                  Unpublish
                </button>
                <button
                  onClick={() => handleBulkAction('delete')}
                  className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XMarkIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={() => {
                    setError(null)
                    fetchBlogPosts()
                  }}
                  className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="bg-white rounded-lg border border-gray-200 p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading blog posts...</span>
          </div>
        </div>
      )}

      {/* Data Display */}
      {!loading && !error && (
        <div className="bg-white rounded-lg border border-gray-200">
          {blogPosts.length === 0 ? (
            /* Empty State */
            <div className="p-12 text-center">
              <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No blog posts found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {debouncedSearchQuery ? 'Try adjusting your search terms.' : 'Get started by creating your first blog post.'}
              </p>
              {config.permissions?.create && !debouncedSearchQuery && (
                <div className="mt-6">
                  <button
                    onClick={() => setIsCreateModalOpen(true)}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <PlusIcon className="w-5 h-5 mr-2" />
                    Add Blog Post
                  </button>
                </div>
              )}
            </div>
          ) : viewMode === 'list' ? (
            /* Table View */
            <div className="overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      {/* Checkbox Column */}
                      {config.enableBulkActions && (
                        <th className={`w-4 px-6 ${
                          density === 'compact' ? 'py-2' : density === 'spacious' ? 'py-4' : 'py-3'
                        }`}>
                          <input
                            type="checkbox"
                            checked={selectedPosts.length === blogPosts.length && blogPosts.length > 0}
                            onChange={(e) => handleSelectAll(e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </th>
                      )}

                      {/* Data Columns */}
                      {getVisibleFields().map((field) => (
                        <th
                          key={field.key}
                          className={`px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 ${
                            density === 'compact' ? 'py-2' : density === 'spacious' ? 'py-4' : 'py-3'
                          }`}
                          onClick={() => handleSort(field.key)}
                        >
                          <div className="flex items-center space-x-1">
                            <span>{field.label}</span>
                            {sortBy === field.key && (
                              sortOrder === 'asc' ? (
                                <ArrowUpIcon className="w-4 h-4" />
                              ) : (
                                <ArrowDownIcon className="w-4 h-4" />
                              )
                            )}
                          </div>
                        </th>
                      ))}

                      {/* Actions Column */}
                      {config.actions && config.actions.length > 0 && (
                        <th className={`px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                          density === 'compact' ? 'py-2' : density === 'spacious' ? 'py-4' : 'py-3'
                        }`}>
                          Actions
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {blogPosts.map((post) => (
                      <tr
                        key={post.id}
                        className={`hover:bg-gray-50 ${
                          selectedPosts.includes(post.id) ? 'bg-blue-50' : ''
                        }`}
                      >
                        {/* Checkbox */}
                        {config.enableBulkActions && (
                          <td className={`w-4 px-6 ${
                            density === 'compact' ? 'py-2' : density === 'spacious' ? 'py-6' : 'py-4'
                          }`}>
                            <input
                              type="checkbox"
                              checked={selectedPosts.includes(post.id)}
                              onChange={(e) => handleSelectPost(post.id, e.target.checked)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                          </td>
                        )}

                        {/* Data Cells */}
                        {getVisibleFields().map((field) => (
                          <td key={field.key} className={`px-6 whitespace-nowrap ${
                            density === 'compact' ? 'py-2' : density === 'spacious' ? 'py-6' : 'py-4'
                          }`}>
                            {field.key === 'title' ? (
                              <div className="flex items-center">
                                <BlogAvatar
                                  title={post.title}
                                  featuredImageUrl={post.featuredImageUrl}
                                  size="sm"
                                  className="mr-3"
                                />
                                <div>
                                  <div className="text-sm font-medium text-gray-900">{post.title}</div>
                                  <div className="text-sm text-gray-500">{post.slug}</div>
                                </div>
                              </div>
                            ) : field.key === 'isPublished' ? (
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                post.isPublished
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {post.isPublished ? 'Published' : 'Draft'}
                              </span>
                            ) : field.key === 'excerpt' || field.key === 'content' ? (
                              <div className="text-sm text-gray-900 max-w-xs truncate" title={post[field.key]}>
                                {truncateText(post[field.key] || '', 50)}
                              </div>
                            ) : field.key === 'tags' || field.key === 'categories' ? (
                              <div className="text-sm text-gray-900">
                                {post[field.key] ? (
                                  <div className="flex flex-wrap gap-1">
                                    {post[field.key].split(',').slice(0, 2).map((tag: string, index: number) => (
                                      <span key={index} className="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                        {tag.trim()}
                                      </span>
                                    ))}
                                    {post[field.key].split(',').length > 2 && (
                                      <span className="text-xs text-gray-500">+{post[field.key].split(',').length - 2} more</span>
                                    )}
                                  </div>
                                ) : '-'}
                              </div>
                            ) : field.key === 'createdAt' || field.key === 'updatedAt' || field.key === 'publishedAt' ? (
                              <div className="text-sm text-gray-500">
                                {post[field.key] ? formatDate(post[field.key]) : '-'}
                              </div>
                            ) : (
                              <div className="text-sm text-gray-900">
                                {post[field.key] || '-'}
                              </div>
                            )}
                          </td>
                        ))}

                        {/* Actions */}
                        {config.actions && config.actions.length > 0 && (
                          <td className={`px-6 whitespace-nowrap text-right text-sm font-medium ${
                            density === 'compact' ? 'py-2' : density === 'spacious' ? 'py-6' : 'py-4'
                          }`}>
                            <div className="flex items-center space-x-2">
                              {config.actions.map((action) => {
                                const isLoading = actionLoading === `${action.action}-${post.id}`
                                const IconComponent = action.icon === 'EyeIcon' ? EyeIcon :
                                                   action.icon === 'PencilIcon' ? PencilIcon :
                                                   action.icon === 'PowerIcon' ? PowerIcon :
                                                   action.icon === 'TrashIcon' ? TrashIcon : DocumentTextIcon

                                return (
                                  <button
                                    key={action.action}
                                    onClick={() => handleAction(action.action, post)}
                                    disabled={isLoading}
                                    className={`p-1 rounded-md transition-colors ${
                                      action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' :
                                      action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' :
                                      action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' :
                                      action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                                      'text-gray-600 hover:bg-gray-50'
                                    } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                    title={action.tooltip}
                                  >
                                    {isLoading ? (
                                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                                    ) : (
                                      <IconComponent className="w-4 h-4" />
                                    )}
                                  </button>
                                )
                              })}
                            </div>
                          </td>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : viewMode === 'grid' ? (
            /* Grid View */
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
              {blogPosts.map((post) => (
                <div key={post.id} className={`bg-white rounded-lg border-2 p-4 hover:shadow-lg transition-all duration-200 ${
                  selectedPosts.includes(String(post.id)) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'
                }`}>
                  {/* Checkbox */}
                  {config.enableBulkActions && (
                    <div className="flex justify-end mb-2">
                      <input
                        type="checkbox"
                        checked={selectedPosts.includes(String(post.id))}
                        onChange={(e) => handleSelectPost(String(post.id), e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </div>
                  )}

                  {/* Featured Image */}
                  <div className="flex justify-center mb-4">
                    <BlogAvatar
                      title={post.title}
                      featuredImageUrl={post.featuredImageUrl}
                      size="xl"
                    />
                  </div>

                  {/* Post Info */}
                  <div className="text-center space-y-2">
                    <h3 className="font-semibold text-gray-900 truncate">{post.title}</h3>
                    <p className="text-sm text-gray-600">{post.slug}</p>

                    {/* Published Status */}
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      post.isPublished
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {post.isPublished ? 'Published' : 'Draft'}
                    </span>

                    {/* Excerpt */}
                    {post.excerpt && (
                      <p className="text-sm text-gray-700 line-clamp-3">
                        {truncateText(post.excerpt, 100)}
                      </p>
                    )}

                    {/* Tags */}
                    {post.tags && (
                      <div className="flex flex-wrap justify-center gap-1">
                        {post.tags.split(',').slice(0, 3).map((tag, index) => (
                          <span key={index} className="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                            {tag.trim()}
                          </span>
                        ))}
                      </div>
                    )}

                    {/* Date */}
                    <div className="text-xs text-gray-500">
                      {post.publishedAt ? `Published: ${formatDate(post.publishedAt)}` : `Created: ${formatDate(post.createdAt)}`}
                    </div>
                  </div>

                  {/* Actions */}
                  {config.actions && config.actions.length > 0 && (
                    <div className="flex justify-center space-x-2 mt-4 pt-4 border-t border-gray-100">
                      {config.actions.map((action) => {
                        const isLoading = actionLoading === `${action.action}-${post.id}`
                        const IconComponent = action.icon === 'EyeIcon' ? EyeIcon :
                                             action.icon === 'PencilIcon' ? PencilIcon :
                                             action.icon === 'PowerIcon' ? PowerIcon :
                                             action.icon === 'TrashIcon' ? TrashIcon : DocumentTextIcon

                        return (
                          <button
                            key={action.action}
                            onClick={() => handleAction(action.action, post)}
                            disabled={isLoading}
                            className={`p-2 rounded-md transition-colors ${
                              action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' :
                              action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' :
                              action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' :
                              action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                              'text-gray-600 hover:bg-gray-50'
                            } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                            title={action.tooltip}
                          >
                            {isLoading ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                            ) : (
                              <IconComponent className="w-4 h-4" />
                            )}
                          </button>
                        )
                      })}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            /* Card View */
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-4 sm:p-6">
                {blogPosts.map((post) => (
                  <div key={post.id} className={`rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300 border-2 min-h-[320px] ${selectedPosts.includes(String(post.id)) ? 'bg-blue-50 border-blue-500' : 'bg-white border-gray-100 hover:border-blue-200'}`}>
                    <div className="flex h-full">
                      {/* Image Section - Full Height */}
                      <div className="flex-shrink-0 w-56 relative">
                        <BlogAvatar
                          title={post.title}
                          featuredImageUrl={post.featuredImageUrl}
                          size="full-height"
                          className="shadow-none"
                          style={{
                            width: '100%',
                            height: '100%',
                            minHeight: '320px'
                          }}
                        />

                        {/* Checkbox overlay */}
                        {config.enableBulkActions && (
                          <div className="absolute top-4 left-4">
                            <input
                              type="checkbox"
                              checked={selectedPosts.includes(String(post.id))}
                              onChange={(e) => handleSelectPost(String(post.id), e.target.checked)}
                              className="w-5 h-5 rounded border-2 border-white text-blue-600 focus:ring-blue-500 shadow-lg"
                            />
                          </div>
                        )}
                      </div>

                      {/* Content Section */}
                      <div className="flex-1 p-6 flex flex-col justify-between">
                        {/* Header */}
                        <div className="space-y-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h3 className="text-xl font-bold text-gray-900 mb-1">{post.title}</h3>
                              <p className="text-sm text-gray-600">{post.slug}</p>
                              <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full mt-2 ${
                                post.isPublished
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {post.isPublished ? 'Published' : 'Draft'}
                              </span>
                            </div>
                          </div>

                          {/* Excerpt */}
                          {post.excerpt && (
                            <p className="text-gray-700 text-sm leading-relaxed line-clamp-4">
                              {truncateText(post.excerpt, 150)}
                            </p>
                          )}

                          {/* Tags */}
                          {post.tags && (
                            <div className="flex flex-wrap gap-1">
                              {post.tags.split(',').slice(0, 4).map((tag, index) => (
                                <span key={index} className="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                  {tag.trim()}
                                </span>
                              ))}
                              {post.tags.split(',').length > 4 && (
                                <span className="text-xs text-gray-500">+{post.tags.split(',').length - 4} more</span>
                              )}
                            </div>
                          )}

                          {/* Metadata */}
                          <div className="grid grid-cols-1 gap-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-500">Published:</span>
                              <span className="text-gray-900 font-medium">
                                {post.publishedAt ? formatDate(post.publishedAt) : 'Not published'}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-500">Last Updated:</span>
                              <span className="text-gray-900 font-medium">
                                {formatDate(post.updatedAt)}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        {config.actions && config.actions.length > 0 && (
                          <div className="flex items-center justify-end space-x-2 pt-4 border-t border-gray-100">
                            {config.actions.map((action) => {
                              const isLoading = actionLoading === `${action.action}-${post.id}`
                              const IconComponent = action.icon === 'EyeIcon' ? EyeIcon :
                                                 action.icon === 'PencilIcon' ? PencilIcon :
                                                 action.icon === 'PowerIcon' ? PowerIcon :
                                                 action.icon === 'TrashIcon' ? TrashIcon : DocumentTextIcon

                              return (
                                <button
                                  key={action.action}
                                  onClick={() => handleAction(action.action, post)}
                                  disabled={isLoading}
                                  className={`p-2 rounded-lg transition-colors ${
                                    action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' :
                                    action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' :
                                    action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' :
                                    action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                                    'text-gray-600 hover:bg-gray-50'
                                  } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                  title={action.tooltip}
                                >
                                  {isLoading ? (
                                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current"></div>
                                  ) : (
                                    <IconComponent className="w-5 h-5" />
                                  )}
                                </button>
                              )
                            })}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Showing page {currentPage} of {totalPages}
                </div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  {/* Page Numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
                    if (pageNum > totalPages) return null

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          currentPage === pageNum
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    )
                  })}

                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Create Modal */}
      <BlogModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreate}
        title="Create Blog Post"
        fields={config.fields}
        layout={config.formLayout}
      />

      {/* Edit Modal */}
      <BlogModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setEditingPost(null)
        }}
        onSubmit={(data) => editingPost && handleUpdate(editingPost.id, data)}
        title="Edit Blog Post"
        initialData={editingPost}
        fields={config.fields}
        layout={config.formLayout}
      />
    </div>
  )
}
