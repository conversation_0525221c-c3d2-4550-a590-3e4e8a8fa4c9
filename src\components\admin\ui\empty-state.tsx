'use client'

import React, { ReactNode } from 'react'
import { DocumentIcon } from '@heroicons/react/24/outline'

interface EmptyStateProps {
  title: string
  description?: string
  icon?: ReactNode
  action?: ReactNode
}

export function EmptyState({ 
  title, 
  description, 
  icon = <DocumentIcon className="h-12 w-12 text-gray-400" />,
  action 
}: EmptyStateProps) {
  return (
    <div className="text-center py-12">
      <div className="flex justify-center mb-4">
        {icon}
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      {description && (
        <p className="text-gray-500 mb-6">{description}</p>
      )}
      {action && action}
    </div>
  )
}
