'use client'

import React, { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import <PERSON><PERSON><PERSON><PERSON> from '@/components/page-builder/PageRenderer'
import { Component } from '@/lib/page-builder/store'
import { ArrowLeftIcon, EyeIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

interface PageData {
  id: string
  title: string
  slug: string
  description?: string
  metaTitle?: string
  metaDescription?: string
  isPublished: boolean
  isHomePage: boolean
  layout: any
  styles: Record<string, any>
  seoSettings: Record<string, any>
  components: Component[]
  createdAt: string
  updatedAt: string
  publishedAt?: string
}

const PreviewPage: React.FC = () => {
  const params = useParams()
  const slug = Array.isArray(params.slug) ? params.slug[0] : params.slug
  
  const [pageData, setPageData] = useState<PageData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  useEffect(() => {
    const fetchPageData = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/page-builder/preview/${slug}`)
        
        if (!response.ok) {
          if (response.status === 404) {
            setError('Page not found')
          } else {
            setError('Failed to load page')
          }
          return
        }
        
        const data = await response.json()
        setPageData(data)
      } catch (err) {
        console.error('Error fetching page data:', err)
        setError('Failed to load page')
      } finally {
        setIsLoading(false)
      }
    }
    
    if (slug) {
      fetchPageData()
    }
  }, [slug])
  
  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading page...</p>
        </div>
      </div>
    )
  }
  
  // Error state
  if (error || !pageData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-6xl mb-4">❌</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {error || 'Page Not Found'}
          </h1>
          <p className="text-gray-600 mb-6">
            The page you're looking for doesn't exist or couldn't be loaded.
          </p>
          <Link
            href="/admin/page-builder"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Page Builder
          </Link>
        </div>
      </div>
    )
  }
  
  return (
    <>
      {/* SEO Meta Tags */}
      <head>
        <title>{pageData.metaTitle || pageData.title}</title>
        {pageData.metaDescription && (
          <meta name="description" content={pageData.metaDescription} />
        )}
        {pageData.seoSettings?.keywords && (
          <meta name="keywords" content={pageData.seoSettings.keywords} />
        )}
        {pageData.seoSettings?.author && (
          <meta name="author" content={pageData.seoSettings.author} />
        )}
        <meta name="robots" content={pageData.isPublished ? 'index,follow' : 'noindex,nofollow'} />
      </head>
      
      <div className="relative">
        {/* Preview Header */}
        <div className="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 shadow-sm z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-14">
              <div className="flex items-center space-x-4">
                <Link
                  href="/admin/page-builder"
                  className="flex items-center text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                  Back to Editor
                </Link>
                
                <div className="text-sm text-gray-600">
                  <span className="font-medium">{pageData.title}</span>
                  <span className="mx-2">•</span>
                  <span>/{pageData.slug}</span>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <EyeIcon className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-600">Preview Mode</span>
                </div>
                
                {!pageData.isPublished && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Draft
                  </span>
                )}
                
                <span className="text-xs text-gray-500">
                  Last updated: {new Date(pageData.updatedAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Page Content */}
        <div className="pt-14">
          <PageRenderer
            components={pageData.components}
            styles={pageData.styles}
            isPreview={true}
          />
        </div>
        
        {/* Preview Footer */}
        <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-50">
          <div className="text-xs text-gray-600">
            <div className="font-medium">Page Info</div>
            <div>Components: {pageData.components.length}</div>
            <div>Status: {pageData.isPublished ? 'Published' : 'Draft'}</div>
            {pageData.isHomePage && <div>Home Page: Yes</div>}
          </div>
        </div>
      </div>
    </>
  )
}

export default PreviewPage
