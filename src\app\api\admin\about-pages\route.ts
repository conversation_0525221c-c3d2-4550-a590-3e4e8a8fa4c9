import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  withError<PERSON>and<PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/about-pages - Get all about pages with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['title', 'content'])
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get about pages with pagination
  const [aboutPages, total] = await Promise.all([
    prisma.aboutpages.findMany({
      where: searchQuery,
      include: {
        sections: {
          orderBy: { displayorder: 'asc' }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.aboutpages.count({ where: searchQuery })
  ])

  return paginatedResponse(aboutPages, page, limit, total)
})

// POST /api/admin/about-pages - Create a new about page
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.aboutPage.create)
  const data = await validate(request)

  const aboutPage = await prisma.aboutpages.create({
    data,
    include: {
      sections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  return successResponse(aboutPage, 'About page created successfully', 201)
})
