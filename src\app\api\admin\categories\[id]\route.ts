import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/categories/[id] - Get a specific category
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const category = await prisma.categories.findUnique({
    where: { id: Number(id) },
    include: {
      services: {
        select: {
          id: true,
          name: true,
          isactive: true,
          price: true
        }
      }
    }
  })

  if (!category) {
    throw new ApiError('Category not found', 404)
  }

  return successResponse(category)
})

// PUT /api/admin/categories/[id] - Update a category
export const PUT = with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.category.update)
  const data = await validate(request)

  const { id } = await params

  // Prevent setting parent to self or creating circular references
  if (data.parentId === id) {
    throw new ApiError('Category cannot be its own parent', 400)
  }

  const category = await prisma.category.update({
    where: { id },
    data,
    include: {
      parent: true,
      children: true,
      services: {
        select: {
          id: true,
          name: true,
          isactive: true,
          price: true
        }
      }
    }
  })

  return successResponse(category, 'Category updated successfully')
})

// DELETE /api/admin/categories/[id] - Delete a category
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if category has services
  const servicesCount = await prisma.service.count({
    where: { categoryId: id }
  })

  if (servicesCount > 0) {
    throw new ApiError('Cannot delete category with associated services', 400)
  }

  // Check if category has children
  const childrenCount = await prisma.category.count({
    where: { parentId: id }
  })

  if (childrenCount > 0) {
    throw new ApiError('Cannot delete category with child categories', 400)
  }

  await prisma.category.delete({
    where: { id }
  })

  return successResponse(null, 'Category deleted successfully')
})
