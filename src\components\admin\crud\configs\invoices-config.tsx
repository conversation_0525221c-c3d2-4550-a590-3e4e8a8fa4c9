import { CrudConfig } from '../types'

interface Invoice {
  id: string
  clientId: string
  projectId?: string
  orderId?: string
  contractId?: string
  invoiceNumber: string
  description?: string
  subtotal: number
  taxAmount: number
  totalAmount: number
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  issueDate: string
  dueDate: string
  paidAt?: string
  createdAt: string
  updatedAt: string
  client: {
    id: string
    companyName: string
    contactName: string
    contactEmail: string
  }
  project?: {
    id: string
    name: string
    status: string
  }
  order?: {
    id: string
    orderNumber: string
    status: string
  }
  contract?: {
    id: string
    title: string
    status: string
  }
  items: Array<{
    id: string
    description: string
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
  payments: Array<{
    id: string
    amount: number
    status: string
    paidAt?: string
  }>
}

export const invoicesConfig: CrudConfig<Invoice> = {
  title: 'Invoices',
  description: 'Manage client invoices, billing, and payment tracking',
  endpoint: 'invoices',
  
  columns: [
    {
      key: 'invoiceNumber',
      label: 'Invoice #',
      sortable: true,
      searchable: true,
      renderType: 'text',
      width: '120px'
    },
    {
      key: 'client.companyName',
      label: 'Client',
      sortable: true,
      searchable: true,
      renderType: 'text'
    },
    {
      key: 'project.name',
      label: 'Project',
      sortable: true,
      renderType: 'text'
    },
    {
      key: 'totalAmount',
      label: 'Amount',
      sortable: true,
      renderType: 'currency'
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      renderType: 'status',
      renderProps: {
        statusColors: {
          DRAFT: 'bg-gray-100 text-gray-800',
          SENT: 'bg-blue-100 text-blue-800',
          PAID: 'bg-green-100 text-green-800',
          OVERDUE: 'bg-red-100 text-red-800',
          CANCELLED: 'bg-red-100 text-red-800'
        }
      }
    },
    {
      key: 'dueDate',
      label: 'Due Date',
      sortable: true,
      renderType: 'date'
    },
    {
      key: 'issueDate',
      label: 'Issue Date',
      sortable: true,
      renderType: 'date',
      width: '120px'
    }
  ],

  fields: [
    {
      name: 'invoiceNumber',
      label: 'Invoice Number',
      type: 'text',
      required: true,
      placeholder: 'INV-2024-001',
      validation: {
        minLength: 1,
        maxLength: 50
      }
    },
    {
      name: 'clientId',
      label: 'Client',
      type: 'select',
      required: true,
      options: [], // Will be populated dynamically
      placeholder: 'Select a client'
    },
    {
      name: 'projectId',
      label: 'Project',
      type: 'select',
      options: [], // Will be populated dynamically
      placeholder: 'Select a project (optional)'
    },
    {
      name: 'orderId',
      label: 'Order',
      type: 'select',
      options: [], // Will be populated dynamically
      placeholder: 'Select an order (optional)'
    },
    {
      name: 'contractId',
      label: 'Contract',
      type: 'select',
      options: [], // Will be populated dynamically
      placeholder: 'Select a contract (optional)'
    },
    {
      name: 'description',
      label: 'Description',
      type: 'textarea',
      placeholder: 'Invoice description or notes',
      rows: 3
    },
    {
      name: 'subtotal',
      label: 'Subtotal',
      type: 'number',
      required: true,
      placeholder: '0.00',
      validation: {
        min: 0,
        step: 0.01
      }
    },
    {
      name: 'taxAmount',
      label: 'Tax Amount',
      type: 'number',
      placeholder: '0.00',
      defaultValue: 0,
      validation: {
        min: 0,
        step: 0.01
      }
    },
    {
      name: 'totalAmount',
      label: 'Total Amount',
      type: 'number',
      required: true,
      placeholder: '0.00',
      validation: {
        min: 0,
        step: 0.01
      }
    },
    {
      name: 'status',
      label: 'Status',
      type: 'select',
      required: true,
      defaultValue: 'DRAFT',
      options: [
        { value: 'DRAFT', label: 'Draft' },
        { value: 'SENT', label: 'Sent' },
        { value: 'PAID', label: 'Paid' },
        { value: 'OVERDUE', label: 'Overdue' },
        { value: 'CANCELLED', label: 'Cancelled' }
      ]
    },
    {
      name: 'issueDate',
      label: 'Issue Date',
      type: 'date',
      required: true,
      defaultValue: new Date().toISOString().split('T')[0]
    },
    {
      name: 'dueDate',
      label: 'Due Date',
      type: 'date',
      required: true
    },
    {
      name: 'paidAt',
      label: 'Paid Date',
      type: 'date',
      placeholder: 'Date when payment was received'
    }
  ],

  filters: [
    {
      name: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: '', label: 'All Statuses' },
        { value: 'DRAFT', label: 'Draft' },
        { value: 'SENT', label: 'Sent' },
        { value: 'PAID', label: 'Paid' },
        { value: 'OVERDUE', label: 'Overdue' },
        { value: 'CANCELLED', label: 'Cancelled' }
      ]
    },
    {
      name: 'client',
      label: 'Client',
      type: 'select',
      options: [] // Will be populated dynamically
    },
    {
      name: 'dateRange',
      label: 'Date Range',
      type: 'select',
      options: [
        { value: '', label: 'All Time' },
        { value: 'today', label: 'Today' },
        { value: 'week', label: 'This Week' },
        { value: 'month', label: 'This Month' },
        { value: 'quarter', label: 'This Quarter' },
        { value: 'year', label: 'This Year' }
      ]
    }
  ],

  actions: [
    {
      label: 'View',
      icon: 'EyeIcon',
      action: 'view',
      variant: 'secondary',
      tooltip: 'View invoice details'
    },
    {
      label: 'Edit',
      icon: 'PencilIcon',
      action: 'edit',
      variant: 'primary',
      tooltip: 'Edit invoice'
    },
    {
      label: 'Download PDF',
      icon: 'ArrowDownTrayIcon',
      action: 'download',
      variant: 'secondary',
      tooltip: 'Download invoice as PDF'
    },
    {
      label: 'Send',
      icon: 'PaperAirplaneIcon',
      action: 'send',
      variant: 'success',
      tooltip: 'Send invoice to client'
    },
    {
      label: 'Mark Paid',
      icon: 'CheckIcon',
      action: 'mark-paid',
      variant: 'success',
      tooltip: 'Mark as paid'
    },
    {
      label: 'Delete',
      icon: 'TrashIcon',
      action: 'delete',
      variant: 'danger',
      requiresConfirmation: true,
      confirmationMessage: 'Are you sure you want to delete this invoice? This action cannot be undone.',
      tooltip: 'Delete invoice'
    }
  ],

  bulkActions: [
    {
      label: 'Mark as Sent',
      action: 'mark-sent',
      variant: 'primary'
    },
    {
      label: 'Mark as Paid',
      action: 'mark-paid',
      variant: 'success'
    },
    {
      label: 'Export Selected',
      action: 'export',
      variant: 'secondary'
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search invoices by number, client, or description...',
  defaultSort: { field: 'createdAt', direction: 'desc' },
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true
}
