import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// Component types
export type ComponentType =
  | 'text'
  | 'image'
  | 'button'
  | 'container'
  | 'grid'
  | 'column'
  | 'row'
  | 'hero'
  | 'card'
  | 'list'
  | 'form'
  | 'video'
  | 'embed'
  | 'spacer'
  | 'divider'
  | 'navbar'
  | 'footer'
  | 'testimonial'
  | 'pricing'
  | 'features'
  | 'cta'
  | 'stats'
  | 'team'
  | 'gallery'
  | 'blog'
  | 'newsletter'

// Component data structure
export interface Component {
  id: string
  type: ComponentType
  name?: string
  content: Record<string, any>
  styles: Record<string, any>
  position: {
    x: number
    y: number
    width: number
    height: number
  }
  parentId?: string
  children?: string[]
  order: number
  isVisible: boolean
  isLocked: boolean
  isSelected: boolean
}

// Page data structure
export interface Page {
  id: string
  title: string
  slug: string
  description?: string
  metaTitle?: string
  metaDescription?: string
  isPublished: boolean
  isHomePage: boolean
  layout: Component[]
  styles: Record<string, any>
  seoSettings: Record<string, any>
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
}

// Editor modes
export type EditorMode = 'edit' | 'preview' | 'responsive'
export type ViewportSize = 'desktop' | 'tablet' | 'mobile'

// Page builder state
interface PageBuilderState {
  // Current page and components
  currentPage: Page | null
  components: Component[]
  selectedComponentIds: string[]
  hoveredComponentId: string | null
  
  // Editor state
  mode: EditorMode
  viewport: ViewportSize
  isLoading: boolean
  isDirty: boolean
  
  // History for undo/redo
  history: Component[][]
  historyIndex: number
  maxHistorySize: number
  
  // UI state
  showGrid: boolean
  snapToGrid: boolean
  gridSize: number
  showRulers: boolean
  showOutlines: boolean
  
  // Panel states
  leftPanelOpen: boolean
  rightPanelOpen: boolean
  leftPanelWidth: number
  rightPanelWidth: number
  
  // Database binding
  availableTables: string[]
  selectedTable: string | null
  tableFields: Record<string, any>[]
}

// Actions interface
interface PageBuilderActions {
  // Page management
  setCurrentPage: (page: Page | null) => void
  updatePageMeta: (meta: Partial<Page>) => void
  
  // Component management
  addComponent: (component: Omit<Component, 'id'>) => void
  updateComponent: (id: string, updates: Partial<Component>) => void
  deleteComponent: (id: string) => void
  duplicateComponent: (id: string) => void
  moveComponent: (id: string, newPosition: { x: number; y: number }) => void
  resizeComponent: (id: string, newSize: { width: number; height: number }) => void
  
  // Selection management
  selectComponent: (id: string, multiSelect?: boolean) => void
  selectMultipleComponents: (ids: string[]) => void
  clearSelection: () => void
  setHoveredComponent: (id: string | null) => void
  
  // Editor state
  setMode: (mode: EditorMode) => void
  setViewport: (viewport: ViewportSize) => void
  setLoading: (loading: boolean) => void
  setDirty: (dirty: boolean) => void
  
  // History management
  saveToHistory: () => void
  undo: () => void
  redo: () => void
  canUndo: () => boolean
  canRedo: () => boolean
  
  // UI state
  toggleGrid: () => void
  toggleSnapToGrid: () => void
  setGridSize: (size: number) => void
  toggleRulers: () => void
  toggleOutlines: () => void
  
  // Panel management
  toggleLeftPanel: () => void
  toggleRightPanel: () => void
  setLeftPanelWidth: (width: number) => void
  setRightPanelWidth: (width: number) => void
  
  // Database integration
  setAvailableTables: (tables: string[]) => void
  setSelectedTable: (table: string | null) => void
  setTableFields: (fields: Record<string, any>[]) => void
  
  // Utility functions
  getComponentById: (id: string) => Component | undefined
  getSelectedComponents: () => Component[]
  getComponentChildren: (id: string) => Component[]
  getComponentParent: (id: string) => Component | undefined
  
  // Reset state
  reset: () => void
}

// Combined store type
type PageBuilderStore = PageBuilderState & PageBuilderActions

// Initial state
const initialState: PageBuilderState = {
  currentPage: null,
  components: [],
  selectedComponentIds: [],
  hoveredComponentId: null,
  
  mode: 'edit',
  viewport: 'desktop',
  isLoading: false,
  isDirty: false,
  
  history: [[]],
  historyIndex: 0,
  maxHistorySize: 50,
  
  showGrid: true,
  snapToGrid: true,
  gridSize: 10,
  showRulers: false,
  showOutlines: true,
  
  leftPanelOpen: true,
  rightPanelOpen: true,
  leftPanelWidth: 280,
  rightPanelWidth: 320,
  
  availableTables: [],
  selectedTable: null,
  tableFields: []
}

// Create the store
export const usePageBuilderStore = create<PageBuilderStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      // Page management
      setCurrentPage: (page) => {
        const components = Array.isArray(page?.layout) ? page.layout : []
        set({
          currentPage: page,
          components: components,
          selectedComponentIds: [],
          isDirty: false
        })
      },
      
      updatePageMeta: (meta) => set((state) => ({
        currentPage: state.currentPage ? { ...state.currentPage, ...meta } : null,
        isDirty: true
      })),
      
      // Component management
      addComponent: (componentData) => {
        const id = `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        const component: Component = {
          ...componentData,
          id,
          isSelected: false,
          isLocked: false
        }
        
        set((state) => {
          const newComponents = [...state.components, component]
          get().saveToHistory()
          return {
            components: newComponents,
            selectedComponentIds: [id],
            isDirty: true
          }
        })
      },
      
      updateComponent: (id, updates) => set((state) => {
        const componentIndex = state.components.findIndex(c => c.id === id)
        if (componentIndex === -1) return state
        
        const updatedComponents = [...state.components]
        updatedComponents[componentIndex] = { ...updatedComponents[componentIndex], ...updates }
        
        return {
          components: updatedComponents,
          isDirty: true
        }
      }),
      
      deleteComponent: (id) => set((state) => {
        get().saveToHistory()
        return {
          components: state.components.filter(c => c.id !== id),
          selectedComponentIds: state.selectedComponentIds.filter(selectedId => selectedId !== id),
          isDirty: true
        }
      }),
      
      duplicateComponent: (id) => {
        const component = get().getComponentById(id)
        if (!component) return
        
        const duplicatedComponent = {
          ...component,
          position: {
            ...component.position,
            x: component.position.x + 20,
            y: component.position.y + 20
          }
        }
        
        get().addComponent(duplicatedComponent)
      },
      
      moveComponent: (id, newPosition) => {
        const component = get().getComponentById(id)
        if (component) {
          get().updateComponent(id, { position: { ...component.position, ...newPosition } })
        }
      },

      resizeComponent: (id, newSize) => {
        const component = get().getComponentById(id)
        if (component) {
          get().updateComponent(id, { position: { ...component.position, ...newSize } })
        }
      },
      
      // Selection management
      selectComponent: (id, multiSelect = false) => set((state) => {
        if (multiSelect) {
          const isSelected = state.selectedComponentIds.includes(id)
          return {
            selectedComponentIds: isSelected 
              ? state.selectedComponentIds.filter(selectedId => selectedId !== id)
              : [...state.selectedComponentIds, id]
          }
        }
        return { selectedComponentIds: [id] }
      }),
      
      selectMultipleComponents: (ids) => set({ selectedComponentIds: ids }),
      
      clearSelection: () => set({ selectedComponentIds: [] }),
      
      setHoveredComponent: (id) => set({ hoveredComponentId: id }),
      
      // Editor state
      setMode: (mode) => set({ mode }),
      setViewport: (viewport) => set({ viewport }),
      setLoading: (loading) => set({ isLoading: loading }),
      setDirty: (dirty) => set({ isDirty: dirty }),
      
      // History management
      saveToHistory: () => set((state) => {
        const newHistory = state.history.slice(0, state.historyIndex + 1)
        newHistory.push([...state.components])
        
        if (newHistory.length > state.maxHistorySize) {
          newHistory.shift()
        }
        
        return {
          history: newHistory,
          historyIndex: newHistory.length - 1
        }
      }),
      
      undo: () => set((state) => {
        if (state.historyIndex > 0) {
          const newIndex = state.historyIndex - 1
          return {
            components: [...state.history[newIndex]],
            historyIndex: newIndex,
            selectedComponentIds: [],
            isDirty: true
          }
        }
        return state
      }),
      
      redo: () => set((state) => {
        if (state.historyIndex < state.history.length - 1) {
          const newIndex = state.historyIndex + 1
          return {
            components: [...state.history[newIndex]],
            historyIndex: newIndex,
            selectedComponentIds: [],
            isDirty: true
          }
        }
        return state
      }),
      
      canUndo: () => get().historyIndex > 0,
      canRedo: () => get().historyIndex < get().history.length - 1,
      
      // UI state
      toggleGrid: () => set((state) => ({ showGrid: !state.showGrid })),
      toggleSnapToGrid: () => set((state) => ({ snapToGrid: !state.snapToGrid })),
      setGridSize: (size) => set({ gridSize: size }),
      toggleRulers: () => set((state) => ({ showRulers: !state.showRulers })),
      toggleOutlines: () => set((state) => ({ showOutlines: !state.showOutlines })),
      
      // Panel management
      toggleLeftPanel: () => set((state) => ({ leftPanelOpen: !state.leftPanelOpen })),
      toggleRightPanel: () => set((state) => ({ rightPanelOpen: !state.rightPanelOpen })),
      setLeftPanelWidth: (width) => set({ leftPanelWidth: width }),
      setRightPanelWidth: (width) => set({ rightPanelWidth: width }),
      
      // Database integration
      setAvailableTables: (tables) => set({ availableTables: tables }),
      setSelectedTable: (table) => set({ selectedTable: table }),
      setTableFields: (fields) => set({ tableFields: fields }),
      
      // Utility functions
      getComponentById: (id) => get().components.find(c => c.id === id),
      getSelectedComponents: () => get().components.filter(c => get().selectedComponentIds.includes(c.id)),
      getComponentChildren: (id) => get().components.filter(c => c.parentId === id),
      getComponentParent: (id) => {
        const component = get().getComponentById(id)
        return component?.parentId ? get().getComponentById(component.parentId) : undefined
      },
      
      // Reset state
      reset: () => set(initialState)
    }),
    { name: 'page-builder-store' }
  )
)
