'use client'

import { useState, useEffect, useRef } from 'react'
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline'

interface ProjectSearchProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  debounceMs?: number
}

export default function ProjectSearch({
  value,
  onChange,
  placeholder = 'Search projects...',
  debounceMs = 300
}: ProjectSearchProps) {
  const [localValue, setLocalValue] = useState(value)
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  // Debounce the search
  useEffect(() => {
    const timer = setTimeout(() => {
      onChange(localValue)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [localValue, onChange, debounceMs])

  // Sync with external value changes
  useEffect(() => {
    setLocalValue(value)
  }, [value])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Focus search on Ctrl/Cmd + K
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault()
        inputRef.current?.focus()
      }
      // Clear search on Escape
      if (event.key === 'Escape' && inputRef.current === document.activeElement) {
        setLocalValue('')
        inputRef.current?.blur()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const handleClear = () => {
    setLocalValue('')
    inputRef.current?.focus()
  }

  return (
    <div className="relative">
      {/* Search Icon */}
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
      </div>

      {/* Search Input */}
      <input
        ref={inputRef}
        type="text"
        value={localValue}
        onChange={(e) => setLocalValue(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        placeholder={`${placeholder} (Ctrl+K)`}
        className={`
          block w-full pl-10 pr-12 py-3 
          border border-gray-300 rounded-lg 
          focus:ring-2 focus:ring-blue-500 focus:border-transparent 
          transition-all duration-200
          ${isFocused ? 'shadow-lg' : 'shadow-sm'}
        `}
      />

      {/* Clear Button */}
      {localValue && (
        <div className="absolute inset-y-0 right-0 flex items-center">
          <button
            onClick={handleClear}
            className="p-2 mr-1 text-gray-400 hover:text-gray-600 transition-colors rounded-md"
            title="Clear search"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Search Status */}
      {localValue !== value && (
        <div className="absolute -bottom-6 left-0">
          <span className="text-xs text-gray-500">Searching...</span>
        </div>
      )}
    </div>
  )
}
