'use client';

import { LegalPagesManager } from '@/components/admin/legal-pages/legal-pages-manager';
import { CrudConfig } from '@/components/admin/crud/types';

interface LegalPage {
  id: number
  title: string
  slug: string
  metaDescription?: string
  content: string
  isActive: boolean
  displayOrder: number
  lastModified: string
  modifiedBy?: string
  createdAt: string
  updatedAt: string
  sections?: any[]
  _count?: {
    sections: number
  }
}

const legalPageConfig: CrudConfig<LegalPage> = {
  title: 'Legal Pages',
  description: 'Manage privacy policies, terms of service, and other legal documentation',
  endpoint: 'legal-pages', // API endpoint

  // Table columns configuration
  columns: [
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      searchable: true,
      width: 'auto'
    },
    {
      key: 'slug',
      label: 'Slug',
      sortable: true,
      searchable: true,
      width: '200px'
    },
    {
      key: 'metaDescription',
      label: 'Meta Description',
      sortable: false,
      searchable: true,
      width: '250px'
    },
    {
      key: 'displayOrder',
      label: 'Order',
      sortable: true,
      searchable: false,
      width: '80px'
    },
    {
      key: 'lastModified',
      label: 'Last Modified',
      sortable: true,
      searchable: false,
      width: '150px'
    },
    {
      key: 'modifiedBy',
      label: 'Modified By',
      sortable: true,
      searchable: true,
      width: '120px'
    },
    {
      key: 'isActive',
      label: 'Status',
      sortable: true,
      searchable: false,
      width: '100px'
    }
  ],
  fields: [
    {
      name: 'title',
      label: 'Title',
      type: 'text',
      required: true,
      placeholder: 'Enter page title'
    },
    {
      name: 'slug',
      label: 'Slug',
      type: 'text',
      required: true,
      placeholder: 'page-url-slug'
    },
    {
      name: 'metaDescription',
      label: 'Meta Description',
      type: 'textarea',
      placeholder: 'Brief description for search engines',
      rows: 2
    },
    {
      name: 'content',
      label: 'Content',
      type: 'textarea',
      required: true,
      placeholder: 'Enter the legal page content (HTML supported)',
      rows: 8
    },
    {
      name: 'displayOrder',
      label: 'Display Order',
      type: 'number',
      defaultValue: 0,
      placeholder: '0'
    },
    {
      name: 'modifiedBy',
      label: 'Modified By',
      type: 'text',
      placeholder: 'admin'
    },
    {
      name: 'isActive',
      label: 'Active Status',
      type: 'checkbox',
      defaultValue: true,
    },
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search legal pages by title, slug, content...',
  defaultSort: { field: 'updatedat', direction: 'desc' }, // Sort by Last Active (most recent)
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['title', 'slug', 'metaDescription', 'displayOrder', 'lastModified', 'modifiedBy', 'isActive']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 2,
    sections: [
      {
        title: 'Basic Information',
        fields: ['title', 'slug', 'metaDescription']
      },
      {
        title: 'Content',
        fields: ['content']
      },
      {
        title: 'Settings',
        fields: ['displayOrder', 'modifiedBy', 'isActive']
      }
    ]
  }
};
export default function LegalPagesPage() {
  return <LegalPagesManager config={legalPageConfig} />
}
