import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { Component, Page } from './store'

// Template types
export interface PageTemplate {
  id: string
  name: string
  description: string
  category: TemplateCategory
  thumbnail: string
  previewUrl?: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  tags: string[]
  components: Component[]
  styles: Record<string, any>
  seoSettings: {
    title: string
    description: string
    keywords: string[]
  }
  isPopular: boolean
  usageCount: number
  createdAt: Date
  updatedAt: Date
  author?: string
  version: string
}

export type TemplateCategory = 
  | 'landing'
  | 'business'
  | 'portfolio'
  | 'blog'
  | 'ecommerce'
  | 'agency'
  | 'personal'
  | 'nonprofit'
  | 'education'
  | 'healthcare'
  | 'restaurant'
  | 'real-estate'

export interface TemplateFilter {
  category?: TemplateCategory | 'all'
  difficulty?: 'beginner' | 'intermediate' | 'advanced' | 'all'
  tags?: string[]
  isPopular?: boolean
  searchTerm?: string
}

// Template store
interface TemplateStore {
  // State
  templates: PageTemplate[]
  favoriteTemplates: string[]
  recentTemplates: string[]
  isLoading: boolean
  error: string | null
  
  // Actions
  loadTemplates: () => Promise<void>
  getTemplateById: (id: string) => PageTemplate | undefined
  getTemplatesByCategory: (category: TemplateCategory) => PageTemplate[]
  getPopularTemplates: () => PageTemplate[]
  getRecentTemplates: () => PageTemplate[]
  searchTemplates: (filter: TemplateFilter) => PageTemplate[]
  
  // Favorites
  addToFavorites: (templateId: string) => void
  removeFromFavorites: (templateId: string) => void
  isFavorite: (templateId: string) => boolean
  
  // Recent templates
  addToRecent: (templateId: string) => void
  clearRecent: () => void
  
  // Template usage
  incrementUsage: (templateId: string) => void
  
  // Template creation
  createTemplate: (template: Omit<PageTemplate, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>) => PageTemplate
  updateTemplate: (id: string, updates: Partial<PageTemplate>) => void
  deleteTemplate: (id: string) => void
}

export const useTemplateStore = create<TemplateStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      templates: [],
      favoriteTemplates: JSON.parse(localStorage.getItem('pageBuilder_favoriteTemplates') || '[]'),
      recentTemplates: JSON.parse(localStorage.getItem('pageBuilder_recentTemplates') || '[]'),
      isLoading: false,
      error: null,

      // Load templates
      loadTemplates: async () => {
        set({ isLoading: true, error: null })
        
        try {
          // In a real app, this would fetch from an API
          // For now, we'll use mock data
          const mockTemplates = await import('./template-definitions').then(m => m.defaultTemplates)
          set({ templates: mockTemplates, isLoading: false })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to load templates',
            isLoading: false 
          })
        }
      },

      // Get template by ID
      getTemplateById: (id) => {
        return get().templates.find(template => template.id === id)
      },

      // Get templates by category
      getTemplatesByCategory: (category) => {
        return get().templates.filter(template => template.category === category)
      },

      // Get popular templates
      getPopularTemplates: () => {
        return get().templates
          .filter(template => template.isPopular)
          .sort((a, b) => b.usageCount - a.usageCount)
          .slice(0, 6)
      },

      // Get recent templates
      getRecentTemplates: () => {
        const recentIds = get().recentTemplates
        const templates = get().templates
        return recentIds
          .map(id => templates.find(t => t.id === id))
          .filter(Boolean) as PageTemplate[]
      },

      // Search templates
      searchTemplates: (filter) => {
        let filtered = get().templates

        // Category filter
        if (filter.category && filter.category !== 'all') {
          filtered = filtered.filter(t => t.category === filter.category)
        }

        // Difficulty filter
        if (filter.difficulty && filter.difficulty !== 'all') {
          filtered = filtered.filter(t => t.difficulty === filter.difficulty)
        }

        // Tags filter
        if (filter.tags && filter.tags.length > 0) {
          filtered = filtered.filter(t => 
            filter.tags!.some(tag => t.tags.includes(tag))
          )
        }

        // Popular filter
        if (filter.isPopular) {
          filtered = filtered.filter(t => t.isPopular)
        }

        // Search term
        if (filter.searchTerm) {
          const term = filter.searchTerm.toLowerCase()
          filtered = filtered.filter(t =>
            t.name.toLowerCase().includes(term) ||
            t.description.toLowerCase().includes(term) ||
            t.tags.some(tag => tag.toLowerCase().includes(term))
          )
        }

        return filtered.sort((a, b) => {
          // Sort by popularity first, then by usage count
          if (a.isPopular && !b.isPopular) return -1
          if (!a.isPopular && b.isPopular) return 1
          return b.usageCount - a.usageCount
        })
      },

      // Favorites management
      addToFavorites: (templateId) => {
        const favorites = get().favoriteTemplates
        if (!favorites.includes(templateId)) {
          const newFavorites = [...favorites, templateId]
          set({ favoriteTemplates: newFavorites })
          localStorage.setItem('pageBuilder_favoriteTemplates', JSON.stringify(newFavorites))
        }
      },

      removeFromFavorites: (templateId) => {
        const favorites = get().favoriteTemplates
        const newFavorites = favorites.filter(id => id !== templateId)
        set({ favoriteTemplates: newFavorites })
        localStorage.setItem('pageBuilder_favoriteTemplates', JSON.stringify(newFavorites))
      },

      isFavorite: (templateId) => {
        return get().favoriteTemplates.includes(templateId)
      },

      // Recent templates management
      addToRecent: (templateId) => {
        const recent = get().recentTemplates
        const newRecent = [templateId, ...recent.filter(id => id !== templateId)].slice(0, 10)
        set({ recentTemplates: newRecent })
        localStorage.setItem('pageBuilder_recentTemplates', JSON.stringify(newRecent))
      },

      clearRecent: () => {
        set({ recentTemplates: [] })
        localStorage.removeItem('pageBuilder_recentTemplates')
      },

      // Usage tracking
      incrementUsage: (templateId) => {
        const templates = get().templates
        const updatedTemplates = templates.map(template =>
          template.id === templateId
            ? { ...template, usageCount: template.usageCount + 1 }
            : template
        )
        set({ templates: updatedTemplates })
      },

      // Template CRUD
      createTemplate: (templateData) => {
        const newTemplate: PageTemplate = {
          ...templateData,
          id: `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date(),
          updatedAt: new Date(),
          usageCount: 0
        }
        
        set(state => ({
          templates: [...state.templates, newTemplate]
        }))
        
        return newTemplate
      },

      updateTemplate: (id, updates) => {
        set(state => ({
          templates: state.templates.map(template =>
            template.id === id
              ? { ...template, ...updates, updatedAt: new Date() }
              : template
          )
        }))
      },

      deleteTemplate: (id) => {
        set(state => ({
          templates: state.templates.filter(template => template.id !== id),
          favoriteTemplates: state.favoriteTemplates.filter(fav => fav !== id),
          recentTemplates: state.recentTemplates.filter(recent => recent !== id)
        }))
      }
    }),
    {
      name: 'template-store'
    }
  )
)
