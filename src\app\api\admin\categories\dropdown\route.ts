import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin
} from '@/lib/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/categories/dropdown - Get categories for dropdown (admin only)
export const GET = withError<PERSON>andler(async (request: NextRequest) => {
  await requireAdmin(request)

  const categories = await prisma.categories.findMany({
    where: { isactive: true },
    select: {
      id: true,
      categname: true,
      categdesc: true,
    },
    orderBy: {
      displayorder: 'asc'
    }
  })

  // Transform the data for frontend
  const transformedCategories = categories.map(cat => ({
    id: Number(cat.id),
    name: cat.categname,
    description: cat.categdesc,
  }))

  return successResponse(transformedCategories, 'Categories retrieved successfully')
})
