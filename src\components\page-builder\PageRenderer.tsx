'use client'

import React from 'react'
import { Component } from '@/lib/page-builder/store'
import ComponentRenderer from './ComponentRenderer'

interface PageRendererProps {
  components: Component[]
  styles?: Record<string, any>
  className?: string
  isPreview?: boolean
}

const PageRenderer: React.FC<PageRendererProps> = ({
  components,
  styles = {},
  className = '',
  isPreview = false
}) => {
  // Sort components by order and filter visible ones
  const visibleComponents = components
    .filter(component => component.isVisible)
    .sort((a, b) => a.order - b.order)
  
  // Build component tree (handle parent-child relationships)
  const buildComponentTree = (components: Component[]): Component[] => {
    const componentMap = new Map<string, Component>()
    const rootComponents: Component[] = []
    
    // Create a map of all components
    components.forEach(component => {
      componentMap.set(component.id, { ...component, children: [] })
    })
    
    // Build the tree structure
    components.forEach(component => {
      const comp = componentMap.get(component.id)!
      
      if (component.parentId) {
        const parent = componentMap.get(component.parentId)
        if (parent) {
          if (!parent.children) parent.children = []
          parent.children.push(comp.id)
        } else {
          // Parent not found, treat as root
          rootComponents.push(comp)
        }
      } else {
        rootComponents.push(comp)
      }
    })
    
    return rootComponents
  }
  
  // Render component with its children
  const renderComponentWithChildren = (component: Component): React.ReactNode => {
    const children = component.children?.map(childId => {
      const childComponent = visibleComponents.find(c => c.id === childId)
      return childComponent ? renderComponentWithChildren(childComponent) : null
    }).filter(Boolean)
    
    return (
      <ComponentRenderer
        key={component.id}
        component={component}
        isEditing={false}
        isSelected={false}
        isHovered={false}
      >
        {children}
      </ComponentRenderer>
    )
  }
  
  const rootComponents = buildComponentTree(visibleComponents)
  
  // Apply page-level styles
  const pageStyles: React.CSSProperties = {
    ...styles,
    position: 'relative',
    minHeight: '100vh'
  }
  
  return (
    <div 
      className={`page-renderer ${className}`}
      style={pageStyles}
    >
      {rootComponents.length === 0 ? (
        <div className="flex items-center justify-center min-h-screen text-gray-500">
          <div className="text-center">
            <div className="text-6xl mb-4">📄</div>
            <h2 className="text-2xl font-medium mb-2">Empty Page</h2>
            <p>This page doesn't have any content yet.</p>
          </div>
        </div>
      ) : (
        rootComponents.map(renderComponentWithChildren)
      )}
      
      {/* Preview indicator */}
      {isPreview && (
        <div className="fixed top-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg z-50">
          Preview Mode
        </div>
      )}
    </div>
  )
}

export default PageRenderer
