'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline'
import { useCrud } from './crud-context'
import { CrudConfig, ViewMode, DisplayDensity } from './types'

interface CrudViewControlsProps<T> {
  config: CrudConfig<T>
}

export function CrudViewControls<T>({ config }: CrudViewControlsProps<T>) {
  const { state, actions } = useCrud<T>()
  const [showColumnMenu, setShowColumnMenu] = useState(false)
  const [showDensityMenu, setShowDensityMenu] = useState(false)

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    card: RectangleStackIcon,
  }

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  }

  const handleViewModeChange = (mode: ViewMode) => {
    actions.setViewMode(mode)
  }

  const handleDensityChange = (density: DisplayDensity) => {
    actions.setDisplayDensity(density)
    setShowDensityMenu(false)
  }

  const handleColumnToggle = (columnKey: string) => {
    const isVisible = state.viewSettings.visibleColumns.includes(columnKey)
    actions.setColumnVisibility(columnKey, !isVisible)
  }

  const getVisibleColumnsCount = () => {
    return state.viewSettings.visibleColumns.length
  }

  const getTotalColumnsCount = () => {
    return config.columns.filter(col => col.hideable !== false).length
  }

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.dropdown-container')) {
        setShowDensityMenu(false)
        setShowColumnMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  if (!config.enableViewControls && !config.enableDensityControls && !config.enableColumnVisibility) {
    return null
  }

  return (
    <div className="flex items-center space-x-2 bg-white border border-gray-200 rounded-lg p-1">
      {/* View Mode Controls */}
      {config.enableViewControls && (
        <div className="flex items-center bg-gray-50 rounded-md p-1">
          {Object.entries(viewModeIcons).map(([mode, Icon]) => (
            <button
              key={mode}
              onClick={() => handleViewModeChange(mode as ViewMode)}
              className={`p-2 rounded transition-colors ${
                state.viewSettings.mode === mode
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
              title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
            >
              <Icon className="h-4 w-4" />
            </button>
          ))}
        </div>
      )}

      {/* Display Density Controls */}
      {config.enableDensityControls && (
        <div className="relative dropdown-container">
          <button
            onClick={() => setShowDensityMenu(!showDensityMenu)}
            className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md border border-gray-300 transition-colors min-w-[140px] justify-between"
            title="Display density"
          >
            <div className="flex items-center space-x-2">
              <AdjustmentsHorizontalIcon className="h-4 w-4" />
              <span>{densityLabels[state.viewSettings.density]}</span>
            </div>
            <ChevronDownIcon className={`h-3 w-3 transition-transform ${showDensityMenu ? 'rotate-180' : ''}`} />
          </button>

          <AnimatePresence>
            {showDensityMenu && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]"
              >
                <div className="py-1">
                  {Object.entries(densityLabels).map(([density, label]) => (
                    <button
                      key={density}
                      onClick={() => handleDensityChange(density as DisplayDensity)}
                      className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors flex items-center justify-between ${
                        state.viewSettings.density === density
                          ? 'bg-blue-50 text-blue-700'
                          : 'text-gray-700'
                      }`}
                    >
                      <span>{label}</span>
                      {state.viewSettings.density === density && (
                        <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      )}
                    </button>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}

      {/* Column Visibility Controls */}
      {config.enableColumnVisibility && (
        <div className="relative dropdown-container">
          <button
            onClick={() => setShowColumnMenu(!showColumnMenu)}
            className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md border border-gray-300 transition-colors min-w-[160px] justify-between"
            title="Column visibility"
          >
            <div className="flex items-center space-x-2">
              <EyeIcon className="h-4 w-4" />
              <span>Columns ({getVisibleColumnsCount()}/{getTotalColumnsCount()})</span>
            </div>
            <ChevronDownIcon className={`h-3 w-3 transition-transform ${showColumnMenu ? 'rotate-180' : ''}`} />
          </button>

          <AnimatePresence>
            {showColumnMenu && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[220px] max-h-64 overflow-y-auto"
              >
                <div className="p-3">
                  <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-100">
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Column Visibility
                    </span>
                    <button
                      onClick={actions.resetViewSettings}
                      className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                    >
                      Reset
                    </button>
                  </div>

                  <div className="space-y-1">
                    {config.columns
                      .filter(col => col.hideable !== false)
                      .map((column) => {
                        const isVisible = state.viewSettings.visibleColumns.includes(column.key as string)
                        return (
                          <label
                            key={column.key as string}
                            className="flex items-center space-x-3 py-2 px-2 hover:bg-gray-50 rounded cursor-pointer transition-colors"
                          >
                            <input
                              type="checkbox"
                              checked={isVisible}
                              onChange={() => handleColumnToggle(column.key as string)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
                            />
                            <span className="text-sm text-gray-700 flex-1">{column.label}</span>
                            {isVisible ? (
                              <EyeIcon className="h-4 w-4 text-green-500" />
                            ) : (
                              <EyeSlashIcon className="h-4 w-4 text-gray-400" />
                            )}
                          </label>
                        )
                      })}
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}
    </div>
  )
}
