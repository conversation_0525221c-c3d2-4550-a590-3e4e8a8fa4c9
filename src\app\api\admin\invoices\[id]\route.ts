import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/invoices/[id] - Get a specific invoice
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  const { id } = await params

  const invoice = await prisma.invoices.findUnique({
    where: { id },
    include: {
      client: true,
      project: true,
      order: true,
      contract: true,
      items: true,
      payments: true
    }
  })

  if (!invoice) {
    throw new ApiError('Invoice not found', 404)
  }

  return successResponse(invoice)
})

// PUT /api/admin/invoices/[id] - Update an invoice
export const PUT = withError<PERSON>and<PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  const { id } = await params

  const body = await request.json()
  console.log('Received invoice update data:', JSON.stringify(body, null, 2))

  const { items, ...invoiceData } = body

  // Clean the invoice data - remove fields that shouldn't be updated
  const cleanInvoiceData = {
    clientid: invoiceData.clientid,
    projectid: invoiceData.projectid,
    orderid: invoiceData.orderid,
    contid: invoiceData.contid,
    invoiceNumber: invoiceData.invoiceNumber,
    description: invoiceData.description,
    subtotal: invoiceData.subtotal,
    taxamount: invoiceData.taxamount,
    totalamount: invoiceData.totalamount,
    status: invoiceData.status,
    issueDate: invoiceData.issueDate,
    duedate: invoiceData.duedate,
    paidat: invoiceData.paidat,
  }

  // Validate invoice data
  console.log('Validating cleaned invoice data:', JSON.stringify(cleanInvoiceData, null, 2))
  const validatedData = schemas.invoice.update.parse(cleanInvoiceData)

  // Update invoice with items in a transaction
  const invoice = await prisma.$transaction(async (tx) => {
    // Update the invoice
    const updatedInvoice = await tx.invoice.update({
      where: { id },
      data: validatedData,
    })

    // Delete existing items and create new ones if provided
    await tx.invoiceItem.deleteMany({
      where: { invoiceId: id }
    })

    if (items && Array.isArray(items) && items.length > 0) {
      const validItems = items.filter(item => item.description && item.description.trim() !== '')

      if (validItems.length > 0) {
        await tx.invoiceItem.createMany({
          data: validItems.map(item => ({
            invoiceId: id,
            description: item.description,
            quantity: Number(item.quantity) || 1,
            unitPrice: Number(item.unitPrice) || 0,
            totalPrice: Number(item.totalPrice) || 0,
          }))
        })
      }
    }

    // Return the complete updated invoice with relations
    return await tx.invoice.findUnique({
      where: { id },
      include: {
        client: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        project: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        items: true,
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paidat: true
          }
        }
      }
    })
  })

  return successResponse(invoice, 'Invoice updated successfully')
})

// DELETE /api/admin/invoices/[id] - Delete an invoice
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  const { id } = await params

  // Check if invoice has payments
  const paymentsCount = await prisma.payments.count({
    where: { invoiceId: id }
  })

  if (paymentsCount > 0) {
    throw new ApiError('Cannot delete invoice with associated payments', 400)
  }

  // Delete invoice items first
  await prisma.invoicesItem.deleteMany({
    where: { invoiceId: id }
  })

  // Then delete the invoice
  await prisma.invoices.delete({
    where: { id }
  })

  return successResponse(null, 'Invoice deleted successfully')
})
