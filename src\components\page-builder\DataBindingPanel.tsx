'use client'

import React, { useState, useEffect } from 'react'
import { 
  DatabaseIcon,
  TableCellsIcon,
  MagnifyingGlassIcon,
  LinkIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'

interface DataBindingPanelProps {
  onClose: () => void
}

interface DatabaseTable {
  name: string
  displayName: string
  description: string
  fields: DatabaseField[]
  recordCount: number
}

interface DatabaseField {
  name: string
  type: string
  description?: string
  isRequired: boolean
}

const DataBindingPanel: React.FC<DataBindingPanelProps> = ({ onClose }) => {
  const [tables, setTables] = useState<DatabaseTable[]>([])
  const [selectedTable, setSelectedTable] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [previewData, setPreviewData] = useState<any[]>([])

  // Mock database tables data
  useEffect(() => {
    const mockTables: DatabaseTable[] = [
      {
        name: 'services',
        displayName: 'Services',
        description: 'Company services and offerings',
        recordCount: 12,
        fields: [
          { name: 'id', type: 'number', isRequired: true },
          { name: 'name', type: 'string', description: 'Service name', isRequired: true },
          { name: 'description', type: 'text', description: 'Service description', isRequired: true },
          { name: 'price', type: 'decimal', description: 'Service price', isRequired: true },
          { name: 'iconclass', type: 'string', description: 'Icon CSS class', isRequired: false },
          { name: 'isactive', type: 'boolean', description: 'Is service active', isRequired: true }
        ]
      },
      {
        name: 'projects',
        displayName: 'Projects',
        description: 'Portfolio projects and case studies',
        recordCount: 24,
        fields: [
          { name: 'id', type: 'number', isRequired: true },
          { name: 'name', type: 'string', description: 'Project name', isRequired: true },
          { name: 'description', type: 'text', description: 'Project description', isRequired: true },
          { name: 'imageurl', type: 'string', description: 'Project image URL', isRequired: false },
          { name: 'projecturl', type: 'string', description: 'Live project URL', isRequired: false },
          { name: 'githuburl', type: 'string', description: 'GitHub repository URL', isRequired: false },
          { name: 'isfeatured', type: 'boolean', description: 'Is featured project', isRequired: true },
          { name: 'ispublic', type: 'boolean', description: 'Is publicly visible', isRequired: true }
        ]
      },
      {
        name: 'teammembers',
        displayName: 'Team Members',
        description: 'Company team members and staff',
        recordCount: 8,
        fields: [
          { name: 'id', type: 'number', isRequired: true },
          { name: 'firstname', type: 'string', description: 'First name', isRequired: true },
          { name: 'lastname', type: 'string', description: 'Last name', isRequired: true },
          { name: 'position', type: 'string', description: 'Job position', isRequired: true },
          { name: 'email', type: 'string', description: 'Email address', isRequired: true },
          { name: 'photourl', type: 'string', description: 'Profile photo URL', isRequired: false },
          { name: 'bio', type: 'text', description: 'Biography', isRequired: false },
          { name: 'isactive', type: 'boolean', description: 'Is active employee', isRequired: true }
        ]
      },
      {
        name: 'testimonials',
        displayName: 'Testimonials',
        description: 'Client testimonials and reviews',
        recordCount: 15,
        fields: [
          { name: 'id', type: 'number', isRequired: true },
          { name: 'clientname', type: 'string', description: 'Client name', isRequired: true },
          { name: 'clienttitle', type: 'string', description: 'Client job title', isRequired: true },
          { name: 'clientcompany', type: 'string', description: 'Client company', isRequired: true },
          { name: 'content', type: 'text', description: 'Testimonial content', isRequired: true },
          { name: 'rating', type: 'number', description: 'Rating (1-5)', isRequired: true },
          { name: 'clientphotourl', type: 'string', description: 'Client photo URL', isRequired: false },
          { name: 'isfeatured', type: 'boolean', description: 'Is featured testimonial', isRequired: true }
        ]
      },
      {
        name: 'blogposts',
        displayName: 'Blog Posts',
        description: 'Blog articles and news',
        recordCount: 32,
        fields: [
          { name: 'id', type: 'number', isRequired: true },
          { name: 'title', type: 'string', description: 'Post title', isRequired: true },
          { name: 'content', type: 'text', description: 'Post content', isRequired: true },
          { name: 'excerpt', type: 'text', description: 'Post excerpt', isRequired: false },
          { name: 'featuredimageurl', type: 'string', description: 'Featured image URL', isRequired: false },
          { name: 'ispublished', type: 'boolean', description: 'Is published', isRequired: true },
          { name: 'publishedat', type: 'datetime', description: 'Published date', isRequired: false },
          { name: 'categories', type: 'string', description: 'Post categories', isRequired: false },
          { name: 'tags', type: 'string', description: 'Post tags', isRequired: false }
        ]
      },
      {
        name: 'technologies',
        displayName: 'Technologies',
        description: 'Technologies and tools used',
        recordCount: 18,
        fields: [
          { name: 'id', type: 'number', isRequired: true },
          { name: 'name', type: 'string', description: 'Technology name', isRequired: true },
          { name: 'description', type: 'text', description: 'Technology description', isRequired: true },
          { name: 'iconurl', type: 'string', description: 'Technology icon URL', isRequired: false },
          { name: 'isactive', type: 'boolean', description: 'Is actively used', isRequired: true }
        ]
      }
    ]

    setTables(mockTables)
    setIsLoading(false)
  }, [])

  const filteredTables = tables.filter(table =>
    table.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    table.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleTableSelect = async (tableName: string) => {
    setSelectedTable(tableName)

    try {
      // Try to fetch real data from the API
      const response = await fetch(`/api/${tableName}`)
      if (response.ok) {
        const data = await response.json()
        setPreviewData(data.slice(0, 5)) // Show first 5 records
      } else {
        // Fallback to mock data if API doesn't exist
        const mockData = generateMockData(tableName)
        setPreviewData(mockData)
      }
    } catch (error) {
      console.error('Error fetching preview data:', error)
      // Fallback to mock data
      const mockData = generateMockData(tableName)
      setPreviewData(mockData)
    }
  }

  const generateMockData = (tableName: string) => {
    const table = tables.find(t => t.name === tableName)
    if (!table) return []

    return Array.from({ length: 3 }, (_, index) => {
      const record: any = { id: index + 1 }
      table.fields.forEach(field => {
        switch (field.type) {
          case 'string':
            record[field.name] = `Sample ${field.name} ${index + 1}`
            break
          case 'text':
            record[field.name] = `This is sample ${field.name} content for record ${index + 1}`
            break
          case 'number':
            record[field.name] = (index + 1) * 10
            break
          case 'decimal':
            record[field.name] = (index + 1) * 29.99
            break
          case 'boolean':
            record[field.name] = index % 2 === 0
            break
          case 'datetime':
            record[field.name] = new Date().toISOString()
            break
          default:
            record[field.name] = `Sample ${field.name}`
        }
      })
      return record
    })
  }

  const handleBindData = async (tableName: string, fieldName?: string) => {
    try {
      // Fetch actual data from the API
      const response = await fetch(`/api/${tableName}`)
      if (response.ok) {
        const data = await response.json()

        // Create a data binding configuration
        const binding = {
          source: tableName,
          field: fieldName,
          data: data.slice(0, 5), // Preview first 5 records
          lastUpdated: new Date().toISOString()
        }

        // Store the binding (this would integrate with the page builder store)
        localStorage.setItem(`dataBinding_${tableName}${fieldName ? `_${fieldName}` : ''}`, JSON.stringify(binding))

        // Show success with preview
        const previewText = fieldName
          ? data.slice(0, 3).map((item: any) => item[fieldName]).join(', ')
          : `${data.length} records from ${tableName}`

        alert(`✅ Data binding configured!\n\nSource: ${tableName}${fieldName ? `.${fieldName}` : ''}\nPreview: ${previewText}`)
      } else {
        throw new Error('Failed to fetch data')
      }
    } catch (error) {
      console.error('Error binding data:', error)
      alert(`❌ Failed to bind data from ${tableName}. Please check if the API endpoint exists.`)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading database tables...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-96 flex">
      {/* Tables List */}
      <div className="w-1/2 border-r border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search tables..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        
        <div className="overflow-y-auto h-80">
          {filteredTables.map(table => (
            <div
              key={table.name}
              onClick={() => handleTableSelect(table.name)}
              className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                selectedTable === table.name ? 'bg-blue-50 border-blue-200' : ''
              }`}
            >
              <div className="flex items-center space-x-3">
                <TableCellsIcon className="h-5 w-5 text-gray-400" />
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-gray-900">{table.displayName}</h4>
                  <p className="text-xs text-gray-500">{table.description}</p>
                  <p className="text-xs text-gray-400 mt-1">{table.recordCount} records</p>
                </div>
                {selectedTable === table.name && (
                  <CheckIcon className="h-4 w-4 text-blue-600" />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Table Details */}
      <div className="w-1/2">
        {selectedTable ? (
          <div className="h-full flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  {tables.find(t => t.name === selectedTable)?.displayName}
                </h3>
                <button
                  onClick={() => handleBindData(selectedTable)}
                  className="flex items-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                >
                  <LinkIcon className="h-4 w-4 mr-2" />
                  Bind Table
                </button>
              </div>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4">
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Fields</h4>
                  <div className="space-y-2">
                    {tables.find(t => t.name === selectedTable)?.fields.map(field => (
                      <div
                        key={field.name}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded-md"
                      >
                        <div>
                          <span className="text-sm font-medium text-gray-900">{field.name}</span>
                          <span className="text-xs text-gray-500 ml-2">({field.type})</span>
                          {field.isRequired && (
                            <span className="text-xs text-red-500 ml-1">*</span>
                          )}
                          {field.description && (
                            <p className="text-xs text-gray-500 mt-1">{field.description}</p>
                          )}
                        </div>
                        <button
                          onClick={() => handleBindData(selectedTable, field.name)}
                          className="text-blue-600 hover:text-blue-800 text-xs"
                        >
                          Bind
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
                
                {previewData.length > 0 && (
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-sm font-medium text-gray-900">Live Data Preview</h4>
                      <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">
                        {previewData.length} records
                      </span>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                      <div className="max-h-64 overflow-y-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50 sticky top-0">
                            <tr>
                              {previewData[0] && Object.keys(previewData[0]).slice(0, 4).map(key => (
                                <th key={key} className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {key}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {previewData.map((record, index) => (
                              <tr key={index} className="hover:bg-gray-50">
                                {Object.entries(record).slice(0, 4).map(([key, value]) => (
                                  <td key={key} className="px-3 py-2 whitespace-nowrap text-xs text-gray-900">
                                    <div className="max-w-24 truncate" title={String(value)}>
                                      {typeof value === 'boolean' ? (
                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                          value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                        }`}>
                                          {value ? 'Yes' : 'No'}
                                        </span>
                                      ) : typeof value === 'number' ? (
                                        <span className="font-mono">{value}</span>
                                      ) : (
                                        String(value)
                                      )}
                                    </div>
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
                      <span>Showing first {previewData.length} records</span>
                      <button
                        onClick={() => handleTableSelect(selectedTable)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        Refresh Data
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <DatabaseIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Select a table to view details</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default DataBindingPanel
