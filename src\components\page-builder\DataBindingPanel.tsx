'use client'

import React, { useState, useEffect } from 'react'
import { useDataBindingStore } from '@/lib/page-builder/data-binding-store'
import { usePageBuilderStore } from '@/lib/page-builder/store'
import {
  CircleStackIcon,
  TableCellsIcon,
  MagnifyingGlassIcon,
  LinkIcon,
  CheckIcon,
  XMarkIcon,
  ArrowPathIcon,
  PlayIcon,
  EyeIcon,
  WifiIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface DataBindingPanelProps {
  onClose: () => void
}

interface DatabaseTable {
  name: string
  displayName: string
  description: string
  fields: DatabaseField[]
  recordCount: number
}

interface DatabaseField {
  name: string
  type: string
  description?: string
  isRequired: boolean
}

const DataBindingPanel: React.FC<DataBindingPanelProps> = ({ onClose }) => {
  const [selectedTable, setSelectedTable] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [isTestingConnection, setIsTestingConnection] = useState<string | null>(null)

  const {
    dataSources,
    previewData,
    isLoading,
    error,
    fetchData,
    testConnection,
    setPreviewData,
    addBinding
  } = useDataBindingStore()

  const { selectedComponentIds, components } = usePageBuilderStore()

  // Convert data sources to the expected format
  const tables: DatabaseTable[] = dataSources.map(source => ({
    name: source.name,
    displayName: source.displayName,
    description: `${source.fields.length} fields available`,
    fields: source.fields.map(field => ({
      name: field.name,
      type: field.type,
      description: field.description,
      isRequired: field.isRequired
    })),
    recordCount: previewData[source.name]?.length || 0
  }))

  // Test connections on component mount
  useEffect(() => {
    const testAllConnections = async () => {
      for (const source of dataSources) {
        await testConnection(source.name)
      }
    }
    testAllConnections()
  }, [dataSources, testConnection])



  const filteredTables = tables.filter(table =>
    table.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    table.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleTableSelect = async (tableName: string) => {
    setSelectedTable(tableName)

    try {
      // Fetch data using the store
      await fetchData(tableName, { limit: 5 })
    } catch (error) {
      console.error('Error fetching preview data:', error)
      // Generate mock data as fallback
      const mockData = generateMockData(tableName)
      setPreviewData(tableName, mockData)
    }
  }

  const handleTestConnection = async (tableName: string) => {
    setIsTestingConnection(tableName)
    try {
      const isConnected = await testConnection(tableName)
      if (isConnected) {
        // Fetch preview data if connection is successful
        await fetchData(tableName, { limit: 5 })
      }
    } catch (error) {
      console.error('Connection test failed:', error)
    } finally {
      setIsTestingConnection(null)
    }
  }

  const generateMockData = (tableName: string) => {
    const table = tables.find(t => t.name === tableName)
    if (!table) return []

    return Array.from({ length: 3 }, (_, index) => {
      const record: any = { id: index + 1 }
      table.fields.forEach(field => {
        switch (field.type) {
          case 'string':
            record[field.name] = `Sample ${field.name} ${index + 1}`
            break
          case 'text':
            record[field.name] = `This is sample ${field.name} content for record ${index + 1}`
            break
          case 'number':
            record[field.name] = (index + 1) * 10
            break
          case 'decimal':
            record[field.name] = (index + 1) * 29.99
            break
          case 'boolean':
            record[field.name] = index % 2 === 0
            break
          case 'datetime':
            record[field.name] = new Date().toISOString()
            break
          default:
            record[field.name] = `Sample ${field.name}`
        }
      })
      return record
    })
  }

  const handleBindData = async (tableName: string, fieldName?: string) => {
    // Check if a component is selected
    if (selectedComponentIds.length === 0) {
      alert('❌ Please select a component first to bind data to it.')
      return
    }

    const componentId = selectedComponentIds[0]
    const component = components.find(c => c.id === componentId)

    if (!component) {
      alert('❌ Selected component not found.')
      return
    }

    try {
      // Create a data binding
      addBinding({
        componentId,
        source: tableName,
        field: fieldName,
        query: { limit: 10 } // Default query
      })

      // Fetch fresh data
      const data = await fetchData(tableName, { limit: 5 })

      // Show success with preview
      const previewText = fieldName
        ? data.slice(0, 3).map((item: any) => item[fieldName]).filter(Boolean).join(', ')
        : `${data.length} records from ${tableName}`

      alert(`✅ Data binding configured!\n\nComponent: ${component.name || component.type}\nSource: ${tableName}${fieldName ? `.${fieldName}` : ''}\nPreview: ${previewText}`)

      // Close the panel
      onClose()
    } catch (error) {
      console.error('Error binding data:', error)
      alert(`❌ Failed to bind data from ${tableName}. Please check if the API endpoint exists.`)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading database tables...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 font-medium">Error loading data</p>
          <p className="text-gray-600 text-sm mt-2">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-96 flex">
      {/* Tables List */}
      <div className="w-1/2 border-r border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search tables..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        
        <div className="overflow-y-auto h-80">
          {filteredTables.map(table => {
            const dataSource = dataSources.find(ds => ds.name === table.name)
            const isConnected = dataSource?.isConnected || false
            const isTesting = isTestingConnection === table.name

            return (
              <div
                key={table.name}
                className={`p-4 border-b border-gray-100 hover:bg-gray-50 ${
                  selectedTable === table.name ? 'bg-blue-50 border-blue-200' : ''
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <TableCellsIcon className="h-5 w-5 text-gray-400" />
                    {/* Connection Status Indicator */}
                    <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                      isConnected ? 'bg-green-500' : 'bg-red-500'
                    }`}></div>
                  </div>

                  <div className="flex-1 cursor-pointer" onClick={() => handleTableSelect(table.name)}>
                    <div className="flex items-center space-x-2">
                      <h4 className="text-sm font-medium text-gray-900">{table.displayName}</h4>
                      {isConnected ? (
                        <WifiIcon className="h-3 w-3 text-green-500" />
                      ) : (
                        <ExclamationTriangleIcon className="h-3 w-3 text-red-500" />
                      )}
                    </div>
                    <p className="text-xs text-gray-500">{table.description}</p>
                    <div className="flex items-center space-x-3 mt-1">
                      <p className="text-xs text-gray-400">{table.recordCount} records</p>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        isConnected
                          ? 'bg-green-100 text-green-700'
                          : 'bg-red-100 text-red-700'
                      }`}>
                        {isConnected ? 'Connected' : 'Disconnected'}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {selectedTable === table.name && (
                      <CheckIcon className="h-4 w-4 text-blue-600" />
                    )}

                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleTestConnection(table.name)
                      }}
                      disabled={isTesting}
                      className={`p-1 rounded-md transition-colors ${
                        isTesting
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                      }`}
                      title="Test Connection"
                    >
                      {isTesting ? (
                        <ArrowPathIcon className="h-4 w-4 animate-spin" />
                      ) : (
                        <PlayIcon className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Table Details */}
      <div className="w-1/2">
        {selectedTable ? (
          <div className="h-full flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  {tables.find(t => t.name === selectedTable)?.displayName}
                </h3>
                <button
                  onClick={() => handleBindData(selectedTable)}
                  className="flex items-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                >
                  <LinkIcon className="h-4 w-4 mr-2" />
                  Bind Table
                </button>
              </div>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4">
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Fields</h4>
                  <div className="space-y-2">
                    {tables.find(t => t.name === selectedTable)?.fields.map(field => (
                      <div
                        key={field.name}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded-md"
                      >
                        <div>
                          <span className="text-sm font-medium text-gray-900">{field.name}</span>
                          <span className="text-xs text-gray-500 ml-2">({field.type})</span>
                          {field.isRequired && (
                            <span className="text-xs text-red-500 ml-1">*</span>
                          )}
                          {field.description && (
                            <p className="text-xs text-gray-500 mt-1">{field.description}</p>
                          )}
                        </div>
                        <button
                          onClick={() => handleBindData(selectedTable, field.name)}
                          className="text-blue-600 hover:text-blue-800 text-xs"
                        >
                          Bind
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
                
                {selectedTable && previewData[selectedTable]?.length > 0 && (
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-sm font-medium text-gray-900">Live Data Preview</h4>
                      <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">
                        {previewData[selectedTable]?.length || 0} records
                      </span>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                      <div className="max-h-64 overflow-y-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50 sticky top-0">
                            <tr>
                              {selectedTable && previewData[selectedTable][0] && Object.keys(previewData[selectedTable][0]).slice(0, 4).map(key => (
                                <th key={key} className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {key}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {selectedTable && previewData[selectedTable].map((record, index) => (
                              <tr key={index} className="hover:bg-gray-50">
                                {Object.entries(record).slice(0, 4).map(([key, value]) => (
                                  <td key={key} className="px-3 py-2 whitespace-nowrap text-xs text-gray-900">
                                    <div className="max-w-24 truncate" title={String(value)}>
                                      {typeof value === 'boolean' ? (
                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                          value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                        }`}>
                                          {value ? 'Yes' : 'No'}
                                        </span>
                                      ) : typeof value === 'number' ? (
                                        <span className="font-mono">{value}</span>
                                      ) : (
                                        String(value)
                                      )}
                                    </div>
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
                      <span>Showing first {selectedTable ? previewData[selectedTable]?.length || 0 : 0} records</span>
                      <button
                        onClick={() => handleTableSelect(selectedTable)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        Refresh Data
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <CircleStackIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Select a table to view details</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default DataBindingPanel
