import { NextRequest } from 'next/server'

interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxAttempts: number // Maximum attempts per window
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
}

interface RateLimitStore {
  [key: string]: {
    count: number
    resetTime: number
  }
}

// In-memory store (in production, use Redis or database)
const store: RateLimitStore = {}

// Clean up expired entries every 5 minutes
setInterval(() => {
  const now = Date.now()
  Object.keys(store).forEach(key => {
    if (store[key].resetTime < now) {
      delete store[key]
    }
  })
}, 5 * 60 * 1000)

export class RateLimiter {
  private config: RateLimitConfig

  constructor(config: RateLimitConfig) {
    this.config = config
  }

  private getClientId(request: NextRequest): string {
    // Get client IP address
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const ip = forwarded?.split(',')[0] || realIp || 'unknown'
    
    // You can also include user agent or other identifiers
    const userAgent = request.headers.get('user-agent') || ''
    
    return `${ip}:${userAgent.slice(0, 50)}`
  }

  async isAllowed(request: NextRequest): Promise<{
    allowed: boolean
    remaining: number
    resetTime: number
    retryAfter?: number
  }> {
    const clientId = this.getClientId(request)
    const now = Date.now()
    const windowStart = now - this.config.windowMs

    // Get or create client record
    if (!store[clientId] || store[clientId].resetTime < now) {
      store[clientId] = {
        count: 0,
        resetTime: now + this.config.windowMs
      }
    }

    const client = store[clientId]
    
    // Check if within rate limit
    if (client.count >= this.config.maxAttempts) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: client.resetTime,
        retryAfter: Math.ceil((client.resetTime - now) / 1000)
      }
    }

    // Increment counter
    client.count++

    return {
      allowed: true,
      remaining: this.config.maxAttempts - client.count,
      resetTime: client.resetTime
    }
  }

  async recordAttempt(request: NextRequest, success: boolean): Promise<void> {
    if (this.config.skipSuccessfulRequests && success) return
    if (this.config.skipFailedRequests && !success) return

    const clientId = this.getClientId(request)
    const now = Date.now()

    if (!store[clientId] || store[clientId].resetTime < now) {
      store[clientId] = {
        count: 1,
        resetTime: now + this.config.windowMs
      }
    } else {
      store[clientId].count++
    }
  }
}

// Pre-configured rate limiters
export const authRateLimiter = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxAttempts: 5, // 5 attempts per 15 minutes
  skipSuccessfulRequests: true
})

export const generalRateLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxAttempts: 100, // 100 requests per minute
  skipSuccessfulRequests: true,
  skipFailedRequests: true
})

// Middleware helper
export async function withRateLimit(
  request: NextRequest,
  rateLimiter: RateLimiter = generalRateLimiter
) {
  const result = await rateLimiter.isAllowed(request)
  
  if (!result.allowed) {
    return new Response(
      JSON.stringify({
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.',
        retryAfter: result.retryAfter
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': result.retryAfter?.toString() || '60',
          'X-RateLimit-Limit': rateLimiter['config'].maxAttempts.toString(),
          'X-RateLimit-Remaining': result.remaining.toString(),
          'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
        }
      }
    )
  }

  return null // No rate limit hit
}
