'use client'

import React, { useState } from 'react'
import { usePageBuilderStore } from '@/lib/page-builder/store'
import { componentRegistry } from '@/lib/page-builder/component-registry'
import { 
  CogIcon, 
  PaintBrushIcon, 
  DocumentTextIcon,
  EyeIcon,
  EyeSlashIcon,
  LockClosedIcon,
  LockOpenIcon,
  TrashIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline'
import PropertyField from './PropertyField'

interface PropertyPanelProps {
  className?: string
}

const PropertyPanel: React.FC<PropertyPanelProps> = ({ className = '' }) => {
  const [activeTab, setActiveTab] = useState<'content' | 'style' | 'settings'>('content')
  
  const {
    selectedComponentIds,
    getSelectedComponents,
    updateComponent,
    deleteComponent,
    duplicateComponent
  } = usePageBuilderStore()
  
  const selectedComponents = getSelectedComponents()
  const selectedComponent = selectedComponents.length === 1 ? selectedComponents[0] : null
  
  if (selectedComponents.length === 0) {
    return (
      <div className={`flex flex-col h-full bg-white border-l border-gray-200 ${className}`}>
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Properties</h2>
        </div>
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="text-center text-gray-500">
            <CogIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium mb-2">No Component Selected</h3>
            <p className="text-sm">
              Select a component on the canvas to edit its properties
            </p>
          </div>
        </div>
      </div>
    )
  }
  
  if (selectedComponents.length > 1) {
    return (
      <div className={`flex flex-col h-full bg-white border-l border-gray-200 ${className}`}>
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Properties</h2>
        </div>
        <div className="flex-1 p-4">
          <div className="text-center text-gray-500 mb-6">
            <CogIcon className="h-8 w-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">
              {selectedComponents.length} components selected
            </p>
          </div>
          
          {/* Bulk actions */}
          <div className="space-y-2">
            <button
              onClick={() => selectedComponentIds.forEach(id => duplicateComponent(id))}
              className="w-full flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
              Duplicate All
            </button>
            
            <button
              onClick={() => selectedComponentIds.forEach(id => deleteComponent(id))}
              className="w-full flex items-center justify-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete All
            </button>
          </div>
        </div>
      </div>
    )
  }
  
  const component = selectedComponent!
  const config = componentRegistry[component.type]
  
  const handleContentUpdate = (field: string, value: any) => {
    updateComponent(component.id, {
      content: { ...component.content, [field]: value }
    })
  }
  
  const handleStyleUpdate = (property: string, value: any) => {
    updateComponent(component.id, {
      styles: { ...component.styles, [property]: value }
    })
  }
  
  const handleVisibilityToggle = () => {
    updateComponent(component.id, { isVisible: !component.isVisible })
  }
  
  const handleLockToggle = () => {
    updateComponent(component.id, { isLocked: !component.isLocked })
  }
  
  const tabs = [
    { id: 'content', label: 'Content', icon: DocumentTextIcon },
    { id: 'style', label: 'Style', icon: PaintBrushIcon },
    { id: 'settings', label: 'Settings', icon: CogIcon }
  ]
  
  return (
    <div className={`flex flex-col h-full bg-white border-l border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Properties</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleVisibilityToggle}
              className={`p-1 rounded ${
                component.isVisible 
                  ? 'text-gray-600 hover:text-gray-800' 
                  : 'text-red-600 hover:text-red-800'
              }`}
              title={component.isVisible ? 'Hide component' : 'Show component'}
            >
              {component.isVisible ? (
                <EyeIcon className="h-4 w-4" />
              ) : (
                <EyeSlashIcon className="h-4 w-4" />
              )}
            </button>
            
            <button
              onClick={handleLockToggle}
              className={`p-1 rounded ${
                component.isLocked 
                  ? 'text-red-600 hover:text-red-800' 
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              title={component.isLocked ? 'Unlock component' : 'Lock component'}
            >
              {component.isLocked ? (
                <LockClosedIcon className="h-4 w-4" />
              ) : (
                <LockOpenIcon className="h-4 w-4" />
              )}
            </button>
          </div>
        </div>
        
        <div className="flex items-center space-x-2 mb-4">
          <div className="text-2xl">{config.icon}</div>
          <div>
            <div className="font-medium text-gray-900">{config.name}</div>
            <div className="text-sm text-gray-500">{config.description}</div>
          </div>
        </div>
        
        {/* Action buttons */}
        <div className="flex space-x-2">
          <button
            onClick={() => duplicateComponent(component.id)}
            className="flex-1 flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <DocumentDuplicateIcon className="h-4 w-4 mr-1" />
            Duplicate
          </button>
          
          <button
            onClick={() => deleteComponent(component.id)}
            className="flex-1 flex items-center justify-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
          >
            <TrashIcon className="h-4 w-4 mr-1" />
            Delete
          </button>
        </div>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex">
          {tabs.map(tab => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium border-b-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-1" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>
      
      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'content' && (
          <div className="p-4 space-y-4">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Content Properties</h3>
            {config.contentFields.map(field => (
              <PropertyField
                key={field.name}
                field={field}
                value={component.content[field.name]}
                onChange={(value) => handleContentUpdate(field.name, value)}
              />
            ))}
            {config.contentFields.length === 0 && (
              <p className="text-sm text-gray-500">No content properties available</p>
            )}
          </div>
        )}
        
        {activeTab === 'style' && (
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Style Properties</h3>
            {config.styleGroups.map(group => (
              <div key={group.name} className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                  {group.icon && <span className="mr-2">{group.icon}</span>}
                  {group.label}
                </h4>
                <div className="space-y-3">
                  {group.properties.map(property => (
                    <PropertyField
                      key={property.name}
                      field={property}
                      value={component.styles[property.name]}
                      onChange={(value) => handleStyleUpdate(property.name, value)}
                    />
                  ))}
                </div>
              </div>
            ))}
            {config.styleGroups.length === 0 && (
              <p className="text-sm text-gray-500">No style properties available</p>
            )}
          </div>
        )}
        
        {activeTab === 'settings' && (
          <div className="p-4 space-y-4">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Component Settings</h3>
            
            {/* Position and Size */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">Position & Size</h4>
              <div className="grid grid-cols-2 gap-2">
                <PropertyField
                  field={{
                    name: 'x',
                    label: 'X',
                    type: 'number',
                    defaultValue: 0,
                    validation: { min: 0 }
                  }}
                  value={component.position.x}
                  onChange={(value) => updateComponent(component.id, {
                    position: { ...component.position, x: Number(value) }
                  })}
                />
                <PropertyField
                  field={{
                    name: 'y',
                    label: 'Y',
                    type: 'number',
                    defaultValue: 0,
                    validation: { min: 0 }
                  }}
                  value={component.position.y}
                  onChange={(value) => updateComponent(component.id, {
                    position: { ...component.position, y: Number(value) }
                  })}
                />
                <PropertyField
                  field={{
                    name: 'width',
                    label: 'Width',
                    type: 'number',
                    defaultValue: 100,
                    validation: { min: 1 }
                  }}
                  value={component.position.width}
                  onChange={(value) => updateComponent(component.id, {
                    position: { ...component.position, width: Number(value) }
                  })}
                />
                <PropertyField
                  field={{
                    name: 'height',
                    label: 'Height',
                    type: 'number',
                    defaultValue: 100,
                    validation: { min: 1 }
                  }}
                  value={component.position.height}
                  onChange={(value) => updateComponent(component.id, {
                    position: { ...component.position, height: Number(value) }
                  })}
                />
              </div>
            </div>
            
            {/* Component Name */}
            <PropertyField
              field={{
                name: 'name',
                label: 'Component Name',
                type: 'text',
                defaultValue: config.name,
                placeholder: 'Enter component name'
              }}
              value={component.name || config.name}
              onChange={(value) => updateComponent(component.id, { name: value })}
            />
            
            {/* Order */}
            <PropertyField
              field={{
                name: 'order',
                label: 'Layer Order',
                type: 'number',
                defaultValue: 0,
                description: 'Higher values appear on top'
              }}
              value={component.order}
              onChange={(value) => updateComponent(component.id, { order: Number(value) })}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default PropertyPanel
