'use client'

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { CrudConfig } from './types'
import {
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  CheckIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline'

interface CrudManagerProps<T> {
  config: CrudConfig<T>
}

// Icon mapping
const iconMap = {
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  CheckIcon,
  XMarkIcon,
}

// Get icon component by name
const getIcon = (iconName: string) => {
  return iconMap[iconName as keyof typeof iconMap] || EyeIcon
}

interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category: {
    id: string
    name: string
    description?: string
  }
  serviceOptions: Array<{
    id: string
    name: string
    price?: number
  }>
  _count: {
    projects: number
    orderDetails: number
    serviceOptions: number
  }
}

export function CrudManager<T>({ config }: CrudManagerProps<T>) {
  const [services, setServices] = useState<Service[]>([])
  const [categories, setCategories] = useState<Array<{ id: string; name: string }>>([
    // Fallback categories in case API fails
    { id: 'web-development', name: 'Web Development' },
    { id: 'mobile-development', name: 'Mobile Development' },
    { id: 'consulting', name: 'Consulting' }
  ])
  const [loading, setLoading] = useState(true)
  const [searchLoading, setSearchLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedItems, setSelectedItems] = useState<Service[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingService, setEditingService] = useState<Service | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      // Only search if query is empty (to show all) or has at least 2 characters
      if (searchQuery.length === 0 || searchQuery.length >= 2) {
        setDebouncedSearchQuery(searchQuery)
      }
    }, 500) // 500ms delay

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Reset page when search changes
  useEffect(() => {
    if (debouncedSearchQuery !== searchQuery) {
      setCurrentPage(1)
    }
  }, [debouncedSearchQuery, searchQuery])

  // Fetch services
  const fetchServices = useCallback(async (isSearching = false) => {
    try {
      if (isSearching) {
        setSearchLoading(true)
      } else {
        setLoading(true)
      }
      setError(null)

      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
      })

      // Only add search parameter if it has a value
      if (debouncedSearchQuery && debouncedSearchQuery.trim()) {
        params.set('search', debouncedSearchQuery.trim())
      }

      const response = await fetch(`/api/admin/${config.endpoint}?${params.toString()}`, {
        credentials: 'include', // Include cookies for authentication
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {

        if (response.status === 401) {
          throw new Error('Authentication required. Please log in as an admin.')
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()

      // Handle different response structures
      if (result.success) {
        const servicesData = Array.isArray(result.data) ? result.data : []
        setServices(servicesData)

        // Handle pagination - check for totalPages or pages
        const pagination = result.pagination
        setTotalPages(pagination?.totalPages || pagination?.pages || 1)
      } else {
        throw new Error(result.error || 'Failed to fetch services')
      }
    } catch (err) {
      console.error('Fetch services error:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setServices([]) // Ensure services is always an array
    } finally {
      setLoading(false)
      setSearchLoading(false)
    }
  }, [config.endpoint, currentPage, debouncedSearchQuery])

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/categories/dropdown', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        console.warn('Failed to fetch categories:', response.statusText)
        return
      }

      const result = await response.json()

      if (result.success && Array.isArray(result.data)) {
        setCategories(result.data)
      }
    } catch (err) {
      console.error('Failed to fetch categories:', err)
    }
  }

  // Fetch services when page or debounced search changes
  useEffect(() => {
    const isSearching = debouncedSearchQuery !== searchQuery
    fetchServices(isSearching)
  }, [fetchServices])

  // Fetch categories on mount
  useEffect(() => {
    fetchCategories()
  }, [])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Focus search on Ctrl/Cmd + K
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault()
        searchInputRef.current?.focus()
      }
      // Clear search on Escape
      if (event.key === 'Escape' && searchInputRef.current === document.activeElement) {
        setSearchQuery('')
        searchInputRef.current?.blur()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  // Create service
  const handleCreate = async (formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to create service')
      
      setIsCreateModalOpen(false)
      fetchServices()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create service')
    }
  }

  // Update service
  const handleUpdate = async (id: string, formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to update service')
      
      setIsEditModalOpen(false)
      setEditingService(null)
      fetchServices()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update service')
    }
  }

  // Delete service
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this service?')) return
    
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) throw new Error('Failed to delete service')
      
      fetchServices()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete service')
    }
  }

  // Bulk delete
  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return
    if (!confirm(`Are you sure you want to delete ${selectedItems.length} service(s)?`)) return
    
    try {
      const ids = selectedItems.map(item => item.id)
      const response = await fetch(`/api/admin/${config.endpoint}/bulk-delete`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids }),
      })
      
      if (!response.ok) throw new Error('Failed to delete services')
      
      setSelectedItems([])
      fetchServices()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete services')
    }
  }

  // Handle individual actions
  const handleAction = async (action: string, item: any) => {
    const actionKey = `${action}-${item.id}`

    try {
      setActionLoading(actionKey)

      switch (action) {
        case 'preview':
          // Open service in new tab
          window.open(`/services/${item.id}`, '_blank')
          break

        case 'edit':
          setEditingService(item)
          setIsEditModalOpen(true)
          break

        case 'toggle-status':
          await handleToggleStatus(item.id, !item.isActive)
          break

        case 'delete':
          if (window.confirm('Are you sure you want to delete this service? This action cannot be undone.')) {
            await handleDelete(item.id)
          }
          break

        default:
          console.warn(`Unknown action: ${action}`)
      }
    } finally {
      setActionLoading(null)
    }
  }

  // Toggle service status
  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive }),
      })

      if (!response.ok) throw new Error('Failed to update service status')

      fetchServices()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update service status')
    }
  }

  // Bulk action
  const handleBulkAction = async (action: string) => {
    if (selectedItems.length === 0) return

    try {
      const ids = selectedItems.map(item => item.id)
      const response = await fetch(`/api/admin/${config.endpoint}/bulk-action`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, ids }),
      })

      if (!response.ok) throw new Error(`Failed to ${action} services`)

      setSelectedItems([])
      fetchServices()
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to ${action} services`)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  // Helper function to get nested object values
  const getNestedValue = (obj: any, path: string) => {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null
    }, obj)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
          {config.description && (
            <p className="text-gray-600">{config.description}</p>
          )}
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <span>+</span>
          <span>Add Service</span>
        </button>
      </div>

      {/* Search and Filters */}
      <div className="flex space-x-4">
        <div className="flex-1 relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            ref={searchInputRef}
            type="text"
            placeholder={config.searchPlaceholder || 'Search services... (Ctrl+K)'}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full pl-10 pr-24 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
          />

          {/* Right side indicators and controls */}
          <div className="absolute inset-y-0 right-0 flex items-center">
            {/* Clear button */}
            {searchQuery && !searchLoading && (
              <button
                onClick={() => {
                  setSearchQuery('')
                  searchInputRef.current?.focus()
                }}
                className="p-1 mr-2 text-gray-400 hover:text-gray-600 transition-colors"
                title="Clear search"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            )}

            {/* Search Loading Indicator */}
            {searchLoading && (
              <div className="pr-3 flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
              </div>
            )}

            {/* Search Status Indicator */}
            {!searchLoading && searchQuery && (
              <div className="pr-3 flex items-center">
                <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  {searchQuery !== debouncedSearchQuery ? 'Typing...' : `${services.length} found`}
                </div>
              </div>
            )}
          </div>
        </div>
        {selectedItems.length > 0 && (
          <div className="flex space-x-2">
            <button
              onClick={() => handleBulkAction('activate')}
              className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              Activate ({selectedItems.length})
            </button>
            <button
              onClick={() => handleBulkAction('deactivate')}
              className="px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
            >
              Deactivate ({selectedItems.length})
            </button>
            <button
              onClick={handleBulkDelete}
              className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              Delete ({selectedItems.length})
            </button>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Services</h3>
              <div className="mt-1 text-sm text-red-700">
                {error}
              </div>
              {error.includes('Authentication') && (
                <div className="mt-2">
                  <button
                    onClick={() => window.location.href = '/auth/signin'}
                    className="text-sm bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded"
                  >
                    Go to Login
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Search Results Summary */}
      {(searchQuery || debouncedSearchQuery) && !loading && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <MagnifyingGlassIcon className="h-5 w-5 text-blue-500 mr-2" />
              <span className="text-sm text-blue-700">
                {services.length > 0
                  ? `Found ${services.length} service${services.length === 1 ? '' : 's'} matching "${debouncedSearchQuery}"`
                  : `No services found matching "${debouncedSearchQuery}"`
                }
              </span>
            </div>
            {(searchQuery || debouncedSearchQuery) && (
              <button
                onClick={() => {
                  setSearchQuery('')
                  searchInputRef.current?.focus()
                }}
                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                Clear search
              </button>
            )}
          </div>
        </div>
      )}

      {/* Services Table */}
      <div className={`bg-white shadow rounded-lg overflow-hidden transition-opacity duration-200 ${
        searchLoading ? 'opacity-75' : 'opacity-100'
      }`}>
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={Array.isArray(services) && selectedItems.length === services.length && services.length > 0}
                  onChange={(e) => {
                    if (e.target.checked && Array.isArray(services)) {
                      setSelectedItems([...services])
                    } else {
                      setSelectedItems([])
                    }
                  }}
                  className="rounded border-gray-300"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Service
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Projects
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Updated
              </th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {Array.isArray(services) && services.map((service) => (
              <tr key={service.id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <input
                    type="checkbox"
                    checked={selectedItems.some(item => item.id === service.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedItems([...selectedItems, service])
                      } else {
                        setSelectedItems(selectedItems.filter(item => item.id !== service.id))
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                </td>
                <td className="px-6 py-4">
                  <div>
                    <div className="font-medium text-gray-900">{service.name}</div>
                    <div className="text-sm text-gray-500 truncate max-w-xs">
                      {service.description}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {getNestedValue(service, 'category.name') || 'No Category'}
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {formatPrice(service.price)}
                </td>
                <td className="px-6 py-4">
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    service.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {service.isActive ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {getNestedValue(service, '_count.projects') || 0}
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {formatDate(service.updatedAt)}
                </td>
                <td className="px-6 py-4 text-sm font-medium">
                  <div className="flex items-center justify-center space-x-1">
                    {config.actions?.map((action) => {
                      const IconComponent = getIcon(action.icon)
                      const getVariantClasses = (variant: string) => {
                        switch (variant) {
                          case 'primary':
                            return 'text-blue-600 hover:text-blue-800 hover:bg-blue-50 border-blue-200'
                          case 'secondary':
                            return 'text-gray-600 hover:text-gray-800 hover:bg-gray-50 border-gray-200'
                          case 'warning':
                            return service.isActive
                              ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-50 border-orange-200'
                              : 'text-green-600 hover:text-green-800 hover:bg-green-50 border-green-200'
                          case 'danger':
                            return 'text-red-600 hover:text-red-800 hover:bg-red-50 border-red-200'
                          default:
                            return 'text-gray-600 hover:text-gray-800 hover:bg-gray-50 border-gray-200'
                        }
                      }

                      // Special handling for toggle status button
                      const getStatusIcon = () => {
                        if (action.action === 'toggle-status') {
                          return service.isActive ? PowerIcon : CheckIcon
                        }
                        return IconComponent
                      }

                      const StatusIcon = getStatusIcon()
                      const actionKey = `${action.action}-${service.id}`
                      const isLoading = actionLoading === actionKey

                      return (
                        <button
                          key={action.action}
                          onClick={() => handleAction(action.action, service)}
                          disabled={isLoading}
                          className={`p-2 rounded-md transition-all duration-200 border border-transparent hover:border-solid ${getVariantClasses(action.variant)} group relative ${
                            isLoading ? 'opacity-50 cursor-not-allowed' : ''
                          }`}
                          title={action.action === 'toggle-status'
                            ? (service.isActive ? 'Deactivate service' : 'Activate service')
                            : (action.tooltip || action.label)
                          }
                        >
                          {isLoading ? (
                            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <StatusIcon className="w-4 h-4" />
                          )}

                          {/* Tooltip */}
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                            {action.action === 'toggle-status'
                              ? (service.isActive ? 'Deactivate' : 'Activate')
                              : action.label
                            }
                          </div>
                        </button>
                      )
                    })}
                  </div>
                </td>
              </tr>
            ))}
            {(!Array.isArray(services) || services.length === 0) && !loading && (
              <tr>
                <td colSpan={8} className="px-6 py-12 text-center">
                  <div className="text-gray-500">
                    {error ? (
                      <div>
                        <p className="text-red-600 mb-2">Error loading services</p>
                        <p className="text-sm">{error}</p>
                      </div>
                    ) : (
                      <div>
                        {debouncedSearchQuery ? (
                          <>
                            <p className="mb-2">No services match your search</p>
                            <p className="text-sm mb-4">Try adjusting your search terms or browse all services</p>
                            <button
                              onClick={() => {
                                setSearchQuery('')
                                searchInputRef.current?.focus()
                              }}
                              className="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                            >
                              Clear search
                            </button>
                          </>
                        ) : (
                          <>
                            <p className="mb-2">No services found</p>
                            <p className="text-sm">Click "Add Service" to create your first service</p>
                          </>
                        )}
                      </div>
                    )}
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && Array.isArray(services) && services.length > 0 && (
        <div className="flex justify-center space-x-2">
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => setCurrentPage(page)}
              className={`px-3 py-2 rounded ${
                currentPage === page
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {page}
            </button>
          ))}
        </div>
      )}

      {/* Create Modal */}
      {isCreateModalOpen && (
        <ServiceModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          onSubmit={handleCreate}
          categories={categories}
          title="Create Service"
          onModalOpen={fetchCategories}
        />
      )}

      {/* Edit Modal */}
      {isEditModalOpen && editingService && (
        <ServiceModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false)
            setEditingService(null)
          }}
          onSubmit={(data) => handleUpdate(editingService.id, data)}
          categories={categories}
          title="Edit Service"
          initialData={editingService}
          onModalOpen={fetchCategories}
        />
      )}
    </div>
  )
}

// Service Modal Component
interface ServiceModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => void
  categories: Array<{ id: string; name: string }>
  title: string
  initialData?: any
  onModalOpen?: () => void
}

function ServiceModal({ isOpen, onClose, onSubmit, categories, title, initialData, onModalOpen }: ServiceModalProps) {
  useEffect(() => {
    if (isOpen && onModalOpen) {
      onModalOpen()
    }
  }, [isOpen, onModalOpen])

  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    categoryId: initialData?.categoryId || '',
    description: initialData?.description || '',
    iconClass: initialData?.iconClass || '',
    price: Number(initialData?.price) || 0,
    discountRate: Number(initialData?.discountRate) || 0,
    totalDiscount: Number(initialData?.totalDiscount) || 0,
    manager: initialData?.manager || '',
    displayOrder: Number(initialData?.displayOrder) || 0,
    isActive: initialData?.isActive ?? true,
  })

  const [uploadingIcon, setUploadingIcon] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileUpload = async (file: File) => {
    setUploadingIcon(true)
    try {
      const uploadFormData = new FormData()
      uploadFormData.append('file', file)

      const response = await fetch('/api/upload/service-icon', {
        method: 'POST',
        body: uploadFormData
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const result = await response.json()
      if (result.success) {
        setFormData({ ...formData, iconClass: result.data.url })
      } else {
        throw new Error(result.error || 'Upload failed')
      }
    } catch (error) {
      console.error('Upload error:', error)
      alert('Failed to upload icon. Please try again.')
    } finally {
      setUploadingIcon(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm" onClick={onClose} />

        <div className="relative bg-white rounded-xl shadow-2xl max-w-5xl w-full max-h-[95vh] overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-white">{title}</h2>
              <button
                type="button"
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Form Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Main Content Grid */}
              <div className="grid grid-cols-12 gap-6">

                {/* Left Column - Basic Info */}
                <div className="col-span-8 space-y-4">
                  {/* Service Details Card */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Service Details
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Service Name *
                        </label>
                        <input
                          type="text"
                          required
                          value={formData.name}
                          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter service name"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Category *
                        </label>
                        <select
                          required
                          value={formData.categoryId}
                          onChange={(e) => setFormData({ ...formData, categoryId: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">Select Category</option>
                          {categories.map((category) => (
                            <option key={category.id} value={category.id}>
                              {category.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="mt-3">
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Description *
                      </label>
                      <textarea
                        required
                        rows={2}
                        value={formData.description}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter service description"
                      />
                    </div>
                  </div>

                  {/* Pricing Card */}
                  <div className="bg-green-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                      Pricing
                    </h3>
                    <div className="grid grid-cols-3 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Starting Price *
                        </label>
                        <input
                          type="number"
                          required
                          min="0"
                          step="0.01"
                          value={formData.price}
                          onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) || 0 })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0.00"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Discount %
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={formData.discountRate}
                          onChange={(e) => setFormData({ ...formData, discountRate: Number(e.target.value) || 0 })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Discount Amount
                        </label>
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          value={formData.totalDiscount}
                          onChange={(e) => setFormData({ ...formData, totalDiscount: Number(e.target.value) || 0 })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Management Card */}
                  <div className="bg-purple-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      Management
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Service Manager
                        </label>
                        <input
                          type="text"
                          value={formData.manager}
                          onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Manager name"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Display Order
                        </label>
                        <input
                          type="number"
                          min="0"
                          value={formData.displayOrder}
                          onChange={(e) => setFormData({ ...formData, displayOrder: Number(e.target.value) || 0 })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right Column - Icon & Status */}
                <div className="col-span-4 space-y-4">
                  {/* Icon Upload Card */}
                  <div className="bg-orange-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      Service Icon
                    </h3>

                    {/* Icon Preview */}
                    <div className="mb-3">
                      {formData.iconClass ? (
                        <div className="flex items-center justify-center w-16 h-16 bg-white border-2 border-dashed border-gray-300 rounded-lg">
                          {formData.iconClass.startsWith('/uploads/') ? (
                            <img
                              src={formData.iconClass}
                              alt="Service icon"
                              className="w-12 h-12 object-contain"
                            />
                          ) : (
                            <div className="text-xs text-gray-500 text-center">
                              {formData.iconClass}
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="flex items-center justify-center w-16 h-16 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg">
                          <svg className="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                      )}
                    </div>

                    {/* Upload Button */}
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/svg+xml,image/png,image/jpeg,image/jpg,image/webp"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) {
                          handleFileUpload(file)
                        }
                      }}
                      className="hidden"
                    />
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={uploadingIcon}
                      className="w-full flex items-center justify-center px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 transition-colors"
                    >
                      {uploadingIcon ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-600 mr-2"></div>
                          Uploading...
                        </>
                      ) : (
                        <>
                          <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                          </svg>
                          Upload Icon
                        </>
                      )}
                    </button>

                    {/* Icon Class Input */}
                    <div className="mt-3">
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Or Icon Class
                      </label>
                      <div className="flex">
                        <input
                          type="text"
                          value={formData.iconClass}
                          onChange={(e) => setFormData({ ...formData, iconClass: e.target.value })}
                          className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-l-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="CodeBracketIcon"
                        />
                        {formData.iconClass && (
                          <button
                            type="button"
                            onClick={() => setFormData({ ...formData, iconClass: '' })}
                            className="px-2 py-2 bg-red-100 text-red-600 border border-l-0 border-gray-300 rounded-r-md hover:bg-red-200 transition-colors"
                          >
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Status Card */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Status
                    </h3>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isActive"
                        checked={formData.isActive}
                        onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
                      />
                      <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
                        Service is active and available
                      </label>
                    </div>
                  </div>

                  {/* Quick Stats */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      Quick Info
                    </h3>
                    <div className="space-y-2 text-xs text-gray-600">
                      <div className="flex justify-between">
                        <span>Final Price:</span>
                        <span className="font-medium">
                          ${(() => {
                            const price = Number(formData.price) || 0
                            const discountRate = Number(formData.discountRate) || 0
                            const totalDiscount = Number(formData.totalDiscount) || 0

                            if (discountRate > 0) {
                              return (price * (1 - discountRate / 100)).toFixed(2)
                            } else if (totalDiscount > 0) {
                              return Math.max(0, price - totalDiscount).toFixed(2)
                            } else {
                              return price.toFixed(2)
                            }
                          })()}
                        </span>
                      </div>
                      {(Number(formData.discountRate) > 0 || Number(formData.totalDiscount) > 0) && (
                        <div className="flex justify-between">
                          <span>Savings:</span>
                          <span className="font-medium text-green-600">
                            ${(() => {
                              const price = Number(formData.price) || 0
                              const discountRate = Number(formData.discountRate) || 0
                              const totalDiscount = Number(formData.totalDiscount) || 0

                              if (discountRate > 0) {
                                return (price * discountRate / 100).toFixed(2)
                              } else {
                                return totalDiscount.toFixed(2)
                              }
                            })()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                <div className="text-xs text-gray-500">
                  {initialData ? 'Last updated: ' + new Date(initialData.updatedAt).toLocaleDateString() : 'Creating new service'}
                </div>
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    disabled={uploadingIcon}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={uploadingIcon}
                    className="px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-md hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 transition-all duration-200 shadow-sm"
                  >
                    {uploadingIcon ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                        Processing...
                      </>
                    ) : (
                      <>
                        {initialData ? (
                          <>
                            <svg className="h-4 w-4 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Update Service
                          </>
                        ) : (
                          <>
                            <svg className="h-4 w-4 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Create Service
                          </>
                        )}
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CrudManager
