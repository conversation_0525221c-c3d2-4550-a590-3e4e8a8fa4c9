import fs from 'fs'
import path from 'path'
import { Component } from './store'

// Define the structure of a scanned page
export interface ScannedPage {
  id: string
  title: string
  slug: string
  filePath: string
  description?: string
  metaTitle?: string
  metaDescription?: string
  isPublished: boolean
  isHomePage: boolean
  layout: Component[]
  styles: Record<string, any>
  seoSettings: Record<string, any>
  sourceCode: string
  lastModified: Date
  type: 'static' | 'dynamic'
  hasParams: boolean
}

// Page detection patterns
const PAGE_PATTERNS = {
  // Static pages
  static: [
    'page.tsx',
    'page.ts',
    'page.jsx',
    'page.js'
  ],
  // Dynamic pages
  dynamic: [
    '[slug]/page.tsx',
    '[id]/page.tsx',
    '[...slug]/page.tsx'
  ]
}

// Extract metadata from page component
export function extractPageMetadata(sourceCode: string, slug: string): {
  title: string
  description?: string
  metaTitle?: string
  metaDescription?: string
} {
  const defaultTitle = slug.split('/').pop()?.replace(/[-_]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase()) || 'Untitled Page'

  // Try to extract title from various patterns
  const titlePatterns = [
    /<title[^>]*>([^<]+)<\/title>/i,
    /title:\s*['"`]([^'"`]+)['"`]/i,
    /<h1[^>]*>([^<]+)<\/h1>/i,
    /const\s+title\s*=\s*['"`]([^'"`]+)['"`]/i
  ]

  const descriptionPatterns = [
    /<meta\s+name=['"`]description['"`]\s+content=['"`]([^'"`]+)['"`]/i,
    /description:\s*['"`]([^'"`]+)['"`]/i,
    /<p[^>]*>([^<]{50,200})<\/p>/i
  ]

  let title = defaultTitle
  let description: string | undefined
  let metaTitle: string | undefined
  let metaDescription: string | undefined

  // Extract title
  for (const pattern of titlePatterns) {
    const match = sourceCode.match(pattern)
    if (match) {
      title = match[1].trim()
      break
    }
  }

  // Extract description
  for (const pattern of descriptionPatterns) {
    const match = sourceCode.match(pattern)
    if (match) {
      description = match[1].trim()
      break
    }
  }

  // Extract meta title and description
  const metaTitleMatch = sourceCode.match(/<meta\s+property=['"`]og:title['"`]\s+content=['"`]([^'"`]+)['"`]/i)
  if (metaTitleMatch) {
    metaTitle = metaTitleMatch[1].trim()
  }

  const metaDescMatch = sourceCode.match(/<meta\s+property=['"`]og:description['"`]\s+content=['"`]([^'"`]+)['"`]/i)
  if (metaDescMatch) {
    metaDescription = metaDescMatch[1].trim()
  }

  return {
    title,
    description,
    metaTitle: metaTitle || title,
    metaDescription: metaDescription || description
  }
}

// Convert React component to page builder components
export function convertToPageBuilderComponents(sourceCode: string, slug: string): Component[] {
  const components: Component[] = []
  let componentId = 1

  // Helper function to create a component
  const createComponent = (
    type: string,
    x: number,
    y: number,
    width: number,
    height: number,
    content: any,
    styles: any = {}
  ): Component => ({
    id: `${slug}-${type}-${componentId++}`,
    type,
    position: { x, y, width, height },
    content,
    styles: {
      backgroundColor: '#ffffff',
      padding: '20px',
      borderRadius: '8px',
      ...styles
    },
    isLocked: false,
    isVisible: true,
    zIndex: 1
  })

  // Extract hero sections
  const heroPatterns = [
    /<section[^>]*hero[^>]*>([\s\S]*?)<\/section>/gi,
    /<div[^>]*hero[^>]*>([\s\S]*?)<\/div>/gi,
    /<header[^>]*>([\s\S]*?)<\/header>/gi
  ]

  let yOffset = 0

  for (const pattern of heroPatterns) {
    const matches = sourceCode.matchAll(pattern)
    for (const match of matches) {
      const heroContent = match[1]
      
      // Extract title and subtitle
      const titleMatch = heroContent.match(/<h1[^>]*>([^<]+)<\/h1>/i)
      const subtitleMatch = heroContent.match(/<p[^>]*>([^<]+)<\/p>/i)
      const buttonMatch = heroContent.match(/<button[^>]*>([^<]+)<\/button>/i) || 
                         heroContent.match(/<a[^>]*button[^>]*>([^<]+)<\/a>/i)

      if (titleMatch) {
        components.push(createComponent('hero', 0, yOffset, 1200, 600, {
          title: titleMatch[1].trim(),
          subtitle: subtitleMatch?.[1]?.trim() || '',
          buttonText: buttonMatch?.[1]?.trim() || 'Learn More',
          buttonUrl: '#',
          backgroundType: 'gradient'
        }, {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: '#ffffff',
          textAlign: 'center'
        }))
        yOffset += 620
      }
    }
  }

  // Extract content sections
  const sectionPatterns = [
    /<section[^>]*>([\s\S]*?)<\/section>/gi,
    /<div[^>]*section[^>]*>([\s\S]*?)<\/div>/gi
  ]

  for (const pattern of sectionPatterns) {
    const matches = sourceCode.matchAll(pattern)
    for (const match of matches) {
      const sectionContent = match[1]
      
      // Skip hero sections (already processed)
      if (sectionContent.toLowerCase().includes('hero')) continue

      // Extract headings and content
      const headingMatch = sectionContent.match(/<h[2-6][^>]*>([^<]+)<\/h[2-6]>/i)
      const paragraphMatches = [...sectionContent.matchAll(/<p[^>]*>([^<]+)<\/p>/gi)]
      
      if (headingMatch) {
        const content = paragraphMatches.map(m => m[1].trim()).join('\n\n')
        
        components.push(createComponent('text', 0, yOffset, 1200, 300, {
          heading: headingMatch[1].trim(),
          content: content || 'Content goes here...',
          alignment: 'left'
        }))
        yOffset += 320
      }
    }
  }

  // Extract lists and features
  const listPatterns = [
    /<ul[^>]*>([\s\S]*?)<\/ul>/gi,
    /<ol[^>]*>([\s\S]*?)<\/ol>/gi
  ]

  for (const pattern of listPatterns) {
    const matches = sourceCode.matchAll(pattern)
    for (const match of matches) {
      const listContent = match[1]
      const items = [...listContent.matchAll(/<li[^>]*>([^<]+)<\/li>/gi)]
      
      if (items.length > 0) {
        components.push(createComponent('features', 0, yOffset, 1200, 400, {
          title: 'Features',
          features: items.map((item, index) => ({
            id: `feature-${index}`,
            title: item[1].trim(),
            description: 'Feature description',
            icon: '✓'
          }))
        }))
        yOffset += 420
      }
    }
  }

  // If no components were extracted, create a basic text component
  if (components.length === 0) {
    const metadata = extractPageMetadata(sourceCode, slug)
    components.push(createComponent('text', 0, 0, 1200, 400, {
      heading: metadata.title,
      content: metadata.description || 'This page content will be converted to editable components.',
      alignment: 'center'
    }))
  }

  return components
}

// Scan the app directory for pages
export async function scanAppDirectory(appPath: string = 'src/app'): Promise<ScannedPage[]> {
  const pages: ScannedPage[] = []
  
  try {
    // Helper function to recursively scan directories
    const scanDirectory = (dirPath: string, relativePath: string = '') => {
      const items = fs.readdirSync(dirPath, { withFileTypes: true })
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item.name)
        const currentRelativePath = path.join(relativePath, item.name).replace(/\\/g, '/')
        
        if (item.isDirectory()) {
          // Skip certain directories
          if (['api', 'admin', 'auth', 'preview'].includes(item.name)) {
            continue
          }
          
          // Recursively scan subdirectories
          scanDirectory(fullPath, currentRelativePath)
        } else if (item.isFile() && PAGE_PATTERNS.static.includes(item.name)) {
          // Found a page file
          const sourceCode = fs.readFileSync(fullPath, 'utf-8')
          const stats = fs.statSync(fullPath)
          
          // Determine slug from path
          let slug = relativePath.replace(/\\/g, '/')
          if (slug === '') slug = 'home' // Root page
          
          // Check if it's a dynamic page
          const isDynamic = item.name.includes('[') && item.name.includes(']')
          const hasParams = isDynamic || sourceCode.includes('useParams') || sourceCode.includes('params')
          
          // Extract metadata
          const metadata = extractPageMetadata(sourceCode, slug)
          
          // Convert to page builder components
          const components = convertToPageBuilderComponents(sourceCode, slug)
          
          pages.push({
            id: `scanned-${slug.replace(/[^a-zA-Z0-9]/g, '-')}`,
            title: metadata.title,
            slug: slug === 'home' ? '' : slug, // Empty slug for home page
            filePath: fullPath,
            description: metadata.description,
            metaTitle: metadata.metaTitle,
            metaDescription: metadata.metaDescription,
            isPublished: true, // Assume existing pages are published
            isHomePage: slug === 'home',
            layout: components,
            styles: {},
            seoSettings: {
              title: metadata.metaTitle,
              description: metadata.metaDescription,
              keywords: []
            },
            sourceCode,
            lastModified: stats.mtime,
            type: isDynamic ? 'dynamic' : 'static',
            hasParams
          })
        }
      }
    }
    
    // Start scanning from the app directory
    if (fs.existsSync(appPath)) {
      scanDirectory(appPath)
    }
    
  } catch (error) {
    console.error('Error scanning app directory:', error)
  }
  
  return pages
}

// Get page preview data
export function getPagePreviewData(page: ScannedPage): {
  thumbnail: string
  componentCount: number
  complexity: 'simple' | 'moderate' | 'complex'
} {
  const componentCount = page.layout.length
  
  let complexity: 'simple' | 'moderate' | 'complex' = 'simple'
  if (componentCount > 5) complexity = 'moderate'
  if (componentCount > 10) complexity = 'complex'
  
  // Generate a simple thumbnail representation
  const thumbnail = `data:image/svg+xml,${encodeURIComponent(`
    <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect width="300" height="200" fill="#f8f9fa"/>
      <rect x="20" y="20" width="260" height="40" fill="#e9ecef" rx="4"/>
      <rect x="20" y="80" width="120" height="20" fill="#dee2e6" rx="2"/>
      <rect x="20" y="110" width="200" height="20" fill="#dee2e6" rx="2"/>
      <rect x="20" y="140" width="160" height="20" fill="#dee2e6" rx="2"/>
      <text x="150" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="#6c757d">
        ${componentCount} components
      </text>
    </svg>
  `)}`
  
  return {
    thumbnail,
    componentCount,
    complexity
  }
}
