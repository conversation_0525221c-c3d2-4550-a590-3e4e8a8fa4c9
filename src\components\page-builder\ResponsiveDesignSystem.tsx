'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

// Breakpoint definitions
export const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
} as const

export type Breakpoint = keyof typeof breakpoints

interface ResponsiveContextType {
  currentBreakpoint: Breakpoint
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  windowSize: { width: number; height: number }
  orientation: 'portrait' | 'landscape'
}

const ResponsiveContext = createContext<ResponsiveContextType | undefined>(undefined)

export const useResponsive = () => {
  const context = useContext(ResponsiveContext)
  if (!context) {
    throw new Error('useResponsive must be used within a ResponsiveProvider')
  }
  return context
}

interface ResponsiveProviderProps {
  children: React.ReactNode
}

export const ResponsiveProvider: React.FC<ResponsiveProviderProps> = ({ children }) => {
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 })
  const [currentBreakpoint, setCurrentBreakpoint] = useState<Breakpoint>('lg')

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      
      setWindowSize({ width, height })
      
      // Determine current breakpoint
      if (width >= breakpoints['2xl']) {
        setCurrentBreakpoint('2xl')
      } else if (width >= breakpoints.xl) {
        setCurrentBreakpoint('xl')
      } else if (width >= breakpoints.lg) {
        setCurrentBreakpoint('lg')
      } else if (width >= breakpoints.md) {
        setCurrentBreakpoint('md')
      } else if (width >= breakpoints.sm) {
        setCurrentBreakpoint('sm')
      } else {
        setCurrentBreakpoint('xs')
      }
    }

    // Set initial size
    handleResize()

    // Add event listener
    window.addEventListener('resize', handleResize)
    
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const isMobile = currentBreakpoint === 'xs' || currentBreakpoint === 'sm'
  const isTablet = currentBreakpoint === 'md'
  const isDesktop = currentBreakpoint === 'lg' || currentBreakpoint === 'xl' || currentBreakpoint === '2xl'
  const orientation = windowSize.width > windowSize.height ? 'landscape' : 'portrait'

  const value = {
    currentBreakpoint,
    isMobile,
    isTablet,
    isDesktop,
    windowSize,
    orientation
  }

  return (
    <ResponsiveContext.Provider value={value}>
      {children}
    </ResponsiveContext.Provider>
  )
}

// Responsive utility components
interface ResponsiveProps {
  children: React.ReactNode
  breakpoint?: Breakpoint
  above?: Breakpoint
  below?: Breakpoint
  only?: Breakpoint[]
}

export const ShowAbove: React.FC<ResponsiveProps> = ({ children, breakpoint = 'md' }) => {
  const { currentBreakpoint } = useResponsive()
  const currentWidth = breakpoints[currentBreakpoint]
  const targetWidth = breakpoints[breakpoint]
  
  if (currentWidth >= targetWidth) {
    return <>{children}</>
  }
  
  return null
}

export const ShowBelow: React.FC<ResponsiveProps> = ({ children, breakpoint = 'md' }) => {
  const { currentBreakpoint } = useResponsive()
  const currentWidth = breakpoints[currentBreakpoint]
  const targetWidth = breakpoints[breakpoint]
  
  if (currentWidth < targetWidth) {
    return <>{children}</>
  }
  
  return null
}

export const ShowOnly: React.FC<ResponsiveProps> = ({ children, only = ['md'] }) => {
  const { currentBreakpoint } = useResponsive()
  
  if (only.includes(currentBreakpoint)) {
    return <>{children}</>
  }
  
  return null
}

// Responsive grid system
interface ResponsiveGridProps {
  children: React.ReactNode
  cols?: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
    '2xl'?: number
  }
  gap?: number
  className?: string
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({ 
  children, 
  cols = { xs: 1, sm: 2, md: 3, lg: 4 },
  gap = 4,
  className = ''
}) => {
  const { currentBreakpoint } = useResponsive()
  
  const getCurrentCols = () => {
    // Find the appropriate column count for current breakpoint
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs']
    const currentIndex = breakpointOrder.indexOf(currentBreakpoint)
    
    for (let i = currentIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i]
      if (cols[bp] !== undefined) {
        return cols[bp]
      }
    }
    
    return 1
  }
  
  const currentCols = getCurrentCols()
  
  return (
    <div 
      className={`grid gap-${gap} ${className}`}
      style={{ gridTemplateColumns: `repeat(${currentCols}, minmax(0, 1fr))` }}
    >
      {children}
    </div>
  )
}

// Responsive container
interface ResponsiveContainerProps {
  children: React.ReactNode
  maxWidth?: Breakpoint
  padding?: {
    xs?: string
    sm?: string
    md?: string
    lg?: string
    xl?: string
    '2xl'?: string
  }
  className?: string
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  maxWidth = 'xl',
  padding = { xs: 'px-4', sm: 'px-6', lg: 'px-8' },
  className = ''
}) => {
  const { currentBreakpoint } = useResponsive()
  
  const getCurrentPadding = () => {
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs']
    const currentIndex = breakpointOrder.indexOf(currentBreakpoint)
    
    for (let i = currentIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i]
      if (padding[bp]) {
        return padding[bp]
      }
    }
    
    return 'px-4'
  }
  
  const maxWidthClass = `max-w-${maxWidth === '2xl' ? '7xl' : maxWidth === 'xl' ? '6xl' : maxWidth === 'lg' ? '5xl' : maxWidth === 'md' ? '4xl' : maxWidth === 'sm' ? '3xl' : '2xl'}`
  
  return (
    <div className={`mx-auto ${maxWidthClass} ${getCurrentPadding()} ${className}`}>
      {children}
    </div>
  )
}

// Responsive text sizing
interface ResponsiveTextProps {
  children: React.ReactNode
  size?: {
    xs?: string
    sm?: string
    md?: string
    lg?: string
    xl?: string
    '2xl'?: string
  }
  className?: string
}

export const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  size = { xs: 'text-sm', sm: 'text-base', md: 'text-lg', lg: 'text-xl' },
  className = ''
}) => {
  const { currentBreakpoint } = useResponsive()
  
  const getCurrentSize = () => {
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs']
    const currentIndex = breakpointOrder.indexOf(currentBreakpoint)
    
    for (let i = currentIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i]
      if (size[bp]) {
        return size[bp]
      }
    }
    
    return 'text-base'
  }
  
  return (
    <div className={`${getCurrentSize()} ${className}`}>
      {children}
    </div>
  )
}

// Touch-friendly components for mobile
export const TouchFriendlyButton: React.FC<{
  children: React.ReactNode
  onClick?: () => void
  className?: string
  disabled?: boolean
}> = ({ children, onClick, className = '', disabled = false }) => {
  const { isMobile } = useResponsive()
  
  const baseClasses = isMobile 
    ? 'min-h-[44px] min-w-[44px] text-base' // Touch-friendly minimum size
    : 'min-h-[36px] text-sm'
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} px-4 py-2 rounded-lg font-medium transition-colors ${className}`}
    >
      {children}
    </button>
  )
}

// Responsive spacing utility
export const useResponsiveSpacing = () => {
  const { currentBreakpoint } = useResponsive()
  
  const getSpacing = (config: {
    xs?: string
    sm?: string
    md?: string
    lg?: string
    xl?: string
    '2xl'?: string
  }) => {
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs']
    const currentIndex = breakpointOrder.indexOf(currentBreakpoint)
    
    for (let i = currentIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i]
      if (config[bp]) {
        return config[bp]
      }
    }
    
    return config.xs || 'p-4'
  }
  
  return { getSpacing }
}

// Responsive image component
export const ResponsiveImage: React.FC<{
  src: string
  alt: string
  sizes?: {
    xs?: string
    sm?: string
    md?: string
    lg?: string
    xl?: string
    '2xl'?: string
  }
  className?: string
}> = ({ src, alt, sizes, className = '' }) => {
  const { currentBreakpoint } = useResponsive()
  
  const getCurrentSize = () => {
    if (!sizes) return undefined
    
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs']
    const currentIndex = breakpointOrder.indexOf(currentBreakpoint)
    
    for (let i = currentIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i]
      if (sizes[bp]) {
        return sizes[bp]
      }
    }
    
    return undefined
  }
  
  const sizeClass = getCurrentSize()
  
  return (
    <img
      src={src}
      alt={alt}
      className={`${sizeClass || ''} ${className}`}
      loading="lazy"
    />
  )
}

// Accessibility helpers
export const useAccessibility = () => {
  const { isMobile } = useResponsive()

  const getFocusClasses = () => {
    return 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
  }

  const getInteractiveClasses = () => {
    return isMobile
      ? 'touch-manipulation select-none'
      : 'hover:transition-colors'
  }

  const getSkipLinkClasses = () => {
    return 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-lg z-50'
  }

  return {
    getFocusClasses,
    getInteractiveClasses,
    getSkipLinkClasses
  }
}
