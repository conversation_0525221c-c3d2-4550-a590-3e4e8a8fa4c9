'use client'

import React from 'react'
import { Component, usePageBuilderStore } from '@/lib/page-builder/store'
import { convertStyleToCSS, getResponsiveValue } from '@/lib/page-builder/utils'
import { componentRegistry } from '@/lib/page-builder/component-registry'

// Individual component renderers
import TextComponent from './components/TextComponent'
import ImageComponent from './components/ImageComponent'
import ButtonComponent from './components/ButtonComponent'
import ContainerComponent from './components/ContainerComponent'
import GridComponent from './components/GridComponent'
import HeroComponent from './components/HeroComponent'

interface ComponentRendererProps {
  component: Component
  isEditing?: boolean
  isSelected?: boolean
  isHovered?: boolean
  onSelect?: (id: string, multiSelect?: boolean) => void
  onHover?: (id: string | null) => void
  children?: React.ReactNode
}

const ComponentRenderer: React.FC<ComponentRendererProps> = ({
  component,
  isEditing = false,
  isSelected = false,
  isHovered = false,
  onSelect,
  onHover,
  children
}) => {
  const { viewport } = usePageBuilderStore()
  
  // Get responsive styles
  const responsiveStyles = Object.entries(component.styles).reduce((acc, [key, value]) => {
    acc[key] = getResponsiveValue(value, viewport)
    return acc
  }, {} as Record<string, any>)
  
  // Convert to CSS styles
  const cssStyles = convertStyleToCSS(responsiveStyles)
  
  // Add position styles for editing mode
  const positionStyles: React.CSSProperties = isEditing ? {
    position: 'absolute',
    left: component.position.x,
    top: component.position.y,
    width: component.position.width,
    height: component.position.height,
    zIndex: component.order
  } : {}
  
  // Add selection and hover styles
  const interactionStyles: React.CSSProperties = isEditing ? {
    outline: isSelected ? '2px solid #3b82f6' : isHovered ? '1px solid #93c5fd' : 'none',
    outlineOffset: '2px'
  } : {}
  
  // Combine all styles
  const finalStyles: React.CSSProperties = {
    ...cssStyles,
    ...positionStyles,
    ...interactionStyles,
    visibility: component.isVisible ? 'visible' : 'hidden',
    pointerEvents: component.isLocked && isEditing ? 'none' : 'auto'
  }
  
  // Handle click events
  const handleClick = (e: React.MouseEvent) => {
    if (isEditing && onSelect) {
      e.stopPropagation()
      onSelect(component.id, e.ctrlKey || e.metaKey)
    }
  }
  
  // Handle hover events
  const handleMouseEnter = () => {
    if (isEditing && onHover) {
      onHover(component.id)
    }
  }
  
  const handleMouseLeave = () => {
    if (isEditing && onHover) {
      onHover(null)
    }
  }
  
  // Get component config
  const config = componentRegistry[component.type]
  
  // Render the appropriate component
  const renderComponent = () => {
    const commonProps = {
      component,
      isEditing,
      isSelected,
      isHovered,
      config
    }
    
    switch (component.type) {
      case 'text':
        return <TextComponent {...commonProps} />
      
      case 'image':
        return <ImageComponent {...commonProps} />
      
      case 'button':
        return <ButtonComponent {...commonProps} />
      
      case 'container':
        return (
          <ContainerComponent {...commonProps}>
            {children}
          </ContainerComponent>
        )
      
      case 'grid':
        return (
          <GridComponent {...commonProps}>
            {children}
          </GridComponent>
        )
      
      case 'hero':
        return (
          <HeroComponent {...commonProps}>
            {children}
          </HeroComponent>
        )
      
      case 'column':
        return (
          <div className="flex flex-col h-full">
            {children}
          </div>
        )
      
      case 'row':
        return (
          <div className="flex flex-row h-full">
            {children}
          </div>
        )
      
      case 'card':
        return (
          <div className="bg-white rounded-lg shadow-md p-4">
            {children}
          </div>
        )
      
      case 'list':
        return (
          <ul className="list-disc list-inside">
            {children}
          </ul>
        )
      
      case 'form':
        return (
          <form className="space-y-4">
            {children}
          </form>
        )
      
      case 'video':
        return (
          <video 
            controls 
            className="w-full h-full object-cover"
            src={component.content.src}
          >
            Your browser does not support the video tag.
          </video>
        )
      
      case 'embed':
        return (
          <div 
            className="w-full h-full"
            dangerouslySetInnerHTML={{ __html: component.content.embedCode || '' }}
          />
        )
      
      case 'spacer':
        return <div className="w-full h-full" />
      
      case 'divider':
        return (
          <hr 
            className="border-gray-300"
            style={{ 
              borderWidth: component.content.thickness || 1,
              borderColor: component.content.color || '#d1d5db'
            }}
          />
        )
      
      default:
        return (
          <div className="flex items-center justify-center bg-gray-100 border-2 border-dashed border-gray-300 text-gray-500">
            <span>Unknown component: {component.type}</span>
          </div>
        )
    }
  }
  
  return (
    <div
      style={finalStyles}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={`
        ${isEditing ? 'cursor-pointer' : ''}
        ${isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''}
        ${isHovered && !isSelected ? 'ring-1 ring-blue-300' : ''}
        ${component.isLocked ? 'opacity-75' : ''}
      `}
      data-component-id={component.id}
      data-component-type={component.type}
    >
      {renderComponent()}
      
      {/* Selection indicator */}
      {isEditing && isSelected && (
        <div className="absolute -top-6 left-0 bg-blue-500 text-white text-xs px-2 py-1 rounded pointer-events-none">
          {config.name}
        </div>
      )}
      
      {/* Resize handles */}
      {isEditing && isSelected && config.resizable && (
        <>
          {/* Corner handles */}
          <div className="absolute -top-1 -left-1 w-2 h-2 bg-blue-500 border border-white cursor-nw-resize" />
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 border border-white cursor-ne-resize" />
          <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 border border-white cursor-sw-resize" />
          <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-blue-500 border border-white cursor-se-resize" />
          
          {/* Edge handles */}
          <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-500 border border-white cursor-n-resize" />
          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-500 border border-white cursor-s-resize" />
          <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 border border-white cursor-w-resize" />
          <div className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 border border-white cursor-e-resize" />
        </>
      )}
    </div>
  )
}

export default ComponentRenderer
