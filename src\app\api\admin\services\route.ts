import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  paginatedResponse,
  successResponse,
  requireAdmin,
  validateRequest,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery
} from '@/lib/api-utils'
import { createServiceSchema } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/services - List all services with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}

  // Add search functionality
  if (search && search.trim()) {
    const searchQuery = buildSearchQuery(search.trim(), ['name', 'description'])
    Object.assign(where, searchQuery)
  }
  
  // Add filter for active/inactive services
  if (filter === 'active') {
    where.isactive = true
  } else if (filter === 'inactive') {
    where.isactive = false
  }

  // Build orderBy clause
  const orderBy: any = {}
  if (sortBy) {
    orderBy[sortBy] = sortOrder || 'asc'
  } else {
    orderBy.displayorder = 'asc'
  }

  const [services, total] = await Promise.all([
    prisma.services.findMany({
      where,
      skip,
      take,
      orderBy,
      include: {
        categories: {
          select: {
            id: true,
            categname: true,
            categdesc: true,
          },
        },
        serviceoptions: {
          select: {
            id: true,
            optname: true,
            optprice: true,
          },
        },
        _count: {
          select: {
            orderdetails: true,
            serviceoptions: true,
          },
        },
      },
    }),
    prisma.services.count({ where }),
  ])

  // Transform the data for frontend
  const transformedServices = services.map(service => transformFromDbFields.service(service))

  return paginatedResponse(transformedServices, page, take, total, 'Services retrieved successfully')
})

// POST /api/admin/services - Create a new service
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const validate = validateRequest(createServiceSchema)
  const data = await validate(request)

  // Check if category exists
  const category = await prisma.categories.findUnique({
    where: { id: Number(data.categoryId) },
  })

  if (!category) {
    throw new Error('Category not found')
  }

  const service = await prisma.services.create({
    data: transformToDbFields.service(data),
    include: {
      categories: {
        select: {
          id: true,
          categname: true,
          categdesc: true,
        },
      },
      serviceoptions: {
        select: {
          id: true,
          optname: true,
          optprice: true,
        },
      },
      _count: {
        select: {
          orderdetails: true,
          serviceoptions: true,
        },
      },
    },
  })

  const transformedService = transformFromDbFields.service(service)
  return successResponse(transformedService, 'Service created successfully', 201)
})

// POST /api/admin/services/bulk-delete - Bulk delete services
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const { ids } = await request.json()

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid or empty IDs array')
  }

  // Check if any services are being used in orders
  const servicesInUse = await prisma.services.findMany({
    where: {
      id: { in: ids.map(Number) },
      orderdetails: { some: {} },
    },
    select: { id: true, name: true },
  })

  if (servicesInUse.length > 0) {
    throw new Error(
      `Cannot delete services that are in use: ${servicesInUse.map(s => s.name).join(', ')}`
    )
  }

  // Delete service options first
  await prisma.serviceoptions.deleteMany({
    where: { servid: { in: ids.map(Number) } },
  })

  // Delete services
  const result = await prisma.services.deleteMany({
    where: { id: { in: ids.map(Number) } },
  })

  return successResponse(
    { deletedCount: result.count },
    `${result.count} service(s) deleted successfully`
  )
})
