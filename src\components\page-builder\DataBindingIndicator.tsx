'use client'

import React, { useState } from 'react'
import { useDataBindingStore } from '@/lib/page-builder/data-binding-store'
import { Component } from '@/lib/page-builder/store'
import {
  CircleStackIcon,
  LinkIcon,
  EyeIcon,
  XMarkIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

interface DataBindingIndicatorProps {
  component: Component
  className?: string
}

const DataBindingIndicator: React.FC<DataBindingIndicatorProps> = ({ 
  component, 
  className = '' 
}) => {
  const [showDetails, setShowDetails] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  
  const { 
    getBindingsByComponent, 
    removeBinding, 
    refreshBinding,
    getPreviewData,
    dataSources
  } = useDataBindingStore()
  
  const bindings = getBindingsByComponent(component.id)
  
  if (bindings.length === 0) {
    return null
  }
  
  const handleRefreshBinding = async (bindingId: string) => {
    setIsRefreshing(true)
    try {
      await refreshBinding(bindingId)
    } catch (error) {
      console.error('Failed to refresh binding:', error)
    } finally {
      setIsRefreshing(false)
    }
  }
  
  const handleRemoveBinding = (bindingId: string) => {
    if (confirm('Are you sure you want to remove this data binding?')) {
      removeBinding(bindingId)
    }
  }
  
  const getBindingStatus = (binding: any) => {
    const dataSource = dataSources.find(ds => ds.name === binding.source)
    const previewData = getPreviewData(binding.source)
    
    if (!dataSource?.isConnected) {
      return { status: 'error', message: 'Data source not connected' }
    }
    
    if (previewData.length === 0) {
      return { status: 'warning', message: 'No data available' }
    }
    
    return { status: 'success', message: `${previewData.length} records available` }
  }
  
  return (
    <div className={`relative ${className}`}>
      {/* Main Indicator */}
      <button
        onClick={() => setShowDetails(!showDetails)}
        className="flex items-center space-x-1 px-2 py-1 bg-blue-100 text-blue-700 rounded-md text-xs font-medium hover:bg-blue-200 transition-colors"
        title={`${bindings.length} data binding${bindings.length > 1 ? 's' : ''}`}
      >
        <CircleStackIcon className="h-3 w-3" />
        <span>{bindings.length}</span>
        <LinkIcon className="h-3 w-3" />
      </button>

      {/* Details Popup */}
      {showDetails && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-semibold text-gray-900">Data Bindings</h4>
              <button
                onClick={() => setShowDetails(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="space-y-3">
              {bindings.map((binding) => {
                const status = getBindingStatus(binding)
                const dataSource = dataSources.find(ds => ds.name === binding.source)
                const previewData = getPreviewData(binding.source)

                return (
                  <div key={binding.id} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <CircleStackIcon className="h-4 w-4 text-gray-500" />
                        <span className="text-sm font-medium text-gray-900">
                          {dataSource?.displayName || binding.source}
                        </span>
                        {binding.field && (
                          <>
                            <span className="text-gray-400">→</span>
                            <span className="text-sm text-gray-600">{binding.field}</span>
                          </>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => handleRefreshBinding(binding.id)}
                          disabled={isRefreshing}
                          className="p-1 text-gray-400 hover:text-blue-600 disabled:opacity-50"
                          title="Refresh data"
                        >
                          <ArrowPathIcon className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} />
                        </button>
                        
                        <button
                          onClick={() => handleRemoveBinding(binding.id)}
                          className="p-1 text-gray-400 hover:text-red-600"
                          title="Remove binding"
                        >
                          <XMarkIcon className="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                    
                    {/* Status */}
                    <div className="flex items-center space-x-2 mb-2">
                      {status.status === 'success' && (
                        <CheckCircleIcon className="h-4 w-4 text-green-500" />
                      )}
                      {status.status === 'warning' && (
                        <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />
                      )}
                      {status.status === 'error' && (
                        <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
                      )}
                      <span className={`text-xs ${
                        status.status === 'success' ? 'text-green-600' :
                        status.status === 'warning' ? 'text-yellow-600' :
                        'text-red-600'
                      }`}>
                        {status.message}
                      </span>
                    </div>
                    
                    {/* Query Info */}
                    {binding.query && (
                      <div className="text-xs text-gray-500 mb-2">
                        {binding.query.limit && `Limit: ${binding.query.limit}`}
                        {binding.query.sort && ` • Sort: ${binding.query.sort.field} ${binding.query.sort.direction}`}
                        {binding.query.filters && ` • Filters: ${Object.keys(binding.query.filters).length}`}
                      </div>
                    )}
                    
                    {/* Preview Data */}
                    {previewData.length > 0 && (
                      <div className="mt-2">
                        <button
                          onClick={() => {
                            const preview = document.getElementById(`preview-${binding.id}`)
                            if (preview) {
                              preview.style.display = preview.style.display === 'none' ? 'block' : 'none'
                            }
                          }}
                          className="flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800"
                        >
                          <EyeIcon className="h-3 w-3" />
                          <span>Preview Data</span>
                        </button>
                        
                        <div id={`preview-${binding.id}`} className="mt-2 hidden">
                          <div className="bg-gray-50 rounded p-2 max-h-32 overflow-y-auto">
                            <pre className="text-xs text-gray-600">
                              {JSON.stringify(previewData.slice(0, 2), null, 2)}
                            </pre>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Last Updated */}
                    <div className="text-xs text-gray-400 mt-2">
                      Last updated: {binding.lastUpdated.toLocaleString()}
                    </div>
                  </div>
                )
              })}
            </div>
            
            {/* Summary */}
            <div className="mt-4 pt-3 border-t border-gray-200">
              <div className="text-xs text-gray-500">
                Total bindings: {bindings.length} • 
                Active sources: {new Set(bindings.map(b => b.source)).size}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default DataBindingIndicator
