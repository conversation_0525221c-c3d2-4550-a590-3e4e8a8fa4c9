import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON>rror<PERSON>and<PERSON>,
  paginatedResponse,
  successResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/technologies - List all technologies with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search && search.trim()) {
    const searchQuery = buildSearchQuery(search.trim(), [
      'name', 
      'description'
    ])
    Object.assign(where, searchQuery)
  }

  // Add filters
  if (filter) {
    try {
      const filters = JSON.parse(filter)
      if (filters.isActive !== undefined) where.isactive = filters.isActive === 'true'
    } catch (e) {
      // Invalid filter JSON, ignore
    }
  }

  // Build orderBy clause with field name mapping
  const orderBy: any = {}
  if (sortBy) {
    // Map frontend field names to database field names
    const fieldMapping: { [key: string]: string } = {
      'updatedAt': 'updatedat',
      'createdAt': 'createdat',
      'displayOrder': 'displayorder',
      'isActive': 'isactive',
      'iconUrl': 'iconurl'
    }
    const dbField = fieldMapping[sortBy] || sortBy
    orderBy[dbField] = sortOrder || 'asc'
  } else {
    orderBy.updatedat = 'desc' // Default sort by last updated
  }

  const [technologies, total] = await Promise.all([
    prisma.technologies.findMany({
      where,
      skip,
      take,
      orderBy,
      include: {
        projecttechnologies: {
          include: {
            projects: {
              select: {
                id: true,
                name: true,
                status: true,
              },
            },
          },
          take: 5,
        },
        _count: {
          select: {
            projecttechnologies: true,
          },
        },
      },
    }),
    prisma.technologies.count({ where }),
  ])

  // Transform the data for frontend
  const transformedTechnologies = technologies.map(tech => transformFromDbFields.technology(tech))
  
  return paginatedResponse(transformedTechnologies, total, page, limit)
})

// POST /api/admin/technologies - Create a new technology
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.technology?.create || schemas.category.create)
  const data = await validate(request)

  // Check if a technology with the same name already exists
  const existingTechnology = await prisma.technologies.findFirst({
    where: {
      name: data.name,
    },
  })

  if (existingTechnology) {
    throw new Error('A technology with this name already exists')
  }

  const technology = await prisma.technologies.create({
    data: transformToDbFields.technology(data),
    include: {
      _count: {
        select: {
          projecttechnologies: true,
        },
      },
    },
  })

  const transformedTechnology = transformFromDbFields.technology(technology)
  return successResponse(transformedTechnology, 'Technology created successfully', 201)
})

// PUT /api/admin/technologies - Bulk update technologies
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    return NextResponse.json(
      { success: false, error: 'Invalid technology IDs provided' },
      { status: 400 }
    )
  }

  const updatedTechnologies = await prisma.technologies.updateMany({
    where: {
      id: {
        in: ids.map(Number),
      },
    },
    data: transformToDbFields.technology(data),
  })

  return successResponse(
    { count: updatedTechnologies.count },
    `${updatedTechnologies.count} technologies updated successfully`
  )
})

// DELETE /api/admin/technologies - Bulk delete technologies
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    return NextResponse.json(
      { success: false, error: 'Invalid technology IDs provided' },
      { status: 400 }
    )
  }

  // Check if any technologies have associated projects
  const technologiesWithProjects = await prisma.technologies.findMany({
    where: {
      id: { in: ids.map(Number) },
      projecttechnologies: { some: {} },
    },
    select: { id: true, name: true },
  })

  if (technologiesWithProjects.length > 0) {
    const techNames = technologiesWithProjects.map(t => t.name).join(', ')
    return NextResponse.json(
      { 
        success: false, 
        error: `Cannot delete technologies with associated projects: ${techNames}. Please remove them from projects first.` 
      },
      { status: 400 }
    )
  }

  const deletedTechnologies = await prisma.technologies.deleteMany({
    where: {
      id: {
        in: ids.map(Number),
      },
    },
  })

  return successResponse(
    { count: deletedTechnologies.count },
    `${deletedTechnologies.count} technologies deleted successfully`
  )
})
