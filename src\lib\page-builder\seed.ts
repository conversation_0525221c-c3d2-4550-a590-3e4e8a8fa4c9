import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function seedPageBuilder() {
  try {
    console.log('🌱 Seeding page builder data...')
    
    // Check if admin user exists
    const adminUser = await prisma.users.findFirst({
      where: { role: 'ADMIN' }
    })
    
    if (!adminUser) {
      console.log('❌ No admin user found. Please create an admin user first.')
      return
    }
    
    // Create sample pages
    const homePage = await prisma.pages.upsert({
      where: { slug: 'home' },
      update: {},
      create: {
        title: 'Home Page',
        slug: 'home',
        description: 'Main landing page for the website',
        metaTitle: 'Welcome to Technoloway',
        metaDescription: 'Leading software development company specializing in modern web applications, mobile apps, and enterprise solutions.',
        isPublished: true,
        isHomePage: true,
        layout: {
          components: [],
          version: '1.0.0'
        },
        styles: {
          backgroundColor: '#ffffff',
          fontFamily: 'Inter, sans-serif'
        },
        seoSettings: {
          keywords: 'software development, web applications, mobile apps',
          author: 'Technoloway',
          robots: 'index,follow'
        },
        createdBy: adminUser.id
      }
    })
    
    const aboutPage = await prisma.pages.upsert({
      where: { slug: 'about' },
      update: {},
      create: {
        title: 'About Us',
        slug: 'about',
        description: 'Learn more about our company and team',
        metaTitle: 'About Technoloway - Our Story',
        metaDescription: 'Discover the story behind Technoloway, our mission, values, and the talented team that makes it all possible.',
        isPublished: true,
        isHomePage: false,
        layout: {
          components: [],
          version: '1.0.0'
        },
        styles: {
          backgroundColor: '#ffffff',
          fontFamily: 'Inter, sans-serif'
        },
        seoSettings: {
          keywords: 'about us, company story, team, mission, values',
          author: 'Technoloway',
          robots: 'index,follow'
        },
        createdBy: adminUser.id
      }
    })
    
    const servicesPage = await prisma.pages.upsert({
      where: { slug: 'services' },
      update: {},
      create: {
        title: 'Our Services',
        slug: 'services',
        description: 'Explore our comprehensive range of software development services',
        metaTitle: 'Software Development Services - Technoloway',
        metaDescription: 'Professional software development services including web applications, mobile apps, enterprise solutions, and more.',
        isPublished: true,
        isHomePage: false,
        layout: {
          components: [],
          version: '1.0.0'
        },
        styles: {
          backgroundColor: '#ffffff',
          fontFamily: 'Inter, sans-serif'
        },
        seoSettings: {
          keywords: 'software services, web development, mobile development, enterprise solutions',
          author: 'Technoloway',
          robots: 'index,follow'
        },
        createdBy: adminUser.id
      }
    })
    
    const contactPage = await prisma.pages.upsert({
      where: { slug: 'contact' },
      update: {},
      create: {
        title: 'Contact Us',
        slug: 'contact',
        description: 'Get in touch with our team',
        metaTitle: 'Contact Technoloway - Get In Touch',
        metaDescription: 'Ready to start your next project? Contact our team of experts to discuss your software development needs.',
        isPublished: false,
        isHomePage: false,
        layout: {
          components: [],
          version: '1.0.0'
        },
        styles: {
          backgroundColor: '#ffffff',
          fontFamily: 'Inter, sans-serif'
        },
        seoSettings: {
          keywords: 'contact, get in touch, software development inquiry',
          author: 'Technoloway',
          robots: 'index,follow'
        },
        createdBy: adminUser.id
      }
    })
    
    // Create sample components for home page
    const heroComponent = await prisma.pageComponents.create({
      data: {
        pageId: homePage.id,
        componentId: 'hero-section-1',
        type: 'hero',
        name: 'Main Hero Section',
        content: {
          title: 'Welcome to Technoloway',
          subtitle: 'Leading software development company specializing in modern web applications, mobile apps, and enterprise solutions.',
          backgroundType: 'gradient',
          backgroundImage: '/placeholder-image.svg'
        },
        styles: {
          minHeight: '500px',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: '#ffffff',
          textAlign: 'center',
          padding: '80px 20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        },
        position: {
          x: 0,
          y: 0,
          width: 1200,
          height: 500
        },
        order: 1,
        isVisible: true
      }
    })
    
    const servicesComponent = await prisma.pageComponents.create({
      data: {
        pageId: homePage.id,
        componentId: 'services-section-1',
        type: 'container',
        name: 'Services Section',
        content: {
          tag: 'section'
        },
        styles: {
          backgroundColor: '#f8f9fa',
          padding: '80px 20px',
          textAlign: 'center'
        },
        position: {
          x: 0,
          y: 500,
          width: 1200,
          height: 400
        },
        order: 2,
        isVisible: true
      }
    })
    
    // Create initial versions for pages
    await prisma.pageVersions.create({
      data: {
        pageId: homePage.id,
        version: 1,
        title: 'Initial Version',
        description: 'Initial page setup with hero and services sections',
        layout: {
          components: [heroComponent.id.toString(), servicesComponent.id.toString()],
          version: '1.0.0'
        },
        createdBy: adminUser.id
      }
    })
    
    // Create component templates
    const heroTemplate = await prisma.componentTemplates.create({
      data: {
        name: 'Hero Section Template',
        type: 'hero',
        description: 'A beautiful hero section with gradient background',
        category: 'Headers',
        template: {
          type: 'hero',
          content: {
            title: 'Your Amazing Title',
            subtitle: 'Your compelling subtitle goes here',
            backgroundType: 'gradient'
          },
          styles: {
            minHeight: '500px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: '#ffffff',
            textAlign: 'center',
            padding: '80px 20px'
          }
        },
        isPublic: true,
        createdBy: adminUser.id
      }
    })
    
    const cardTemplate = await prisma.componentTemplates.create({
      data: {
        name: 'Service Card Template',
        type: 'card',
        description: 'A clean card design for showcasing services',
        category: 'Content',
        template: {
          type: 'card',
          content: {
            title: 'Service Title',
            description: 'Service description goes here',
            icon: '🚀'
          },
          styles: {
            backgroundColor: '#ffffff',
            padding: '30px',
            borderRadius: '12px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            textAlign: 'center'
          }
        },
        isPublic: true,
        createdBy: adminUser.id
      }
    })
    
    console.log('✅ Page builder data seeded successfully!')
    console.log(`📄 Created pages: ${[homePage, aboutPage, servicesPage, contactPage].length}`)
    console.log(`🧩 Created components: 2`)
    console.log(`📋 Created templates: 2`)
    
  } catch (error) {
    console.error('❌ Error seeding page builder data:', error)
    throw error
  }
}

// Run seeder if called directly
if (require.main === module) {
  seedPageBuilder()
    .then(() => {
      console.log('🎉 Page builder seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Page builder seeding failed:', error)
      process.exit(1)
    })
    .finally(async () => {
      await prisma.$disconnect()
    })
}
