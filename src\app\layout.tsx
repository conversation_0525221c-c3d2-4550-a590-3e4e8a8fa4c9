import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import AuthProvider from "@/components/providers/session-provider";
import { ThemeProvider } from "@/components/providers/theme-provider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: "Technoloway - Software Development Company",
    template: "%s | Technoloway",
  },
  description: "Leading software development company specializing in modern web applications, mobile apps, and enterprise solutions.",
  keywords: ["software development", "web development", "mobile apps", "enterprise solutions", "TypeScript", "React", "Next.js"],
  authors: [{ name: "Technoloway Team" }],
  creator: "Technolow<PERSON>",
  publisher: "Technoloway",
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || "https://technoloway.com"),
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "/",
    title: "Technoloway - Software Development Company",
    description: "Leading software development company specializing in modern web applications, mobile apps, and enterprise solutions.",
    siteName: "Technoloway",
  },
  twitter: {
    card: "summary_large_image",
    title: "Technoloway - Software Development Company",
    description: "Leading software development company specializing in modern web applications, mobile apps, and enterprise solutions.",
    creator: "@technoloway",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth" suppressHydrationWarning>
      <body className={`${inter.className} antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            {children}
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
