'use client'

import React, { useState, useEffect, useRef } from 'react'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  EnvelopeIcon,
  CheckIcon
} from '@heroicons/react/24/outline'
import { ContactFormModal } from './contact-form-modal'
import { CrudConfig } from '../crud/types'
import { motion, AnimatePresence } from 'framer-motion'

interface ContactForm {
  id: string
  name: string
  email: string
  phone?: string
  subject: string
  message: string
  isread?: boolean
  readat?: string
  status: string
  createdAt: string
  updatedAt: string
  [key: string]: any
}

interface ContactFormsManagerProps {
  config: CrudConfig<ContactForm>
}

export function ContactFormsManager({ config }: ContactFormsManagerProps) {
  const [contactForms, setContactForms] = useState<ContactForm[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedForms, setSelectedForms] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingForm, setEditingForm] = useState<ContactForm | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // View controls state
  const [viewMode, setViewMode] = useState('list')
  const [displayDensity, setDisplayDensity] = useState('comfortable')
  const [visibleColumns, setVisibleColumns] = useState([
    'name', 'subject', 'message', 'status', 'isread', 'createdat'
  ])
  const [sortBy, setSortBy] = useState('createdat')
  const [sortOrder, setSortOrder] = useState('desc')
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnMenu, setShowColumnMenu] = useState(false)
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [showSortMenu, setShowSortMenu] = useState(false)

  // Filter states
  const [statusFilter, setStatusFilter] = useState('')
  const [readStatusFilter, setReadStatusFilter] = useState('')

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch contact forms
  const fetchContactForms = async (preserveFocus = false) => {
    try {
      // Only show full loading for initial load, not for search
      if (!preserveFocus) {
        setLoading(true)
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        sortBy: sortBy,
        sortOrder: sortOrder,
        ...(debouncedSearchQuery && { search: debouncedSearchQuery }),
        ...(statusFilter && { status: statusFilter }),
        ...(readStatusFilter && { isread: readStatusFilter }),
      })

      console.log('Fetching contact forms with params:', params.toString()) // Debug log

      const response = await fetch(`/api/admin/${config.endpoint}?${params}`)
      if (!response.ok) throw new Error('Failed to fetch contact forms')

      const data = await response.json()
      console.log('Received contact forms data:', data) // Debug log

      setContactForms(data.data || [])
      setTotalPages(Math.ceil((data.total || 0) / (config.pageSize || 10)))
      setError(null) // Clear any previous errors on successful fetch
    } catch (err) {
      console.error('Error fetching contact forms:', err) // Debug log
      setError(err instanceof Error ? err.message : 'Failed to fetch contact forms')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Preserve focus when searching
    const isSearching = debouncedSearchQuery !== ''
    fetchContactForms(isSearching)
  }, [currentPage, debouncedSearchQuery, sortBy, sortOrder, statusFilter, readStatusFilter])

  // Handle create
  const handleCreate = async (formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to create contact form')

      setIsCreateModalOpen(false)
      fetchContactForms()
      alert('Contact form created successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create contact form'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle update
  const handleUpdate = async (id: string, formData: any) => {
    try {
      console.log('Updating contact form with data:', formData)

      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      console.log('Update response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Update error response:', errorData)
        throw new Error(errorData.error || `Failed to update contact form (${response.status})`)
      }

      const result = await response.json()
      console.log('Update success:', result)

      setIsEditModalOpen(false)
      setEditingForm(null)
      fetchContactForms()
      alert('Contact form updated successfully!')
    } catch (err) {
      console.error('Update error:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update contact form'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete contact form')
      }

      // Show success message
      setError(null)
      fetchContactForms()

      // Optional: Show success notification
      console.log('Contact form deleted successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete contact form'
      setError(errorMessage)
      console.error('Delete error:', err)
    }
  }

  // Handle individual actions
  const handleAction = async (action: string, item: ContactForm) => {
    const actionKey = `${action}-${item.id}`

    try {
      setActionLoading(actionKey)

      switch (action) {
        case 'view':
          // Open contact form details in new tab or modal
          window.open(`/admin/contact-forms/${item.id}`, '_blank')
          break

        case 'edit':
          setEditingForm(item)
          setIsEditModalOpen(true)
          break

        case 'reply':
          // Open email client or reply modal
          window.location.href = `mailto:${item.email}?subject=Re: ${item.subject}`
          break

        case 'mark-read':
          await handleToggleReadStatus(item.id, !item.isread)
          break

        case 'delete':
          const deleteAction = config.actions?.find(a => a.action === 'delete')
          const confirmMessage = deleteAction?.confirmationMessage || 'Are you sure you want to delete this contact form? This action cannot be undone.'

          if (window.confirm(confirmMessage)) {
            await handleDelete(item.id)
          }
          break

        default:
          console.warn(`Unknown action: ${action}`)
      }
    } finally {
      setActionLoading(null)
    }
  }

  // Toggle contact form read status
  const handleToggleReadStatus = async (id: string, isread: boolean) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isread }),
      })

      if (!response.ok) throw new Error('Failed to update contact form read status')

      fetchContactForms()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update contact form read status')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  // Handle bulk actions
  const handleBulkAction = async (action: string, formIds: string[]) => {
    try {
      let endpoint = `/api/admin/${config.endpoint}`
      let method = 'PUT'
      let body: any = { ids: formIds }

      switch (action) {
        case 'mark-read':
          body.data = { isread: true }
          break
        case 'mark-unread':
          body.data = { isread: false }
          break
        case 'mark-resolved':
          body.data = { status: 'Resolved' }
          break
        case 'delete':
          method = 'DELETE'
          body = { ids: formIds }
          break
        default:
          throw new Error(`Unknown bulk action: ${action}`)
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to ${action} contact forms`)
      }

      const result = await response.json()

      if (result.success) {
        setSelectedForms([])
        fetchContactForms()
        // Show success notification
      } else {
        throw new Error(result.error || `Failed to ${action} contact forms`)
      }
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error)
      throw error
    }
  }

  // Handle select all
  const handleSelectAll = () => {
    if (selectedForms.length === contactForms.length) {
      setSelectedForms([])
    } else {
      setSelectedForms(contactForms.map(form => String(form.id)))
    }
  }

  // Handle select individual form
  const handleSelectForm = (formId: string) => {
    setSelectedForms(prev =>
      prev.includes(formId)
        ? prev.filter(id => id !== formId)
        : [...prev, formId]
    )
  }

  // View control functions
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const handleColumnToggle = (columnKey) => {
    const isVisible = visibleColumns.includes(columnKey)
    if (isVisible) {
      setVisibleColumns(prev => prev.filter(col => col !== columnKey))
    } else {
      setVisibleColumns(prev => [...prev, columnKey])
    }
  }

  const resetViewSettings = () => {
    setVisibleColumns(['name', 'subject', 'message', 'status', 'isread', 'createdat'])
    setViewMode('list')
    setDisplayDensity('comfortable')
    setSortBy('createdat')
    setSortOrder('desc')
  }

  const availableColumns = [
    { key: 'name', label: 'Contact Info', hideable: false },
    { key: 'subject', label: 'Subject', hideable: true },
    { key: 'message', label: 'Message', hideable: true },
    { key: 'status', label: 'Status', hideable: true },
    { key: 'isread', label: 'Read Status', hideable: true },
    { key: 'createdat', label: 'Submitted', hideable: true },
    { key: 'updatedat', label: 'Last Updated', hideable: true },
  ]

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    card: RectangleStackIcon,
  }

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  }

  // Get density-based classes
  const getDensityClasses = () => {
    return displayDensity === 'compact'
      ? 'px-4 py-2'
      : 'px-6 py-4'
  }

  const getHeaderDensityClasses = () => {
    return displayDensity === 'compact'
      ? 'px-4 py-2'
      : 'px-6 py-3'
  }

  const getImageSize = () => {
    return displayDensity === 'compact'
      ? 'h-8 w-8'
      : 'h-10 w-10'
  }

  const getTextSize = () => {
    return displayDensity === 'compact'
      ? 'text-xs'
      : 'text-sm'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6 mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
          {config.description && (
            <p className="text-gray-600">{config.description}</p>
          )}
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <span>+</span>
          <span>Add Contact Form</span>
        </button>
      </div>

      {/* Comprehensive Search and Controls Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-4">
        {/* Search and Primary Controls */}
        <div className="flex items-center justify-between space-x-4">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                placeholder={config.searchPlaceholder || 'Search contact forms by name, email, subject...'}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
              {/* Search Loading Indicator */}
              {searchQuery !== debouncedSearchQuery && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                </div>
              )}
              {/* Clear Search Button */}
              {searchQuery && (
                <button
                  onClick={() => {
                    setSearchQuery('')
                    searchInputRef.current?.focus()
                  }}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 border rounded-lg transition-colors flex items-center space-x-2 ${
                showFilters
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2 bg-gray-50 border border-gray-200 rounded-lg p-1">
            {/* View Mode Controls */}
            <div className="flex items-center bg-white rounded-md p-1">
              {Object.entries(viewModeIcons).map(([mode, Icon]) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode as 'list' | 'grid' | 'card')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === mode
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
                >
                  <Icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            {/* Display Density Controls */}
            <div className="relative">
              <button
                onClick={() => setShowDensityMenu(!showDensityMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Display density"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>{densityLabels[displayDensity]}</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showDensityMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10"
                  >
                    <div className="py-1">
                      {Object.entries(densityLabels).map(([density, label]) => (
                        <button
                          key={density}
                          onClick={() => {
                            setDisplayDensity(density as 'compact' | 'comfortable')
                            setShowDensityMenu(false)
                          }}
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${
                            displayDensity === density ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                          }`}
                        >
                          {label}
                        </button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Column Visibility Controls */}
            <div className="relative">
              <button
                onClick={() => setShowColumnMenu(!showColumnMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Column visibility"
              >
                <EyeIcon className="h-4 w-4" />
                <span>Columns</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showColumnMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg border border-gray-200 z-10"
                  >
                    <div className="py-2">
                      <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-100">
                        Show/Hide Columns
                      </div>
                      {availableColumns.map((column) => (
                        <label
                          key={column.key}
                          className="flex items-center px-4 py-2 hover:bg-gray-50 cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            checked={visibleColumns.includes(column.key)}
                            onChange={() => handleColumnToggle(column.key)}
                            disabled={!column.hideable}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className={`ml-3 text-sm ${!column.hideable ? 'text-gray-400' : 'text-gray-700'}`}>
                            {column.label}
                          </span>
                        </label>
                      ))}
                      <div className="border-t border-gray-100 mt-2 pt-2">
                        <button
                          onClick={() => {
                            resetViewSettings()
                            setShowColumnMenu(false)
                          }}
                          className="w-full text-left px-4 py-2 text-sm text-blue-600 hover:bg-blue-50"
                        >
                          Reset to Default
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Filters Section */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border-t border-gray-200 pt-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Status Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="">All Status</option>
                    <option value="New">New</option>
                    <option value="In Progress">In Progress</option>
                    <option value="Resolved">Resolved</option>
                    <option value="Closed">Closed</option>
                  </select>
                </div>

                {/* Read Status Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Read Status</label>
                  <select
                    value={readStatusFilter}
                    onChange={(e) => setReadStatusFilter(e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="">All</option>
                    <option value="true">Read</option>
                    <option value="false">Unread</option>
                  </select>
                </div>

                {/* Clear Filters */}
                <div className="flex items-end">
                  <button
                    onClick={() => {
                      setStatusFilter('')
                      setReadStatusFilter('')
                      setSearchQuery('')
                    }}
                    className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Clear Filters
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Bulk Actions */}
        {selectedForms.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-blue-50 border border-blue-200 rounded-lg p-4"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-blue-900">
                  {selectedForms.length} contact form{selectedForms.length !== 1 ? 's' : ''} selected
                </span>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleBulkAction('mark-read', selectedForms)}
                    className="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    Mark as Read
                  </button>
                  <button
                    onClick={() => handleBulkAction('mark-unread', selectedForms)}
                    className="px-3 py-1 text-xs bg-yellow-600 text-white rounded hover:bg-yellow-700"
                  >
                    Mark as Unread
                  </button>
                  <button
                    onClick={() => handleBulkAction('mark-resolved', selectedForms)}
                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Mark as Resolved
                  </button>
                  <button
                    onClick={() => {
                      if (window.confirm('Are you sure you want to delete the selected contact forms? This action cannot be undone.')) {
                        handleBulkAction('delete', selectedForms)
                      }
                    }}
                    className="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    Delete Selected
                  </button>
                </div>
              </div>
              <button
                onClick={() => setSelectedForms([])}
                className="text-blue-600 hover:text-blue-800"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          </motion.div>
        )}
      </div>

      {/* Contact Forms Display */}
      {viewMode === 'list' ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className={`w-12 ${getHeaderDensityClasses()} text-left`}>
                    <input
                      type="checkbox"
                      checked={selectedForms.length === contactForms.length && contactForms.length > 0}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </th>

                  {availableColumns
                    .filter(column => visibleColumns.includes(column.key))
                    .map((column) => (
                      <th
                        key={column.key}
                        className={`${getHeaderDensityClasses()} text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100`}
                        onClick={() => handleSort(column.key)}
                      >
                        <div className="flex items-center space-x-1">
                          <span>{column.label}</span>
                          {sortBy === column.key && (
                            sortOrder === 'asc' ? (
                              <ArrowUpIcon className="h-3 w-3" />
                            ) : (
                              <ArrowDownIcon className="h-3 w-3" />
                            )
                          )}
                        </div>
                      </th>
                    ))}

                  <th className={`${getHeaderDensityClasses()} text-right text-xs font-medium text-gray-500 uppercase tracking-wider`}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {contactForms.length === 0 ? (
                  <tr>
                    <td colSpan={visibleColumns.length + 2} className="px-6 py-12 text-center text-gray-500">
                      {debouncedSearchQuery || statusFilter || readStatusFilter ? 'No contact forms found matching your criteria.' : 'No contact forms found.'}
                    </td>
                  </tr>
                ) : (
                  contactForms.map((form) => (
                    <tr
                      key={form.id}
                      className={`hover:bg-gray-50 ${
                        selectedForms.includes(form.id.toString()) ? 'bg-blue-50' : ''
                      }`}
                    >
                      <td className={`${getDensityClasses()} whitespace-nowrap`}>
                        <input
                          type="checkbox"
                          checked={selectedForms.includes(form.id.toString())}
                          onChange={() => handleSelectForm(form.id.toString())}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>

                      {availableColumns
                        .filter(column => visibleColumns.includes(column.key))
                        .map((column) => (
                          <td key={column.key} className={`${getDensityClasses()} whitespace-nowrap`}>
                            {column.key === 'name' && (
                              <div className="flex items-center">
                                <div className={`flex-shrink-0 ${getImageSize()}`}>
                                  <div className={`${getImageSize()} rounded-full bg-gray-200 flex items-center justify-center`}>
                                    <span className={`text-gray-500 ${getTextSize()} font-medium`}>
                                      {form.name.charAt(0)}
                                    </span>
                                  </div>
                                </div>
                                <div className={displayDensity === 'compact' ? 'ml-2' : 'ml-4'}>
                                  <div className={`${getTextSize()} font-medium text-gray-900`}>{form.name}</div>
                                  <div className={`${getTextSize()} text-gray-500`}>{form.email}</div>
                                  {displayDensity === 'comfortable' && form.phone && (
                                    <div className="text-xs text-gray-400">{form.phone}</div>
                                  )}
                                </div>
                              </div>
                            )}
                            {column.key === 'subject' && (
                              <div className={`${getTextSize()} text-gray-900`}>
                                {form.subject}
                              </div>
                            )}
                            {column.key === 'message' && (
                              <div className={`${getTextSize()} text-gray-900 max-w-xs`}>
                                <div className="truncate">{form.message}</div>
                              </div>
                            )}
                            {column.key === 'status' && (
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                form.status === 'Resolved' ? 'bg-green-100 text-green-800' :
                                form.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                                form.status === 'Closed' ? 'bg-gray-100 text-gray-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {form.status || 'New'}
                              </span>
                            )}
                            {column.key === 'isread' && (
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                form.isread ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {form.isread ? 'Read' : 'Unread'}
                              </span>
                            )}
                            {column.key === 'createdat' && form.createdAt && (
                              <div className={`${getTextSize()} text-gray-900`}>{formatDate(form.createdAt)}</div>
                            )}
                            {column.key === 'updatedat' && form.updatedAt && (
                              <div className={`${getTextSize()} text-gray-900`}>{formatDate(form.updatedAt)}</div>
                            )}
                          </td>
                        ))}

                      <td className={`${getDensityClasses()} whitespace-nowrap text-right text-sm font-medium`}>
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleAction('view', form)}
                            className="text-gray-400 hover:text-blue-600 transition-colors"
                            title="View"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleAction('reply', form)}
                            className="text-gray-400 hover:text-green-600 transition-colors"
                            title="Reply"
                          >
                            <EnvelopeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleAction('mark-read', form)}
                            className={`text-gray-400 hover:text-yellow-600 transition-colors ${
                              form.isread ? 'text-green-600' : ''
                            }`}
                            title={form.isread ? 'Mark as Unread' : 'Mark as Read'}
                          >
                            <CheckIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleAction('edit', form)}
                            className="text-gray-400 hover:text-blue-600 transition-colors"
                            title="Edit"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleAction('delete', form)}
                            className="text-gray-400 hover:text-red-600 transition-colors"
                            title="Delete"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      ) : viewMode === 'grid' ? (
        /* Grid View */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 px-2">
          {contactForms.map((form) => (
            <div key={form.id} className={`rounded-lg p-4 hover:bg-gray-100 transition-colors border-2 ${selectedForms.includes(String(form.id)) ? 'bg-blue-50 border-blue-500' : 'bg-gray-50 border-gray-200'}`}>
              <div className="flex items-center space-x-3 mb-3">
                <div className={`flex-shrink-0 ${getImageSize()}`}>
                  <div className={`${getImageSize()} rounded-full bg-gray-200 flex items-center justify-center`}>
                    <span className={`text-gray-500 ${getTextSize()} font-medium`}>
                      {form.name.charAt(0)}
                    </span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold text-gray-900 truncate">{form.name}</h3>
                  <p className="text-xs text-gray-600 truncate">{form.email}</p>
                </div>
                <input
                  type="checkbox"
                  checked={selectedForms.includes(String(form.id))}
                  onChange={() => handleSelectForm(String(form.id))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
              <div className="space-y-2 text-xs text-gray-600">
                <div className="flex items-center justify-between">
                  <span>Subject:</span>
                  <span className="truncate ml-2">{form.subject}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Status:</span>
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    form.status === 'Resolved' ? 'bg-green-100 text-green-800' :
                    form.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                    form.status === 'Closed' ? 'bg-gray-100 text-gray-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {form.status || 'New'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Read:</span>
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    form.isread ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {form.isread ? 'Read' : 'Unread'}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-center space-x-1 mt-3 pt-3 border-t border-gray-200">
                <button
                  onClick={() => handleAction('view', form)}
                  className="p-1.5 rounded-md transition-all duration-200 border border-transparent hover:border-solid text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-200"
                  title="View"
                >
                  <EyeIcon className="w-3 h-3" />
                </button>
                <button
                  onClick={() => handleAction('reply', form)}
                  className="p-1.5 rounded-md transition-all duration-200 border border-transparent hover:border-solid text-green-600 hover:text-green-800 hover:bg-green-50 hover:border-green-200"
                  title="Reply"
                >
                  <EnvelopeIcon className="w-3 h-3" />
                </button>
                <button
                  onClick={() => handleAction('mark-read', form)}
                  className={`p-1.5 rounded-md transition-all duration-200 border border-transparent hover:border-solid ${
                    form.isread ? 'text-green-600' : 'text-yellow-600'
                  } hover:text-yellow-800 hover:bg-yellow-50 hover:border-yellow-200`}
                  title={form.isread ? 'Mark as Unread' : 'Mark as Read'}
                >
                  <CheckIcon className="w-3 h-3" />
                </button>
                <button
                  onClick={() => handleAction('edit', form)}
                  className="p-1.5 rounded-md transition-all duration-200 border border-transparent hover:border-solid text-blue-600 hover:text-blue-800 hover:bg-blue-50 hover:border-blue-200"
                  title="Edit"
                >
                  <PencilIcon className="w-3 h-3" />
                </button>
                <button
                  onClick={() => handleAction('delete', form)}
                  className="p-1.5 rounded-md transition-all duration-200 border border-transparent hover:border-solid text-red-600 hover:text-red-800 hover:bg-red-50 hover:border-red-200"
                  title="Delete"
                >
                  <TrashIcon className="w-3 h-3" />
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        /* Card View */
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 px-2">
          {contactForms.map((form) => (
            <div key={form.id} className={`rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300 border-2 min-h-[320px] ${selectedForms.includes(String(form.id)) ? 'bg-blue-50 border-blue-500' : 'bg-white border-gray-100 hover:border-blue-200'}`}>
              <div className="flex h-full">
                {/* Contact Icon Section - Full Height */}
                <div className="flex-shrink-0 w-56 relative bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <div className="text-white text-6xl font-bold">
                    {form.name.charAt(0)}
                  </div>
                  {/* Checkbox Overlay */}
                  <div className="absolute top-4 left-4">
                    <input
                      type="checkbox"
                      checked={selectedForms.includes(String(form.id))}
                      onChange={() => handleSelectForm(String(form.id))}
                      className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded shadow-lg"
                    />
                  </div>
                  {/* Status Badge Overlay */}
                  <div className="absolute top-4 right-4">
                    <span className={`inline-flex px-3 py-1.5 text-xs font-bold rounded-full shadow-lg ${
                      form.isread ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                    }`}>
                      {form.isread ? 'READ' : 'UNREAD'}
                    </span>
                  </div>
                </div>

                {/* Content Section */}
                <div className="flex-1 p-8 flex flex-col justify-between min-w-0">
                  {/* Header */}
                  <div>
                    <div className="mb-6">
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">{form.name}</h3>
                      <p className="text-xl text-blue-600 font-semibold">{form.subject}</p>
                    </div>

                    {/* Message Preview */}
                    <div className="mb-6">
                      <p className="text-gray-700 text-sm leading-relaxed italic border-l-4 border-blue-200 pl-4">
                        "{form.message.length > 150 ? form.message.substring(0, 150) + '...' : form.message}"
                      </p>
                    </div>

                    {/* Contact Information */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 text-sm">
                      <div className="space-y-3">
                        <div className="flex items-center">
                          <span className="font-bold text-gray-800 w-16">Email:</span>
                          <span className="text-gray-600 ml-2">{form.email}</span>
                        </div>
                        {form.phone && (
                          <div className="flex items-center">
                            <span className="font-bold text-gray-800 w-16">Phone:</span>
                            <span className="text-gray-600 ml-2">{form.phone}</span>
                          </div>
                        )}
                      </div>
                      <div className="space-y-3">
                        <div className="flex items-center">
                          <span className="font-bold text-gray-800 w-16">Status:</span>
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            form.status === 'Resolved' ? 'bg-green-100 text-green-800' :
                            form.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                            form.status === 'Closed' ? 'bg-gray-100 text-gray-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {form.status || 'New'}
                          </span>
                        </div>
                        {form.createdAt && (
                          <div className="flex items-center">
                            <span className="font-bold text-gray-800 w-16">Date:</span>
                            <span className="text-gray-600 ml-2">{formatDate(form.createdAt)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                    <button
                      onClick={() => handleAction('view', form)}
                      className="p-3 rounded-xl transition-all duration-200 border-2 border-transparent hover:border-solid shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300"
                      title="View Details"
                    >
                      <EyeIcon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => handleAction('reply', form)}
                      className="p-3 rounded-xl transition-all duration-200 border-2 border-transparent hover:border-solid shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-green-600 hover:text-green-800 hover:bg-green-50 hover:border-green-300"
                      title="Reply via Email"
                    >
                      <EnvelopeIcon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => handleAction('mark-read', form)}
                      className={`p-3 rounded-xl transition-all duration-200 border-2 border-transparent hover:border-solid shadow-md hover:shadow-lg transform hover:-translate-y-0.5 ${
                        form.isread ? 'text-green-600 hover:text-green-800 hover:bg-green-50 hover:border-green-300' : 'text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50 hover:border-yellow-300'
                      }`}
                      title={form.isread ? 'Mark as Unread' : 'Mark as Read'}
                    >
                      <CheckIcon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => handleAction('edit', form)}
                      className="p-3 rounded-xl transition-all duration-200 border-2 border-transparent hover:border-solid shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-blue-600 hover:text-blue-800 hover:bg-blue-50 hover:border-blue-300"
                      title="Edit"
                    >
                      <PencilIcon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => handleAction('delete', form)}
                      className="p-3 rounded-xl transition-all duration-200 border-2 border-transparent hover:border-solid shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-red-600 hover:text-red-800 hover:bg-red-50 hover:border-red-300"
                      title="Delete"
                    >
                      <TrashIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create Modal */}
      <ContactFormModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreate}
        title="Create Contact Form"
      />

      {/* Edit Modal */}
      <ContactFormModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setEditingForm(null)
        }}
        onSubmit={(formData) => editingForm && handleUpdate(editingForm.id, formData)}
        title="Edit Contact Form"
        initialData={editingForm}
      />
    </div>
  )
}
