'use client'

import React, { useState, useEffect } from 'react'
import { usePageBuilderStore } from '@/lib/page-builder/store'
import { useDataBindingStore } from '@/lib/page-builder/data-binding-store'
import { useTemplateStore } from '@/lib/page-builder/template-store'
import { useNotifications } from './NotificationSystem'
import { 
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  ClockIcon,
  CogIcon
} from '@heroicons/react/24/outline'

interface TestResult {
  id: string
  name: string
  status: 'pending' | 'running' | 'passed' | 'failed' | 'warning'
  message?: string
  duration?: number
  details?: string[]
}

interface TestSuite {
  name: string
  tests: TestResult[]
  status: 'pending' | 'running' | 'completed'
}

export const PageBuilderTester: React.FC<{ isOpen: boolean; onClose: () => void }> = ({ 
  isOpen, 
  onClose 
}) => {
  const [testSuites, setTestSuites] = useState<TestSuite[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [currentTest, setCurrentTest] = useState<string | null>(null)
  
  const { components, currentPage } = usePageBuilderStore()
  const { dataSources, getPreviewData } = useDataBindingStore()
  const { templates } = useTemplateStore()
  const { success, error, warning } = useNotifications()

  // Initialize test suites
  useEffect(() => {
    if (isOpen) {
      initializeTests()
    }
  }, [isOpen, components, dataSources, templates])

  const initializeTests = () => {
    const suites: TestSuite[] = [
      {
        name: 'Component Validation',
        status: 'pending',
        tests: [
          { id: 'components-exist', name: 'Components exist on page', status: 'pending' },
          { id: 'components-valid', name: 'All components have valid configuration', status: 'pending' },
          { id: 'components-positioned', name: 'Components are properly positioned', status: 'pending' },
          { id: 'components-responsive', name: 'Components are responsive-ready', status: 'pending' }
        ]
      },
      {
        name: 'Data Binding Tests',
        status: 'pending',
        tests: [
          { id: 'data-sources', name: 'Data sources are connected', status: 'pending' },
          { id: 'data-bindings', name: 'Data bindings are valid', status: 'pending' },
          { id: 'data-preview', name: 'Preview data is available', status: 'pending' },
          { id: 'data-rendering', name: 'Data renders correctly', status: 'pending' }
        ]
      },
      {
        name: 'Performance Tests',
        status: 'pending',
        tests: [
          { id: 'page-size', name: 'Page size is optimized', status: 'pending' },
          { id: 'component-count', name: 'Component count is reasonable', status: 'pending' },
          { id: 'render-time', name: 'Render time is acceptable', status: 'pending' },
          { id: 'memory-usage', name: 'Memory usage is within limits', status: 'pending' }
        ]
      },
      {
        name: 'Accessibility Tests',
        status: 'pending',
        tests: [
          { id: 'alt-text', name: 'Images have alt text', status: 'pending' },
          { id: 'heading-structure', name: 'Proper heading hierarchy', status: 'pending' },
          { id: 'focus-management', name: 'Focus management is correct', status: 'pending' },
          { id: 'color-contrast', name: 'Color contrast is sufficient', status: 'pending' }
        ]
      },
      {
        name: 'SEO Tests',
        status: 'pending',
        tests: [
          { id: 'meta-tags', name: 'Meta tags are present', status: 'pending' },
          { id: 'page-title', name: 'Page title is set', status: 'pending' },
          { id: 'meta-description', name: 'Meta description is present', status: 'pending' },
          { id: 'structured-data', name: 'Structured data is valid', status: 'pending' }
        ]
      }
    ]
    
    setTestSuites(suites)
  }

  const runAllTests = async () => {
    setIsRunning(true)
    
    try {
      for (const suite of testSuites) {
        await runTestSuite(suite)
      }
      
      const totalTests = testSuites.reduce((acc, suite) => acc + suite.tests.length, 0)
      const passedTests = testSuites.reduce((acc, suite) => 
        acc + suite.tests.filter(test => test.status === 'passed').length, 0
      )
      const failedTests = testSuites.reduce((acc, suite) => 
        acc + suite.tests.filter(test => test.status === 'failed').length, 0
      )
      
      if (failedTests === 0) {
        success('All tests passed!', `${passedTests}/${totalTests} tests completed successfully`)
      } else {
        warning('Some tests failed', `${passedTests}/${totalTests} tests passed, ${failedTests} failed`)
      }
    } catch (err) {
      error('Test execution failed', 'An error occurred while running tests')
    } finally {
      setIsRunning(false)
      setCurrentTest(null)
    }
  }

  const runTestSuite = async (suite: TestSuite) => {
    // Update suite status
    setTestSuites(prev => prev.map(s => 
      s.name === suite.name ? { ...s, status: 'running' } : s
    ))

    for (const test of suite.tests) {
      setCurrentTest(test.id)
      await runIndividualTest(suite.name, test)
      
      // Small delay for visual feedback
      await new Promise(resolve => setTimeout(resolve, 200))
    }

    // Mark suite as completed
    setTestSuites(prev => prev.map(s => 
      s.name === suite.name ? { ...s, status: 'completed' } : s
    ))
  }

  const runIndividualTest = async (suiteName: string, test: TestResult) => {
    const startTime = Date.now()
    
    // Update test status to running
    setTestSuites(prev => prev.map(suite => 
      suite.name === suiteName 
        ? {
            ...suite,
            tests: suite.tests.map(t => 
              t.id === test.id ? { ...t, status: 'running' } : t
            )
          }
        : suite
    ))

    try {
      const result = await executeTest(test.id)
      const duration = Date.now() - startTime

      // Update test with result
      setTestSuites(prev => prev.map(suite => 
        suite.name === suiteName 
          ? {
              ...suite,
              tests: suite.tests.map(t => 
                t.id === test.id 
                  ? { 
                      ...t, 
                      status: result.status,
                      message: result.message,
                      details: result.details,
                      duration 
                    }
                  : t
              )
            }
          : suite
      ))
    } catch (error) {
      const duration = Date.now() - startTime
      
      setTestSuites(prev => prev.map(suite => 
        suite.name === suiteName 
          ? {
              ...suite,
              tests: suite.tests.map(t => 
                t.id === test.id 
                  ? { 
                      ...t, 
                      status: 'failed',
                      message: 'Test execution failed',
                      duration 
                    }
                  : t
              )
            }
          : suite
      ))
    }
  }

  const executeTest = async (testId: string): Promise<{ 
    status: 'passed' | 'failed' | 'warning'
    message?: string
    details?: string[]
  }> => {
    switch (testId) {
      case 'components-exist':
        return components.length > 0 
          ? { status: 'passed', message: `${components.length} components found` }
          : { status: 'failed', message: 'No components on page' }

      case 'components-valid':
        const invalidComponents = components.filter(c => !c.type || !c.position)
        return invalidComponents.length === 0
          ? { status: 'passed', message: 'All components are valid' }
          : { status: 'failed', message: `${invalidComponents.length} invalid components` }

      case 'components-positioned':
        const unpositioned = components.filter(c => 
          c.position.x < 0 || c.position.y < 0 || c.position.width <= 0 || c.position.height <= 0
        )
        return unpositioned.length === 0
          ? { status: 'passed', message: 'All components properly positioned' }
          : { status: 'warning', message: `${unpositioned.length} components may have positioning issues` }

      case 'data-sources':
        const connectedSources = dataSources.filter(ds => ds.isConnected)
        return connectedSources.length > 0
          ? { status: 'passed', message: `${connectedSources.length} data sources connected` }
          : { status: 'warning', message: 'No data sources connected' }

      case 'page-size':
        const estimatedSize = components.length * 50 // Rough estimate in KB
        return estimatedSize < 500
          ? { status: 'passed', message: `Estimated size: ${estimatedSize}KB` }
          : { status: 'warning', message: `Page may be large: ${estimatedSize}KB` }

      case 'component-count':
        return components.length < 50
          ? { status: 'passed', message: `${components.length} components (good)` }
          : { status: 'warning', message: `${components.length} components (consider optimization)` }

      case 'alt-text':
        const images = components.filter(c => c.type === 'image')
        const imagesWithoutAlt = images.filter(img => !img.content.alt)
        return imagesWithoutAlt.length === 0
          ? { status: 'passed', message: 'All images have alt text' }
          : { status: 'failed', message: `${imagesWithoutAlt.length} images missing alt text` }

      case 'meta-tags':
        return currentPage?.metaTitle && currentPage?.metaDescription
          ? { status: 'passed', message: 'Meta tags are present' }
          : { status: 'failed', message: 'Missing meta tags' }

      default:
        // Simulate test execution
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500))
        return Math.random() > 0.3 
          ? { status: 'passed', message: 'Test completed successfully' }
          : { status: 'warning', message: 'Test completed with warnings' }
    }
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'passed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
      case 'running':
        return <CogIcon className="h-5 w-5 text-blue-500 animate-spin" />
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity" onClick={onClose} />
        
        <div className="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">Page Builder Testing Suite</h3>
              <div className="flex items-center space-x-3">
                <button
                  onClick={runAllTests}
                  disabled={isRunning}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  <PlayIcon className="h-4 w-4" />
                  <span>{isRunning ? 'Running...' : 'Run All Tests'}</span>
                </button>
                <button
                  onClick={onClose}
                  className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
                >
                  ×
                </button>
              </div>
            </div>
          </div>
          
          {/* Content */}
          <div className="p-6 max-h-96 overflow-y-auto">
            <div className="space-y-6">
              {testSuites.map(suite => (
                <div key={suite.name} className="border border-gray-200 rounded-lg">
                  <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-900">{suite.name}</h4>
                  </div>
                  
                  <div className="p-4">
                    <div className="space-y-3">
                      {suite.tests.map(test => (
                        <div key={test.id} className="flex items-center justify-between p-3 border border-gray-100 rounded-lg">
                          <div className="flex items-center space-x-3">
                            {getStatusIcon(test.status)}
                            <div>
                              <div className="text-sm font-medium text-gray-900">{test.name}</div>
                              {test.message && (
                                <div className="text-xs text-gray-600 mt-1">{test.message}</div>
                              )}
                            </div>
                          </div>
                          
                          {test.duration && (
                            <div className="text-xs text-gray-500">
                              {test.duration}ms
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
