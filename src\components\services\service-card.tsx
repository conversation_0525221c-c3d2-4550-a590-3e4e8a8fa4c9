'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  CheckIcon,
  ClockIcon,
  UserGroupIcon,
  ArrowRightIcon,
  CodeBracketIcon,
  DevicePhoneMobileIcon,
  CloudIcon,
  CogIcon,
  ShieldCheckIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline'
import { Service } from '@/hooks/useServices'

interface ServiceCardProps {
  service: Service
  index?: number
  variant?: 'default' | 'compact' | 'detailed'
  showCTA?: boolean
}

// Icon mapping for services
const iconMap: { [key: string]: any } = {
  'fas fa-code': CodeBracketIcon,
  'fas fa-mobile-alt': DevicePhoneMobileIcon,
  'fas fa-cloud': CloudIcon,
  'fas fa-cog': CogIcon,
  'fas fa-shield-alt': ShieldCheckIcon,
  'fas fa-chart-bar': ChartBarIcon,
  'fab fa-apple': DevicePhoneMobileIcon,
  'fab fa-android': DevicePhoneMobileIcon,
  'fas fa-shopping-cart': CodeBracketIcon,
  'fas fa-lightbulb': ChartBarIcon,
}

const getServiceIcon = (iconClass?: string) => {
  if (!iconClass) return CodeBracketIcon
  return iconMap[iconClass] || CodeBracketIcon
}

const formatPrice = (price: number, discountRate?: number) => {
  const formattedPrice = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
  }).format(price)
  
  if (discountRate && discountRate > 0) {
    const discountedPrice = price * (1 - discountRate / 100)
    const formattedDiscountedPrice = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(discountedPrice)
    
    return {
      original: formattedPrice,
      discounted: formattedDiscountedPrice,
      hasDiscount: true,
    }
  }
  
  return {
    original: formattedPrice,
    hasDiscount: false,
  }
}

export function ServiceCard({ service, index = 0, variant = 'default', showCTA = true }: ServiceCardProps) {
  const IconComponent = getServiceIcon(service.iconClass)
  const pricing = formatPrice(service.price, service.discountRate)

  if (variant === 'compact') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: index * 0.1 }}
        viewport={{ once: true }}
        className="group relative bg-white p-6 rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300"
      >
        <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg mb-4 group-hover:bg-blue-200 transition-colors">
          <IconComponent className="w-5 h-5 text-blue-600" />
        </div>

        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {service.name}
        </h3>

        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {service.description}
        </p>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500">{service.category.name}</span>
          <Link
            href={`/services/${service.id}`}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
          >
            Learn More →
          </Link>
        </div>
      </motion.div>
    )
  }

  if (variant === 'detailed') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: index * 0.1 }}
        viewport={{ once: true }}
        className="group relative bg-white p-8 rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-xl transition-all duration-300"
      >
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
            <IconComponent className="w-6 h-6 text-blue-600" />
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500 mb-1">Starting at</div>
            <div className="text-lg font-bold text-blue-600">
              {pricing.hasDiscount ? (
                <div className="flex flex-col">
                  <span className="line-through text-gray-400 text-sm">{pricing.original}</span>
                  <span>{pricing.discounted}</span>
                </div>
              ) : (
                pricing.original
              )}
            </div>
          </div>
        </div>

        <h3 className="text-2xl font-semibold text-gray-900 mb-3">
          {service.name}
        </h3>

        <p className="text-gray-600 mb-6 leading-relaxed">
          {service.description}
        </p>

        <div className="space-y-4 mb-8">
          <div className="flex items-center text-sm text-gray-500">
            <UserGroupIcon className="w-4 h-4 mr-2" />
            Category: {service.category.name}
          </div>
          {service.manager && (
            <div className="flex items-center text-sm text-gray-500">
              <UserGroupIcon className="w-4 h-4 mr-2" />
              Manager: {service.manager}
            </div>
          )}
          <div className="flex items-center text-sm text-gray-500">
            <ChartBarIcon className="w-4 h-4 mr-2" />
            {service._count.projects} Projects Completed
          </div>
        </div>

        {service.serviceOptions.length > 0 && (
          <div className="mb-8">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Key Features:</h4>
            <div className="grid grid-cols-1 gap-2">
              {service.serviceOptions.slice(0, 3).map((option) => (
                <div key={option.id} className="flex items-center text-sm text-gray-600">
                  <CheckIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                  {option.name}
                </div>
              ))}
            </div>
            {service.serviceOptions.length > 3 && (
              <div className="mt-2 text-sm text-gray-500">
                +{service.serviceOptions.length - 3} more options
              </div>
            )}
          </div>
        )}

        {showCTA && (
          <div className="flex items-center justify-between">
            <Link
              href={`/services/${service.id}`}
              className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium group/link"
            >
              Learn More
              <ArrowRightIcon className="ml-2 h-4 w-4 transition-transform group-hover/link:translate-x-1" />
            </Link>
            <Link
              href="/contact"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              Get Quote
            </Link>
          </div>
        )}

        {/* Hover effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
      </motion.div>
    )
  }

  // Default variant
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="group relative bg-white p-8 rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300"
    >
      <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-6 group-hover:bg-blue-200 transition-colors">
        <IconComponent className="w-6 h-6 text-blue-600" />
      </div>

      <h3 className="text-xl font-semibold text-gray-900 mb-3">
        {service.name}
      </h3>

      <p className="text-gray-600 mb-6">
        {service.description}
      </p>

      {service.serviceOptions.length > 0 && (
        <ul className="space-y-2 mb-6">
          {service.serviceOptions.slice(0, 4).map((option) => (
            <li key={option.id} className="flex items-center text-sm text-gray-500">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3" />
              {option.name}
            </li>
          ))}
        </ul>
      )}

      {showCTA && (
        <Link
          href={`/services/${service.id}`}
          className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium group/link"
        >
          Learn More
          <ArrowRightIcon className="ml-2 h-4 w-4 transition-transform group-hover/link:translate-x-1" />
        </Link>
      )}
    </motion.div>
  )
}
