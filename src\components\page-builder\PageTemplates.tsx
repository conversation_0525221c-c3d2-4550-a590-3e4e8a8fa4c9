'use client'

import React, { useState, useEffect } from 'react'
import { usePageBuilderStore } from '@/lib/page-builder/store'
import { 
  TemplateIcon,
  EyeIcon,
  DocumentDuplicateIcon,
  MagnifyingGlassIcon,
  TagIcon,
  StarIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

interface PageTemplatesProps {
  onClose: () => void
}

interface PageTemplate {
  id: string
  name: string
  description: string
  category: string
  thumbnail: string
  isPopular: boolean
  components: any[]
  styles: any
  createdAt: Date
  usageCount: number
}

const PageTemplates: React.FC<PageTemplatesProps> = ({ onClose }) => {
  const [templates, setTemplates] = useState<PageTemplate[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [previewTemplate, setPreviewTemplate] = useState<PageTemplate | null>(null)

  const { setCurrentPage, setComponents } = usePageBuilderStore()

  // Mock templates data
  useEffect(() => {
    const mockTemplates: PageTemplate[] = [
      {
        id: 'landing-modern',
        name: 'Modern Landing Page',
        description: 'A clean, modern landing page with hero section, features, and call-to-action',
        category: 'Landing Pages',
        thumbnail: '/templates/landing-modern.jpg',
        isPopular: true,
        usageCount: 245,
        createdAt: new Date('2024-01-15'),
        components: [
          {
            id: 'hero-1',
            type: 'hero',
            content: {
              title: 'Build Amazing Websites',
              subtitle: 'Create stunning web experiences with our powerful page builder',
              buttonText: 'Get Started',
              backgroundType: 'gradient'
            },
            styles: {
              minHeight: '600px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: '#ffffff',
              textAlign: 'center',
              padding: '80px 20px'
            },
            position: { x: 0, y: 0, width: 1200, height: 600 }
          },
          {
            id: 'features-1',
            type: 'container',
            content: { tag: 'section' },
            styles: {
              backgroundColor: '#f8f9fa',
              padding: '80px 20px',
              textAlign: 'center'
            },
            position: { x: 0, y: 600, width: 1200, height: 400 }
          }
        ],
        styles: {
          backgroundColor: '#ffffff',
          fontFamily: 'Inter, sans-serif'
        }
      },
      {
        id: 'portfolio-creative',
        name: 'Creative Portfolio',
        description: 'Showcase your work with this creative portfolio template',
        category: 'Portfolio',
        thumbnail: '/templates/portfolio-creative.jpg',
        isPopular: true,
        usageCount: 189,
        createdAt: new Date('2024-01-20'),
        components: [
          {
            id: 'portfolio-hero',
            type: 'hero',
            content: {
              title: 'Creative Portfolio',
              subtitle: 'Showcasing amazing projects and designs',
              backgroundType: 'image'
            },
            styles: {
              minHeight: '500px',
              backgroundImage: 'url(/hero-portfolio.jpg)',
              color: '#ffffff',
              textAlign: 'center'
            },
            position: { x: 0, y: 0, width: 1200, height: 500 }
          }
        ],
        styles: {
          backgroundColor: '#ffffff',
          fontFamily: 'Poppins, sans-serif'
        }
      },
      {
        id: 'business-corporate',
        name: 'Corporate Business',
        description: 'Professional business template with services and team sections',
        category: 'Business',
        thumbnail: '/templates/business-corporate.jpg',
        isPopular: false,
        usageCount: 156,
        createdAt: new Date('2024-01-25'),
        components: [
          {
            id: 'business-hero',
            type: 'hero',
            content: {
              title: 'Your Business Success',
              subtitle: 'Professional solutions for modern businesses',
              buttonText: 'Learn More'
            },
            styles: {
              minHeight: '550px',
              backgroundColor: '#1f2937',
              color: '#ffffff',
              textAlign: 'center'
            },
            position: { x: 0, y: 0, width: 1200, height: 550 }
          }
        ],
        styles: {
          backgroundColor: '#ffffff',
          fontFamily: 'Roboto, sans-serif'
        }
      },
      {
        id: 'blog-minimal',
        name: 'Minimal Blog',
        description: 'Clean and minimal blog template focused on content',
        category: 'Blog',
        thumbnail: '/templates/blog-minimal.jpg',
        isPopular: false,
        usageCount: 98,
        createdAt: new Date('2024-02-01'),
        components: [
          {
            id: 'blog-header',
            type: 'container',
            content: { tag: 'header' },
            styles: {
              backgroundColor: '#ffffff',
              padding: '40px 20px',
              borderBottom: '1px solid #e5e7eb'
            },
            position: { x: 0, y: 0, width: 1200, height: 120 }
          }
        ],
        styles: {
          backgroundColor: '#ffffff',
          fontFamily: 'Georgia, serif'
        }
      },
      {
        id: 'ecommerce-product',
        name: 'Product Showcase',
        description: 'E-commerce product page with gallery and details',
        category: 'E-commerce',
        thumbnail: '/templates/ecommerce-product.jpg',
        isPopular: true,
        usageCount: 203,
        createdAt: new Date('2024-02-05'),
        components: [
          {
            id: 'product-hero',
            type: 'container',
            content: { tag: 'section' },
            styles: {
              backgroundColor: '#ffffff',
              padding: '60px 20px'
            },
            position: { x: 0, y: 0, width: 1200, height: 600 }
          }
        ],
        styles: {
          backgroundColor: '#ffffff',
          fontFamily: 'Inter, sans-serif'
        }
      },
      {
        id: 'agency-creative',
        name: 'Creative Agency',
        description: 'Bold and creative template for design agencies',
        category: 'Agency',
        thumbnail: '/templates/agency-creative.jpg',
        isPopular: false,
        usageCount: 134,
        createdAt: new Date('2024-02-10'),
        components: [
          {
            id: 'agency-hero',
            type: 'hero',
            content: {
              title: 'Creative Agency',
              subtitle: 'We create amazing digital experiences',
              backgroundType: 'video'
            },
            styles: {
              minHeight: '700px',
              backgroundColor: '#000000',
              color: '#ffffff'
            },
            position: { x: 0, y: 0, width: 1200, height: 700 }
          }
        ],
        styles: {
          backgroundColor: '#000000',
          fontFamily: 'Montserrat, sans-serif'
        }
      }
    ]

    setTemplates(mockTemplates)
    setIsLoading(false)
  }, [])

  const categories = ['all', ...Array.from(new Set(templates.map(t => t.category)))]

  const filteredTemplates = templates.filter(template => {
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  const handleUseTemplate = async (template: PageTemplate) => {
    try {
      // Create a new page with the template
      const newPage = {
        id: `template-${Date.now()}`,
        title: `${template.name} - Copy`,
        slug: `${template.name.toLowerCase().replace(/\s+/g, '-')}-copy-${Date.now()}`,
        description: `Page created from ${template.name} template`,
        metaTitle: template.name,
        metaDescription: template.description,
        isPublished: false,
        isHomePage: false,
        layout: template.components,
        styles: template.styles,
        seoSettings: {},
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Set the current page and components
      setCurrentPage(newPage)
      setComponents(template.components)
      
      // Close the templates panel
      onClose()
      
      // Show success message
      alert(`Template "${template.name}" has been applied successfully!`)
    } catch (error) {
      console.error('Error applying template:', error)
      alert('Failed to apply template. Please try again.')
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading templates...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-96">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Page Templates</h3>
            <p className="text-sm text-gray-600">Choose from professionally designed templates</p>
          </div>
        </div>
        
        {/* Search and Filter */}
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      {/* Templates Grid */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTemplates.map(template => (
            <div
              key={template.id}
              className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
            >
              {/* Template Thumbnail */}
              <div className="aspect-video bg-gray-100 relative">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 opacity-20"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <TemplateIcon className="h-12 w-12 text-gray-400" />
                </div>
                {template.isPopular && (
                  <div className="absolute top-2 right-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      <StarIcon className="h-3 w-3 mr-1" />
                      Popular
                    </span>
                  </div>
                )}
              </div>
              
              {/* Template Info */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-900 truncate">{template.name}</h4>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <TagIcon className="h-3 w-3 mr-1" />
                    {template.category}
                  </span>
                </div>
                
                <p className="text-xs text-gray-600 mb-3 line-clamp-2">{template.description}</p>
                
                <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                  <span className="flex items-center">
                    <ClockIcon className="h-3 w-3 mr-1" />
                    {template.createdAt.toLocaleDateString()}
                  </span>
                  <span>{template.usageCount} uses</span>
                </div>
                
                {/* Actions */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setPreviewTemplate(template)}
                    className="flex-1 flex items-center justify-center px-3 py-2 text-xs font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    <EyeIcon className="h-3 w-3 mr-1" />
                    Preview
                  </button>
                  <button
                    onClick={() => handleUseTemplate(template)}
                    className="flex-1 flex items-center justify-center px-3 py-2 text-xs font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                  >
                    <DocumentDuplicateIcon className="h-3 w-3 mr-1" />
                    Use Template
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {filteredTemplates.length === 0 && (
          <div className="text-center py-8">
            <TemplateIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No templates found</p>
            <p className="text-sm text-gray-500">Try adjusting your search or filter</p>
          </div>
        )}
      </div>
      
      {/* Preview Modal */}
      {previewTemplate && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-900 bg-opacity-50 transition-opacity" onClick={() => setPreviewTemplate(null)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">{previewTemplate.name}</h3>
                  <button
                    onClick={() => setPreviewTemplate(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>
                
                <div className="aspect-video bg-gray-100 rounded-lg mb-4">
                  <div className="h-full flex items-center justify-center">
                    <p className="text-gray-500">Template Preview</p>
                  </div>
                </div>
                
                <p className="text-gray-600 mb-4">{previewTemplate.description}</p>
                
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setPreviewTemplate(null)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    Close
                  </button>
                  <button
                    onClick={() => {
                      handleUseTemplate(previewTemplate)
                      setPreviewTemplate(null)
                    }}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                  >
                    Use This Template
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PageTemplates
