'use client'

import React, { useState, useEffect } from 'react'
import { usePageBuilderStore } from '@/lib/page-builder/store'
import { useTemplateStore, TemplateCategory, TemplateFilter } from '@/lib/page-builder/template-store'
import {
  RectangleStackIcon,
  EyeIcon,
  DocumentDuplicateIcon,
  MagnifyingGlassIcon,
  TagIcon,
  StarIcon,
  ClockIcon,
  HeartIcon,
  FunnelIcon,
  SparklesIcon,
  AcademicCapIcon,
  FireIcon
} from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'

interface PageTemplatesProps {
  onClose: () => void
}

interface PageTemplate {
  id: string
  name: string
  description: string
  category: string
  thumbnail: string
  isPopular: boolean
  components: any[]
  styles: any
  createdAt: Date
  usageCount: number
}

const PageTemplates: React.FC<PageTemplatesProps> = ({ onClose }) => {
  const [selectedCategory, setSelectedCategory] = useState<TemplateCategory | 'all'>('all')
  const [selectedDifficulty, setSelectedDifficulty] = useState<'beginner' | 'intermediate' | 'advanced' | 'all'>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false)
  const [showPopularOnly, setShowPopularOnly] = useState(false)
  const [previewTemplate, setPreviewTemplate] = useState<any>(null)
  const [activeTab, setActiveTab] = useState<'all' | 'popular' | 'recent' | 'favorites'>('all')

  const { setCurrentPage, setComponents } = usePageBuilderStore()
  const {
    templates,
    isLoading,
    error,
    loadTemplates,
    searchTemplates,
    getPopularTemplates,
    getRecentTemplates,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    addToRecent,
    incrementUsage
  } = useTemplateStore()

  // Load templates on component mount
  useEffect(() => {
    loadTemplates()
  }, [loadTemplates])

  // Get filtered templates based on current tab and filters
  const getFilteredTemplates = () => {
    const filter: TemplateFilter = {
      category: selectedCategory,
      difficulty: selectedDifficulty,
      searchTerm: searchTerm || undefined,
      isPopular: showPopularOnly || undefined
    }

    switch (activeTab) {
      case 'popular':
        return getPopularTemplates()
      case 'recent':
        return getRecentTemplates()
      case 'favorites':
        const favoriteTemplates = templates.filter(t => isFavorite(t.id))
        return searchTemplates({ ...filter }).filter(t => favoriteTemplates.includes(t))
      default:
        return searchTemplates(filter)
    }
  }

  const filteredTemplates = getFilteredTemplates()

  // Categories for filtering
  const categories: (TemplateCategory | 'all')[] = [
    'all', 'landing', 'business', 'portfolio', 'blog', 'ecommerce',
    'agency', 'personal', 'nonprofit', 'education'
  ]

  // Handle template selection and usage
  const handleUseTemplate = async (template: any) => {
    try {
      // Track usage
      incrementUsage(template.id)
      addToRecent(template.id)

      // Create a new page with the template
      const newPage = {
        id: `template-${Date.now()}`,
        title: `${template.name} - Copy`,
        slug: `${template.name.toLowerCase().replace(/\s+/g, '-')}-copy-${Date.now()}`,
        description: template.description,
        metaTitle: template.seoSettings.title,
        metaDescription: template.seoSettings.description,
        isPublished: false,
        isHomePage: false,
        layout: template.components,
        styles: template.styles,
        seoSettings: template.seoSettings,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Set the current page and components
      setCurrentPage(newPage)
      setComponents(template.components)

      // Close the templates panel
      onClose()

      // Show success message
      alert(`Template "${template.name}" has been applied successfully!`)
    } catch (error) {
      console.error('Error applying template:', error)
      alert('Failed to apply template. Please try again.')
    }
  }

  const handleToggleFavorite = (templateId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    if (isFavorite(templateId)) {
      removeFromFavorites(templateId)
    } else {
      addToFavorites(templateId)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading templates...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <p className="text-red-600 font-medium">Error loading templates</p>
          <p className="text-gray-600 text-sm mt-2">{error}</p>
          <button
            onClick={() => loadTemplates()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-xl font-bold text-gray-900">Page Templates</h3>
            <p className="text-sm text-gray-600">Choose from professionally designed templates to get started quickly</p>
          </div>
          <div className="text-sm text-gray-500">
            {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''}
          </div>
        </div>

        {/* Tabs */}
        <div className="flex items-center space-x-1 mb-4">
          {[
            { id: 'all', label: 'All Templates', icon: RectangleStackIcon },
            { id: 'popular', label: 'Popular', icon: FireIcon },
            { id: 'recent', label: 'Recent', icon: ClockIcon },
            { id: 'favorites', label: 'Favorites', icon: HeartIcon }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Enhanced Search and Filters */}
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="relative">
              <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value as any)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Categories</option>
                {categories.filter(cat => cat !== 'all').map(category => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            <div className="relative">
              <AcademicCapIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <select
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value as any)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Levels</option>
                <option value="beginner">🟢 Beginner</option>
                <option value="intermediate">🟡 Intermediate</option>
                <option value="advanced">🔴 Advanced</option>
              </select>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showPopularOnly}
                onChange={(e) => setShowPopularOnly(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">Popular only</span>
            </label>
          </div>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map(template => (
            <div
              key={template.id}
              className="group bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-lg hover:border-blue-300 transition-all duration-200"
            >
              {/* Template Thumbnail */}
              <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-600/10"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <RectangleStackIcon className="h-16 w-16 text-gray-400 group-hover:text-blue-500 transition-colors" />
                </div>

                {/* Badges */}
                <div className="absolute top-3 left-3 flex flex-col space-y-2">
                  {template.isPopular && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      <FireIcon className="h-3 w-3 mr-1" />
                      Popular
                    </span>
                  )}
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    template.difficulty === 'beginner' ? 'bg-green-100 text-green-800' :
                    template.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {template.difficulty === 'beginner' ? '🟢' :
                     template.difficulty === 'intermediate' ? '🟡' : '🔴'} {template.difficulty}
                  </span>
                </div>

                {/* Favorite Button */}
                <div className="absolute top-3 right-3">
                  <button
                    onClick={(e) => handleToggleFavorite(template.id, e)}
                    className="p-2 rounded-full bg-white/80 hover:bg-white transition-colors"
                  >
                    {isFavorite(template.id) ? (
                      <HeartSolidIcon className="h-4 w-4 text-red-500" />
                    ) : (
                      <HeartIcon className="h-4 w-4 text-gray-600" />
                    )}
                  </button>
                </div>

                {/* Hover Overlay */}
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <button
                    onClick={() => setPreviewTemplate(template)}
                    className="px-4 py-2 bg-white text-gray-900 rounded-lg font-medium hover:bg-gray-100 transition-colors"
                  >
                    <EyeIcon className="h-4 w-4 inline mr-2" />
                    Quick Preview
                  </button>
                </div>
              </div>

              {/* Template Info */}
              <div className="p-5">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {template.name}
                    </h4>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 mt-1">
                      <TagIcon className="h-3 w-3 mr-1" />
                      {template.category}
                    </span>
                  </div>
                </div>

                <p className="text-sm text-gray-600 mb-4 line-clamp-2">{template.description}</p>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {template.tags.slice(0, 3).map(tag => (
                    <span key={tag} className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-md">
                      {tag}
                    </span>
                  ))}
                  {template.tags.length > 3 && (
                    <span className="px-2 py-1 bg-gray-50 text-gray-500 text-xs rounded-md">
                      +{template.tags.length - 3} more
                    </span>
                  )}
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                  <span className="flex items-center">
                    <ClockIcon className="h-3 w-3 mr-1" />
                    {template.createdAt.toLocaleDateString()}
                  </span>
                  <span className="flex items-center">
                    <SparklesIcon className="h-3 w-3 mr-1" />
                    {template.usageCount} uses
                  </span>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setPreviewTemplate(template)}
                    className="flex-1 flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    <EyeIcon className="h-4 w-4 mr-2" />
                    Preview
                  </button>
                  <button
                    onClick={() => handleUseTemplate(template)}
                    className="flex-1 flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
                    Use Template
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredTemplates.length === 0 && (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">🎨</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedCategory !== 'all' || selectedDifficulty !== 'all' || showPopularOnly
                ? 'Try adjusting your search criteria or filters'
                : 'Templates are being loaded...'}
            </p>
            {(searchTerm || selectedCategory !== 'all' || selectedDifficulty !== 'all' || showPopularOnly) && (
              <button
                onClick={() => {
                  setSearchTerm('')
                  setSelectedCategory('all')
                  setSelectedDifficulty('all')
                  setShowPopularOnly(false)
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Clear Filters
              </button>
            )}
          </div>
        )}
      </div>
      
      {/* Enhanced Preview Modal */}
      {previewTemplate && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity" onClick={() => setPreviewTemplate(null)} />

            <div className="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
              <div className="bg-white">
                {/* Header */}
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{previewTemplate.name}</h3>
                        <div className="flex items-center space-x-3 mt-1">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            previewTemplate.difficulty === 'beginner' ? 'bg-green-100 text-green-800' :
                            previewTemplate.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {previewTemplate.difficulty}
                          </span>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {previewTemplate.category}
                          </span>
                          {previewTemplate.isPopular && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              <FireIcon className="h-3 w-3 mr-1" />
                              Popular
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => handleToggleFavorite(previewTemplate.id, e)}
                        className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
                      >
                        {isFavorite(previewTemplate.id) ? (
                          <HeartSolidIcon className="h-5 w-5 text-red-500" />
                        ) : (
                          <HeartIcon className="h-5 w-5 text-gray-600" />
                        )}
                      </button>
                      <button
                        onClick={() => setPreviewTemplate(null)}
                        className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
                      >
                        <span className="sr-only">Close</span>
                        <svg className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Preview */}
                    <div className="lg:col-span-2">
                      <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl mb-4 relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-600/10"></div>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-center">
                            <RectangleStackIcon className="h-20 w-20 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-500 font-medium">Template Preview</p>
                            <p className="text-sm text-gray-400">Interactive preview coming soon</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Details */}
                    <div className="space-y-6">
                      <div>
                        <h4 className="text-sm font-semibold text-gray-900 mb-2">Description</h4>
                        <p className="text-sm text-gray-600">{previewTemplate.description}</p>
                      </div>

                      <div>
                        <h4 className="text-sm font-semibold text-gray-900 mb-2">Tags</h4>
                        <div className="flex flex-wrap gap-2">
                          {previewTemplate.tags.map(tag => (
                            <span key={tag} className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-md">
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-semibold text-gray-900 mb-2">Statistics</h4>
                        <div className="space-y-2 text-sm text-gray-600">
                          <div className="flex justify-between">
                            <span>Usage Count:</span>
                            <span className="font-medium">{previewTemplate.usageCount}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Created:</span>
                            <span className="font-medium">{previewTemplate.createdAt.toLocaleDateString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Components:</span>
                            <span className="font-medium">{previewTemplate.components.length}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-semibold text-gray-900 mb-2">SEO Settings</h4>
                        <div className="space-y-2 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Title:</span>
                            <p className="text-xs mt-1">{previewTemplate.seoSettings.title}</p>
                          </div>
                          <div>
                            <span className="font-medium">Description:</span>
                            <p className="text-xs mt-1">{previewTemplate.seoSettings.description}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Footer */}
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                  <button
                    onClick={() => setPreviewTemplate(null)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Close
                  </button>
                  <button
                    onClick={() => {
                      handleUseTemplate(previewTemplate)
                      setPreviewTemplate(null)
                    }}
                    className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <DocumentDuplicateIcon className="h-4 w-4 inline mr-2" />
                    Use This Template
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PageTemplates
