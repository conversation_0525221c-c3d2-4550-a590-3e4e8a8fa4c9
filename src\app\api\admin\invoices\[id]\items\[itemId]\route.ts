import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { z } from 'zod'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string; itemId: string }>
}

const updateInvoiceItemSchema = z.object({
  description: z.string().min(1).optional(),
  quantity: z.coerce.number().int().positive().optional(),
  unitPrice: z.coerce.number().positive().optional(),
  totalPrice: z.coerce.number().positive().optional(),
})

// PUT /api/admin/invoices/[id]/items/[itemId] - Update invoice item
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id, itemId } = await params
  const validate = validateRequest(updateInvoiceItemSchema)
  const data = await validate(request)

  const item = await prisma.invoicesItem.update({
    where: { id: itemId },
    data
  })

  // Recalculate invoice totals
  const invoice = await prisma.invoices.findUnique({
    where: { id },
    include: { items: true }
  })

  if (invoice) {
    const subtotal = invoice.items.reduce((sum, item) => sum + Number(item.totalPrice), 0)
    const totalAmount = subtotal + Number(invoice.taxamount)

    await prisma.invoices.update({
      where: { id },
      data: {
        subtotal,
        totalAmount
      }
    })
  }

  return successResponse(item, 'Invoice item updated successfully')
})

// DELETE /api/admin/invoices/[id]/items/[itemId] - Delete invoice item
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id, itemId } = await params

  await prisma.invoicesItem.delete({
    where: { id: itemId }
  })

  // Recalculate invoice totals
  const invoice = await prisma.invoices.findUnique({
    where: { id },
    include: { items: true }
  })

  if (invoice) {
    const subtotal = invoice.items.reduce((sum, item) => sum + Number(item.totalPrice), 0)
    const totalAmount = subtotal + Number(invoice.taxamount)

    await prisma.invoices.update({
      where: { id },
      data: {
        subtotal,
        totalAmount
      }
    })
  }

  return successResponse(null, 'Invoice item deleted successfully')
})
