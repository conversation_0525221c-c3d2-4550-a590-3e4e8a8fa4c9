'use client'

import React from 'react'
import { StarIcon } from '@heroicons/react/24/solid'
import { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline'

interface CellRendererProps {
  value: any
  item: any
  renderType?: string
  renderProps?: any
}

export function CellRenderer({ value, item, renderType = 'text', renderProps = {} }: CellRendererProps) {
  // Handle null/undefined values
  if (value === null || value === undefined) {
    return <span className="text-gray-400">—</span>
  }

  switch (renderType) {
    case 'email':
      return (
        <a href={`mailto:${value}`} className="text-blue-600 hover:text-blue-800">
          {value}
        </a>
      )

    case 'date':
      if (!value) return <span className="text-gray-400">—</span>
      try {
        const date = value instanceof Date ? value : new Date(value)
        return <span>{date.toLocaleDateString()}</span>
      } catch (error) {
        return <span className="text-gray-400">Invalid date</span>
      }

    case 'currency':
      return (
        <span>
          {new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
          }).format(value)}
        </span>
      )

    case 'boolean':
      return (
        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
          value 
            ? 'bg-green-100 text-green-800' 
            : 'bg-gray-100 text-gray-800'
        }`}>
          {value ? 'Yes' : 'No'}
        </span>
      )

    case 'status':
      const statusColors = renderProps?.statusColors || {
        active: 'bg-green-100 text-green-800',
        inactive: 'bg-red-100 text-red-800',
        pending: 'bg-yellow-100 text-yellow-800',
        completed: 'bg-blue-100 text-blue-800',
        cancelled: 'bg-gray-100 text-gray-800',
        true: 'bg-green-100 text-green-800',
        false: 'bg-red-100 text-red-800',
        ...renderProps?.customColors
      }

      let displayValue = value
      let colorKey = String(value).toLowerCase().replace('_', '').replace(' ', '')

      // Handle boolean values
      if (typeof value === 'boolean') {
        displayValue = value ? (renderProps?.trueLabel || 'Active') : (renderProps?.falseLabel || 'Inactive')
        colorKey = String(value)
      }

      const colorClass = statusColors[colorKey] || 'bg-gray-100 text-gray-800'

      return (
        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${colorClass}`}>
          {typeof displayValue === 'string' ? displayValue.replace('_', ' ') : displayValue}
        </span>
      )

    case 'rating':
      const rating = Number(value) || 0
      return (
        <div className="flex items-center space-x-1">
          {[...Array(5)].map((_, i) => (
            i < rating ? (
              <StarIcon key={i} className="h-4 w-4 text-yellow-400" />
            ) : (
              <StarOutlineIcon key={i} className="h-4 w-4 text-gray-300" />
            )
          ))}
          <span className="text-sm text-gray-600 ml-1">({rating})</span>
        </div>
      )

    case 'progress':
      const progress = Number(value) || 0
      return (
        <div className="flex items-center space-x-2">
          <div className="flex-1 bg-gray-200 rounded-full h-2 max-w-[100px]">
            <div 
              className="bg-blue-600 h-2 rounded-full" 
              style={{ width: `${Math.min(progress, 100)}%` }}
            />
          </div>
          <span className="text-sm text-gray-600">{progress}%</span>
        </div>
      )

    case 'image':
      if (!value) return <span className="text-gray-400">—</span>
      return (
        <img
          src={value}
          alt={renderProps.alt || 'Image'}
          className={`object-cover ${renderProps.className || 'h-8 w-8 rounded-full'}`}
        />
      )

    case 'client':
      return (
        <div className="flex items-center space-x-3">
          {item.clientPhotoUrl && (
            <img
              src={item.clientPhotoUrl}
              alt={item.clientName}
              className="h-8 w-8 rounded-full object-cover"
            />
          )}
          <div>
            <div className="font-medium text-gray-900">{item.clientName}</div>
            <div className="text-sm text-gray-500">{item.clientTitle}</div>
          </div>
        </div>
      )

    case 'company':
      return (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 bg-gray-100 rounded-lg flex items-center justify-center">
              <span className="text-gray-600 text-xs font-medium">
                {value?.charAt(0)?.toUpperCase()}
              </span>
            </div>
          </div>
          <div>
            <div className="font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500">{item.industry || item.contactName}</div>
          </div>
        </div>
      )

    case 'project':
      return (
        <div>
          <div className="font-medium text-gray-900">{value}</div>
          <div className="text-sm text-gray-500 truncate max-w-xs">
            {item.description}
          </div>
        </div>
      )

    case 'truncated':
      const maxLength = renderProps.maxLength || 50
      const truncated = String(value).length > maxLength 
        ? String(value).substring(0, maxLength) + '...'
        : String(value)
      
      return (
        <div className="max-w-xs">
          <p className="text-sm text-gray-900 truncate" title={String(value)}>
            {renderProps.quote ? `"${truncated}"` : truncated}
          </p>
        </div>
      )

    case 'overdue':
      if (!value) return <span className="text-gray-400">—</span>
      try {
        const date = value instanceof Date ? value : new Date(value)
        const isOverdue = date < new Date()
        return (
          <span className={isOverdue ? 'text-red-600 font-medium' : 'text-gray-900'}>
            {date.toLocaleDateString()}
            {isOverdue && <span className="text-xs ml-1">(Overdue)</span>}
          </span>
        )
      } catch (error) {
        return <span className="text-gray-400">Invalid date</span>
      }

    case 'text':
    default:
      // Handle arrays
      if (Array.isArray(value)) {
        return <span>{value.join(', ')}</span>
      }
      
      // Handle objects
      if (typeof value === 'object') {
        return <span>{JSON.stringify(value)}</span>
      }
      
      // Handle numbers with formatting
      if (typeof value === 'number' && renderProps.format) {
        if (renderProps.format === 'currency') {
          return <span>${value.toLocaleString()}</span>
        }
        if (renderProps.format === 'number') {
          return <span>{value.toLocaleString()}</span>
        }
      }
      
      return <span>{String(value)}</span>
  }
}
