'use client';

import { JobsManager } from '@/components/admin/jobs/jobs-manager';
import { CrudConfig } from '@/components/admin/crud/types';

interface Job {
  id: number
  title: string
  description: string
  requirements: string
  location: string
  employmenttype: string
  salarymin?: number
  salarymax?: number
  salarycurrency?: string
  isremote?: boolean
  isactive: boolean
  expiresat?: string
  createdat: string
  updatedat?: string
  _count?: {
    jobapplications: number
  }
}

const jobConfig: CrudConfig<Job> = {
  title: 'Jobs',
  description: 'Manage job listings, applications, and recruitment process',
  endpoint: 'jobs', // API endpoint

  columns: [
    {
      key: 'title',
      label: 'Job Title',
      sortable: true,
      searchable: true,
      renderType: 'text',
      width: '250px',
      hideable: false, // Always visible
      defaultVisible: true
    },
    {
      key: 'location',
      label: 'Location',
      sortable: true,
      searchable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'employmenttype',
      label: 'Type',
      sortable: true,
      searchable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'salary',
      label: 'Salary Range',
      sortable: false,
      renderType: 'custom',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'isremote',
      label: 'Remote',
      sortable: true,
      renderType: 'status',
      renderProps: {
        trueLabel: 'Remote',
        falseLabel: 'On-site',
        statusColors: {
          true: 'bg-green-100 text-green-800',
          false: 'bg-gray-100 text-gray-800'
        }
      },
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'applications',
      label: 'Applications',
      sortable: false,
      renderType: 'number',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'expiresat',
      label: 'Expires',
      sortable: true,
      renderType: 'date',
      hideable: true,
      defaultVisible: false
    },
    {
      key: 'updatedat',
      label: 'Last Updated',
      sortable: true,
      renderType: 'date',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'isactive',
      label: 'Status',
      sortable: true,
      renderType: 'status',
      renderProps: {
        trueLabel: 'Active',
        falseLabel: 'Inactive',
        statusColors: {
          true: 'bg-green-100 text-green-800',
          false: 'bg-red-100 text-red-800'
        }
      },
      hideable: true,
      defaultVisible: true
    }
  ],

  // Action buttons for each row
  actions: [
    {
      action: 'view',
      label: 'View',
      icon: 'EyeIcon',
      variant: 'secondary',
      tooltip: 'Preview job listing'
    },
    {
      action: 'edit',
      label: 'Edit',
      icon: 'PencilIcon',
      variant: 'primary',
      tooltip: 'Edit job listing'
    },
    {
      action: 'toggle-status',
      label: 'Toggle Status',
      icon: 'PowerIcon',
      variant: 'warning',
      tooltip: 'Activate/Deactivate job listing'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      tooltip: 'Delete job listing'
    }
  ],

  fields: [
    {
      key: 'title',
      label: 'Job Title',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'Enter job title'
    },
    {
      key: 'description',
      label: 'Job Description',
      type: 'textarea',
      required: true,
      searchable: true,
      placeholder: 'Detailed job description...',
      rows: 4
    },
    {
      key: 'requirements',
      label: 'Requirements',
      type: 'textarea',
      required: true,
      searchable: true,
      placeholder: 'Job requirements and qualifications...',
      rows: 4
    },
    {
      key: 'location',
      label: 'Location',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'e.g., San Francisco, CA or Remote'
    },
    {
      key: 'employmenttype',
      label: 'Employment Type',
      type: 'select',
      required: true,
      options: [
        { value: '', label: 'Select Type' },
        { value: 'Full-time', label: 'Full-time' },
        { value: 'Part-time', label: 'Part-time' },
        { value: 'Contract', label: 'Contract' },
        { value: 'Internship', label: 'Internship' },
        { value: 'Freelance', label: 'Freelance' },
      ],
      searchable: false,
    },
    {
      key: 'salarymin',
      label: 'Minimum Salary',
      type: 'number',
      searchable: false,
      placeholder: '50000'
    },
    {
      key: 'salarymax',
      label: 'Maximum Salary',
      type: 'number',
      searchable: false,
      placeholder: '80000'
    },
    {
      key: 'salarycurrency',
      label: 'Currency',
      type: 'select',
      options: [
        { value: 'USD', label: 'USD' },
        { value: 'EUR', label: 'EUR' },
        { value: 'GBP', label: 'GBP' },
        { value: 'CAD', label: 'CAD' },
      ],
      defaultValue: 'USD',
      searchable: false,
    },
    {
      key: 'isremote',
      label: 'Remote Work',
      type: 'boolean',
      defaultValue: false,
      searchable: false,
    },
    {
      key: 'expiresat',
      label: 'Expires At',
      type: 'datetime-local',
      searchable: false,
    },
    {
      key: 'isactive',
      label: 'Active Status',
      type: 'boolean',
      defaultValue: true,
      searchable: false,
    },
  ],

  filters: [
    {
      key: 'employmenttype',
      label: 'Employment Type',
      type: 'select',
      options: [
        { value: '', label: 'All Types' },
        { value: 'Full-time', label: 'Full-time' },
        { value: 'Part-time', label: 'Part-time' },
        { value: 'Contract', label: 'Contract' },
        { value: 'Internship', label: 'Internship' },
        { value: 'Freelance', label: 'Freelance' },
      ],
    },
    {
      key: 'isremote',
      label: 'Work Type',
      type: 'select',
      options: [
        { value: '', label: 'All' },
        { value: 'true', label: 'Remote' },
        { value: 'false', label: 'On-site' },
      ],
    },
    {
      key: 'isactive',
      label: 'Status',
      type: 'select',
      options: [
        { value: '', label: 'All' },
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' },
      ],
    },
  ],

  bulkActions: [
    {
      label: 'Activate Selected',
      action: 'activate',
      variant: 'success'
    },
    {
      label: 'Deactivate Selected',
      action: 'deactivate',
      variant: 'warning'
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search jobs by title, description, location...',
  defaultSort: { field: 'updatedat', direction: 'desc' }, // Sort by Last Updated (most recent)
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['title', 'location', 'employmenttype', 'salary', 'isremote', 'applications', 'updatedat', 'isactive']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 2,
    sections: [
      {
        title: 'Basic Information',
        fields: ['title', 'location', 'employmenttype']
      },
      {
        title: 'Job Details',
        fields: ['description', 'requirements']
      },
      {
        title: 'Compensation',
        fields: ['salarymin', 'salarymax', 'salarycurrency']
      },
      {
        title: 'Settings',
        fields: ['isremote', 'expiresat', 'isactive']
      }
    ]
  }
};

export default function JobsPage() {
  return <JobsManager config={jobConfig} />;
}
