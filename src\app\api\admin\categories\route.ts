import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  withError<PERSON>and<PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/categories - Get all categories with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['name', 'description'])
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get categories with pagination
  const [categories, total] = await Promise.all([
    prisma.categories.findMany({
      where: searchQuery,
      include: {
        services: {
          select: {
            id: true,
            name: true,
            isactive: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.categories.count({ where: searchQuery })
  ])

  return paginatedResponse(categories, page, limit, total)
})

// POST /api/admin/categories - Create a new category
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.category.create)
  const data = await validate(request)

  const category = await prisma.categories.create({
    data,
    include: {
      services: {
        select: {
          id: true,
          name: true,
          isactive: true
        }
      }
    }
  })

  return successResponse(category, 'Category created successfully', 201)
})
