// Monitoring and error tracking utilities

interface ErrorLog {
  id: string
  timestamp: Date
  level: 'error' | 'warn' | 'info' | 'debug'
  message: string
  stack?: string
  context?: Record<string, any>
  userId?: string
  sessionId?: string
  url?: string
  userAgent?: string
}

interface PerformanceMetric {
  id: string
  timestamp: Date
  name: string
  value: number
  unit: string
  context?: Record<string, any>
}

// In-memory storage for development (replace with database in production)
const errorLogs: ErrorLog[] = []
const performanceMetrics: PerformanceMetric[] = []

// Generate unique ID
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// Error logging functions
export const logger = {
  error: (message: string, error?: Error, context?: Record<string, any>) => {
    const errorLog: ErrorLog = {
      id: generateId(),
      timestamp: new Date(),
      level: 'error',
      message,
      stack: error?.stack,
      context,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
    }

    errorLogs.push(errorLog)
    
    // Console log in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error logged:', errorLog)
    }

    // Send to external service in production
    if (process.env.NODE_ENV === 'production') {
      sendToExternalService('error', errorLog)
    }

    return errorLog.id
  },

  warn: (message: string, context?: Record<string, any>) => {
    const errorLog: ErrorLog = {
      id: generateId(),
      timestamp: new Date(),
      level: 'warn',
      message,
      context,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
    }

    errorLogs.push(errorLog)
    
    if (process.env.NODE_ENV === 'development') {
      console.warn('Warning logged:', errorLog)
    }

    return errorLog.id
  },

  info: (message: string, context?: Record<string, any>) => {
    const errorLog: ErrorLog = {
      id: generateId(),
      timestamp: new Date(),
      level: 'info',
      message,
      context,
    }

    errorLogs.push(errorLog)
    
    if (process.env.NODE_ENV === 'development') {
      console.info('Info logged:', errorLog)
    }

    return errorLog.id
  },

  debug: (message: string, context?: Record<string, any>) => {
    if (process.env.NODE_ENV !== 'development') return

    const errorLog: ErrorLog = {
      id: generateId(),
      timestamp: new Date(),
      level: 'debug',
      message,
      context,
    }

    errorLogs.push(errorLog)
    console.debug('Debug logged:', errorLog)

    return errorLog.id
  },
}

// Performance monitoring
export const performance = {
  // Track page load times
  trackPageLoad: (pageName: string) => {
    if (typeof window === 'undefined') return

    const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    
    if (navigation) {
      const metrics = [
        { name: 'dns_lookup', value: navigation.domainLookupEnd - navigation.domainLookupStart },
        { name: 'tcp_connection', value: navigation.connectEnd - navigation.connectStart },
        { name: 'server_response', value: navigation.responseEnd - navigation.requestStart },
        { name: 'dom_processing', value: navigation.domContentLoadedEventEnd - navigation.responseEnd },
        { name: 'total_load_time', value: navigation.loadEventEnd - navigation.navigationStart },
      ]

      metrics.forEach(metric => {
        if (metric.value > 0) {
          trackMetric(`page_load_${metric.name}`, metric.value, 'ms', { page: pageName })
        }
      })
    }
  },

  // Track API response times
  trackApiCall: (endpoint: string, method: string, duration: number, status: number) => {
    trackMetric('api_response_time', duration, 'ms', {
      endpoint,
      method,
      status,
    })

    if (status >= 400) {
      logger.warn(`API call failed: ${method} ${endpoint}`, {
        status,
        duration,
      })
    }
  },

  // Track database query times
  trackDbQuery: (query: string, duration: number) => {
    trackMetric('db_query_time', duration, 'ms', { query })

    if (duration > 1000) { // Slow query threshold
      logger.warn('Slow database query detected', {
        query,
        duration,
      })
    }
  },

  // Track memory usage
  trackMemoryUsage: () => {
    if (typeof window === 'undefined' || !('memory' in performance)) return

    const memory = (performance as any).memory
    trackMetric('memory_used', memory.usedJSHeapSize, 'bytes')
    trackMetric('memory_total', memory.totalJSHeapSize, 'bytes')
    trackMetric('memory_limit', memory.jsHeapSizeLimit, 'bytes')
  },

  // Track user interactions
  trackUserInteraction: (action: string, element: string, duration?: number) => {
    trackMetric('user_interaction', duration || 1, 'count', {
      action,
      element,
    })
  },
}

// Generic metric tracking
function trackMetric(name: string, value: number, unit: string, context?: Record<string, any>) {
  const metric: PerformanceMetric = {
    id: generateId(),
    timestamp: new Date(),
    name,
    value,
    unit,
    context,
  }

  performanceMetrics.push(metric)

  if (process.env.NODE_ENV === 'development') {
    console.debug('Metric tracked:', metric)
  }

  if (process.env.NODE_ENV === 'production') {
    sendToExternalService('metric', metric)
  }
}

// Health check functions
export const healthCheck = {
  // Check database connectivity
  checkDatabase: async (): Promise<boolean> => {
    try {
      const { prisma } = await import('./prisma')
      await prisma.$queryRaw`SELECT 1`
      return true
    } catch (error) {
      logger.error('Database health check failed', error as Error)
      return false
    }
  },

  // Check external services
  checkExternalServices: async (): Promise<Record<string, boolean>> => {
    const services = {
      email: await checkEmailService(),
      analytics: checkAnalyticsService(),
    }

    return services
  },

  // Overall system health
  getSystemHealth: async () => {
    const dbHealth = await healthCheck.checkDatabase()
    const externalServices = await healthCheck.checkExternalServices()
    
    const health = {
      status: dbHealth && Object.values(externalServices).every(Boolean) ? 'healthy' : 'degraded',
      timestamp: new Date(),
      checks: {
        database: dbHealth,
        ...externalServices,
      },
      metrics: {
        errorCount: errorLogs.filter(log => log.level === 'error' && 
          log.timestamp > new Date(Date.now() - 60000)).length, // Last minute
        avgResponseTime: getAverageResponseTime(),
      },
    }

    return health
  },
}

// Helper functions
async function checkEmailService(): Promise<boolean> {
  try {
    // Simple check - verify SMTP configuration exists
    return !!(process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS)
  } catch {
    return false
  }
}

function checkAnalyticsService(): boolean {
  return !!process.env.NEXT_PUBLIC_GA_ID
}

function getAverageResponseTime(): number {
  const recentMetrics = performanceMetrics.filter(
    metric => metric.name === 'api_response_time' && 
    metric.timestamp > new Date(Date.now() - 300000) // Last 5 minutes
  )

  if (recentMetrics.length === 0) return 0

  const total = recentMetrics.reduce((sum, metric) => sum + metric.value, 0)
  return Math.round(total / recentMetrics.length)
}

// Send data to external monitoring service (placeholder)
async function sendToExternalService(type: 'error' | 'metric', data: any) {
  // In production, integrate with services like:
  // - Sentry for error tracking
  // - DataDog for metrics
  // - New Relic for APM
  // - LogRocket for user sessions
  
  if (process.env.SENTRY_DSN && type === 'error') {
    // Send to Sentry
    console.log('Would send to Sentry:', data)
  }
  
  if (process.env.DATADOG_API_KEY && type === 'metric') {
    // Send to DataDog
    console.log('Would send to DataDog:', data)
  }
}

// Error boundary for React components
export class ErrorBoundary extends Error {
  constructor(message: string, public componentStack?: string) {
    super(message)
    this.name = 'ErrorBoundary'
  }
}

// Global error handler for unhandled errors
export function setupGlobalErrorHandling() {
  if (typeof window === 'undefined') return

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    logger.error('Unhandled promise rejection', event.reason, {
      type: 'unhandledrejection',
      promise: event.promise,
    })
  })

  // Handle JavaScript errors
  window.addEventListener('error', (event) => {
    logger.error('JavaScript error', event.error, {
      type: 'javascript_error',
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    })
  })
}

// Get logs and metrics (for admin dashboard)
export function getLogs(level?: ErrorLog['level'], limit: number = 100): ErrorLog[] {
  let logs = errorLogs
  
  if (level) {
    logs = logs.filter(log => log.level === level)
  }
  
  return logs
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, limit)
}

export function getMetrics(name?: string, limit: number = 100): PerformanceMetric[] {
  let metrics = performanceMetrics
  
  if (name) {
    metrics = metrics.filter(metric => metric.name === name)
  }
  
  return metrics
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, limit)
}
