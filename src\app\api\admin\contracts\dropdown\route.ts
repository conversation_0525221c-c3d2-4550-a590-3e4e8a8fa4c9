import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin
} from '@/lib/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/contracts/dropdown - Get contracts for dropdown selection
export const GET = with<PERSON>rror<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const contracts = await prisma.contracts.findMany({
    select: {
      id: true,
      title: true,
      status: true,
      totalamount: true,
      client: {
        select: {
          companyname: true,
        },
      },
    },
    orderBy: {
      createdat: 'desc',
    },
  })

  // Format for dropdown
  const formattedContracts = contracts.map(contract => ({
    value: contract.id,
    label: `${contract.title} - ${contract.client.companyname} (${contract.status})`,
    title: contract.title,
    clientname: contract.client.companyname,
    status: contract.status,
    totalamount: contract.totalamount,
  }))

  return successResponse(formattedContracts)
})
