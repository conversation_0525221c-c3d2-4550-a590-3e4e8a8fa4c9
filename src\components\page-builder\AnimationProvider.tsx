'use client'

import React, { createContext, useContext, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface AnimationContextType {
  isAnimationEnabled: boolean
  toggleAnimations: () => void
  animationSpeed: 'slow' | 'normal' | 'fast'
  setAnimationSpeed: (speed: 'slow' | 'normal' | 'fast') => void
}

const AnimationContext = createContext<AnimationContextType | undefined>(undefined)

export const useAnimations = () => {
  const context = useContext(AnimationContext)
  if (!context) {
    throw new Error('useAnimations must be used within an AnimationProvider')
  }
  return context
}

interface AnimationProviderProps {
  children: React.ReactNode
}

export const AnimationProvider: React.FC<AnimationProviderProps> = ({ children }) => {
  const [isAnimationEnabled, setIsAnimationEnabled] = useState(true)
  const [animationSpeed, setAnimationSpeed] = useState<'slow' | 'normal' | 'fast'>('normal')

  const toggleAnimations = () => {
    setIsAnimationEnabled(!isAnimationEnabled)
  }

  const value = {
    isAnimationEnabled,
    toggleAnimations,
    animationSpeed,
    setAnimationSpeed
  }

  return (
    <AnimationContext.Provider value={value}>
      {children}
    </AnimationContext.Provider>
  )
}

// Animation variants
export const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
}

export const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 }
}

export const slideInFromLeft = {
  initial: { opacity: 0, x: -50 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -50 }
}

export const slideInFromRight = {
  initial: { opacity: 0, x: 50 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 50 }
}

export const scaleIn = {
  initial: { opacity: 0, scale: 0.9 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.9 }
}

export const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
}

// Animation duration based on speed
export const getAnimationDuration = (speed: 'slow' | 'normal' | 'fast') => {
  switch (speed) {
    case 'slow':
      return 0.5
    case 'fast':
      return 0.2
    default:
      return 0.3
  }
}

// Enhanced motion components with animation controls
interface AnimatedDivProps {
  children: React.ReactNode
  variant?: any
  className?: string
  delay?: number
  duration?: number
  [key: string]: any
}

export const AnimatedDiv: React.FC<AnimatedDivProps> = ({ 
  children, 
  variant = fadeInUp, 
  className = '',
  delay = 0,
  duration,
  ...props 
}) => {
  const { isAnimationEnabled, animationSpeed } = useAnimations()
  
  if (!isAnimationEnabled) {
    return <div className={className} {...props}>{children}</div>
  }

  const animationDuration = duration || getAnimationDuration(animationSpeed)

  return (
    <motion.div
      variants={variant}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{ 
        duration: animationDuration,
        delay,
        ease: "easeOut"
      }}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  )
}

export const AnimatedButton: React.FC<AnimatedDivProps> = ({ 
  children, 
  className = '',
  ...props 
}) => {
  const { isAnimationEnabled } = useAnimations()
  
  if (!isAnimationEnabled) {
    return <button className={className} {...props}>{children}</button>
  }

  return (
    <motion.button
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.1 }}
      className={className}
      {...props}
    >
      {children}
    </motion.button>
  )
}

export const AnimatedCard: React.FC<AnimatedDivProps> = ({ 
  children, 
  className = '',
  ...props 
}) => {
  const { isAnimationEnabled } = useAnimations()
  
  if (!isAnimationEnabled) {
    return <div className={className} {...props}>{children}</div>
  }

  return (
    <motion.div
      variants={scaleIn}
      initial="initial"
      animate="animate"
      exit="exit"
      whileHover={{ 
        y: -2,
        boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
      }}
      transition={{ duration: 0.2 }}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  )
}

// Loading animations
export const LoadingSpinner: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ size = 'md' }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  return (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      className={`${sizeClasses[size]} border-2 border-blue-600 border-t-transparent rounded-full`}
    />
  )
}

export const LoadingDots: React.FC = () => {
  return (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: i * 0.2
          }}
          className="w-2 h-2 bg-blue-600 rounded-full"
        />
      ))}
    </div>
  )
}

// Page transition wrapper
export const PageTransition: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAnimationEnabled } = useAnimations()
  
  if (!isAnimationEnabled) {
    return <>{children}</>
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  )
}

// Stagger animation for lists
export const StaggeredList: React.FC<{ 
  children: React.ReactNode
  className?: string
}> = ({ children, className = '' }) => {
  const { isAnimationEnabled } = useAnimations()
  
  if (!isAnimationEnabled) {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      className={className}
    >
      {children}
    </motion.div>
  )
}

// Notification animations
export const NotificationContainer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <AnimatePresence mode="wait">
      {children}
    </AnimatePresence>
  )
}

export const Notification: React.FC<{
  children: React.ReactNode
  type?: 'success' | 'error' | 'warning' | 'info'
  onClose?: () => void
}> = ({ children, type = 'info', onClose }) => {
  const { isAnimationEnabled } = useAnimations()
  
  const typeColors = {
    success: 'bg-green-50 border-green-200 text-green-800',
    error: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800'
  }

  if (!isAnimationEnabled) {
    return (
      <div className={`p-4 border rounded-lg ${typeColors[type]}`}>
        {children}
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -50, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -50, scale: 0.9 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={`p-4 border rounded-lg ${typeColors[type]}`}
    >
      {children}
    </motion.div>
  )
}
