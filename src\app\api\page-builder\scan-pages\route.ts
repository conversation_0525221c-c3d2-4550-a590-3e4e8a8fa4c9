import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { scanAppDirectory, getPagePreviewData, ScannedPage } from '@/lib/page-builder/page-scanner'
import { prisma } from '@/lib/prisma'

// GET /api/page-builder/scan-pages - Scan and return actual website pages
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Scan the app directory for pages
    const scannedPages = await scanAppDirectory()
    
    // Get existing pages from database
    const dbPages = await prisma.pages.findMany({
      include: {
        creator: {
          select: {
            id: true,
            firstname: true,
            lastname: true,
            email: true
          }
        }
      }
    })

    // Merge scanned pages with database pages
    const mergedPages = scannedPages.map(scannedPage => {
      // Check if this page exists in database
      const dbPage = dbPages.find(p => p.slug === scannedPage.slug)
      
      if (dbPage) {
        // Use database version but include scanned metadata
        return {
          id: dbPage.id.toString(),
          title: dbPage.title,
          slug: dbPage.slug,
          description: dbPage.description,
          metaTitle: dbPage.metaTitle,
          metaDescription: dbPage.metaDescription,
          isPublished: dbPage.isPublished,
          isHomePage: dbPage.isHomePage,
          layout: dbPage.layout || scannedPage.layout,
          styles: dbPage.styles || scannedPage.styles,
          seoSettings: dbPage.seoSettings || scannedPage.seoSettings,
          createdAt: dbPage.createdAt,
          updatedAt: dbPage.updatedAt,
          publishedAt: dbPage.publishedAt,
          creator: dbPage.creator,
          // Additional metadata from scanning
          sourceFile: scannedPage.filePath,
          sourceCode: scannedPage.sourceCode,
          lastModified: scannedPage.lastModified,
          type: scannedPage.type,
          hasParams: scannedPage.hasParams,
          preview: getPagePreviewData(scannedPage),
          source: 'database' as const
        }
      } else {
        // New page found in filesystem
        return {
          id: scannedPage.id,
          title: scannedPage.title,
          slug: scannedPage.slug,
          description: scannedPage.description,
          metaTitle: scannedPage.metaTitle,
          metaDescription: scannedPage.metaDescription,
          isPublished: scannedPage.isPublished,
          isHomePage: scannedPage.isHomePage,
          layout: scannedPage.layout,
          styles: scannedPage.styles,
          seoSettings: scannedPage.seoSettings,
          createdAt: scannedPage.lastModified,
          updatedAt: scannedPage.lastModified,
          publishedAt: scannedPage.isPublished ? scannedPage.lastModified : null,
          creator: null,
          // Additional metadata from scanning
          sourceFile: scannedPage.filePath,
          sourceCode: scannedPage.sourceCode,
          lastModified: scannedPage.lastModified,
          type: scannedPage.type,
          hasParams: scannedPage.hasParams,
          preview: getPagePreviewData(scannedPage),
          source: 'filesystem' as const
        }
      }
    })

    // Add database-only pages (pages that exist in DB but not in filesystem)
    const dbOnlyPages = dbPages.filter(dbPage => 
      !scannedPages.some(scannedPage => scannedPage.slug === dbPage.slug)
    ).map(dbPage => ({
      id: dbPage.id.toString(),
      title: dbPage.title,
      slug: dbPage.slug,
      description: dbPage.description,
      metaTitle: dbPage.metaTitle,
      metaDescription: dbPage.metaDescription,
      isPublished: dbPage.isPublished,
      isHomePage: dbPage.isHomePage,
      layout: dbPage.layout || [],
      styles: dbPage.styles || {},
      seoSettings: dbPage.seoSettings || {},
      createdAt: dbPage.createdAt,
      updatedAt: dbPage.updatedAt,
      publishedAt: dbPage.publishedAt,
      creator: dbPage.creator,
      // No source file data
      sourceFile: null,
      sourceCode: null,
      lastModified: dbPage.updatedAt,
      type: 'database' as const,
      hasParams: false,
      preview: {
        thumbnail: `data:image/svg+xml,${encodeURIComponent(`
          <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="300" height="200" fill="#f8f9fa"/>
            <rect x="20" y="20" width="260" height="40" fill="#e9ecef" rx="4"/>
            <text x="150" y="120" text-anchor="middle" font-family="Arial" font-size="14" fill="#6c757d">
              Page Builder Page
            </text>
          </svg>
        `)}`,
        componentCount: Array.isArray(dbPage.layout) ? dbPage.layout.length : 0,
        complexity: 'simple' as const
      },
      source: 'database' as const
    }))

    const allPages = [...mergedPages, ...dbOnlyPages]

    // Sort pages by type and name
    allPages.sort((a, b) => {
      // Home page first
      if (a.isHomePage) return -1
      if (b.isHomePage) return 1
      
      // Then by source type (filesystem first, then database)
      if (a.source !== b.source) {
        if (a.source === 'filesystem') return -1
        if (b.source === 'filesystem') return 1
      }
      
      // Finally by title
      return a.title.localeCompare(b.title)
    })

    return NextResponse.json({
      success: true,
      data: allPages,
      meta: {
        total: allPages.length,
        scanned: scannedPages.length,
        database: dbPages.length,
        merged: mergedPages.length,
        dbOnly: dbOnlyPages.length
      }
    })

  } catch (error) {
    console.error('Error scanning pages:', error)
    return NextResponse.json(
      { 
        error: 'Failed to scan pages',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST /api/page-builder/scan-pages - Import a scanned page to database
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { pageId, importOptions = {} } = body

    // Re-scan to get latest data
    const scannedPages = await scanAppDirectory()
    const pageToImport = scannedPages.find(p => p.id === pageId)

    if (!pageToImport) {
      return NextResponse.json({ error: 'Page not found' }, { status: 404 })
    }

    // Check if page already exists in database
    const existingPage = await prisma.pages.findUnique({
      where: { slug: pageToImport.slug }
    })

    if (existingPage && !importOptions.overwrite) {
      return NextResponse.json({ 
        error: 'Page already exists in database',
        suggestion: 'Use overwrite option to replace existing page'
      }, { status: 409 })
    }

    // Import the page
    const pageData = {
      title: pageToImport.title,
      slug: pageToImport.slug,
      description: pageToImport.description,
      metaTitle: pageToImport.metaTitle,
      metaDescription: pageToImport.metaDescription,
      isHomePage: pageToImport.isHomePage,
      isPublished: pageToImport.isPublished,
      layout: pageToImport.layout,
      styles: pageToImport.styles,
      seoSettings: pageToImport.seoSettings,
      createdBy: BigInt(session.user.id)
    }

    let savedPage
    if (existingPage && importOptions.overwrite) {
      // Update existing page
      savedPage = await prisma.pages.update({
        where: { id: existingPage.id },
        data: {
          ...pageData,
          updatedAt: new Date()
        }
      })
    } else {
      // Create new page
      savedPage = await prisma.pages.create({
        data: pageData
      })
    }

    return NextResponse.json({
      success: true,
      data: savedPage,
      message: `Page "${pageToImport.title}" imported successfully`
    })

  } catch (error) {
    console.error('Error importing page:', error)
    return NextResponse.json(
      { 
        error: 'Failed to import page',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
