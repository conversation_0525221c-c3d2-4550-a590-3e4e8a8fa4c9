import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

// Optional imports - will gracefully handle if not available
let prisma: any = null
let getServerSession: any = null
let authOptions: any = null

try {
  prisma = require('@/lib/prisma').prisma
} catch (e) {
  console.warn('Prisma not available:', e)
}

try {
  getServerSession = require('next-auth').getServerSession
  authOptions = require('@/lib/auth').authOptions
} catch (e) {
  console.warn('NextAuth not available:', e)
}

// Define the structure of a scanned page (moved here to avoid client-side imports)
interface ScannedPage {
  id: string
  title: string
  slug: string
  filePath: string
  description?: string
  metaTitle?: string
  metaDescription?: string
  isPublished: boolean
  isHomePage: boolean
  layout: any[]
  styles: Record<string, any>
  seoSettings: Record<string, any>
  sourceCode: string
  lastModified: Date
  type: 'static' | 'dynamic'
  hasParams: boolean
}

// Helper functions for scanning (moved here to be server-side only)
function extractPageMetadata(sourceCode: string, slug: string): {
  title: string
  description?: string
  metaTitle?: string
  metaDescription?: string
} {
  const defaultTitle = slug.split('/').pop()?.replace(/[-_]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase()) || 'Untitled Page'

  // Try to extract title from various patterns
  const titlePatterns = [
    /<title[^>]*>([^<]+)<\/title>/i,
    /title:\s*['"`]([^'"`]+)['"`]/i,
    /<h1[^>]*>([^<]+)<\/h1>/i,
    /const\s+title\s*=\s*['"`]([^'"`]+)['"`]/i
  ]

  const descriptionPatterns = [
    /<meta\s+name=['"`]description['"`]\s+content=['"`]([^'"`]+)['"`]/i,
    /description:\s*['"`]([^'"`]+)['"`]/i,
    /<p[^>]*>([^<]{50,200})<\/p>/i
  ]

  let title = defaultTitle
  let description: string | undefined
  let metaTitle: string | undefined
  let metaDescription: string | undefined

  // Extract title
  for (const pattern of titlePatterns) {
    const match = sourceCode.match(pattern)
    if (match) {
      title = match[1].trim()
      break
    }
  }

  // Extract description
  for (const pattern of descriptionPatterns) {
    const match = sourceCode.match(pattern)
    if (match) {
      description = match[1].trim()
      break
    }
  }

  return {
    title,
    description,
    metaTitle: metaTitle || title,
    metaDescription: metaDescription || description
  }
}

function convertToPageBuilderComponents(sourceCode: string, slug: string): any[] {
  const components: any[] = []
  let componentId = 1

  // Helper function to create a component
  const createComponent = (
    type: string,
    x: number,
    y: number,
    width: number,
    height: number,
    content: any,
    styles: any = {}
  ) => ({
    id: `${slug}-${type}-${componentId++}`,
    type,
    position: { x, y, width, height },
    content,
    styles: {
      backgroundColor: '#ffffff',
      padding: '20px',
      borderRadius: '8px',
      ...styles
    },
    isLocked: false,
    isVisible: true,
    zIndex: 1
  })

  // Extract hero sections
  const heroPatterns = [
    /<section[^>]*hero[^>]*>([\s\S]*?)<\/section>/gi,
    /<div[^>]*hero[^>]*>([\s\S]*?)<\/div>/gi,
    /<header[^>]*>([\s\S]*?)<\/header>/gi
  ]

  let yOffset = 0

  for (const pattern of heroPatterns) {
    const matches = sourceCode.matchAll(pattern)
    for (const match of matches) {
      const heroContent = match[1]

      // Extract title and subtitle
      const titleMatch = heroContent.match(/<h1[^>]*>([^<]+)<\/h1>/i)
      const subtitleMatch = heroContent.match(/<p[^>]*>([^<]+)<\/p>/i)
      const buttonMatch = heroContent.match(/<button[^>]*>([^<]+)<\/button>/i) ||
                         heroContent.match(/<a[^>]*button[^>]*>([^<]+)<\/a>/i)

      if (titleMatch) {
        components.push(createComponent('hero', 0, yOffset, 1200, 600, {
          title: titleMatch[1].trim(),
          subtitle: subtitleMatch?.[1]?.trim() || '',
          buttonText: buttonMatch?.[1]?.trim() || 'Learn More',
          buttonUrl: '#',
          backgroundType: 'gradient'
        }, {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: '#ffffff',
          textAlign: 'center'
        }))
        yOffset += 620
      }
    }
  }

  // If no components were extracted, create a basic text component
  if (components.length === 0) {
    const metadata = extractPageMetadata(sourceCode, slug)
    components.push(createComponent('text', 0, 0, 1200, 400, {
      heading: metadata.title,
      content: metadata.description || 'This page content will be converted to editable components.',
      alignment: 'center'
    }))
  }

  return components
}

async function scanAppDirectory(appPath: string = 'src/app'): Promise<ScannedPage[]> {
  const pages: ScannedPage[] = []

  try {
    // Helper function to recursively scan directories
    const scanDirectory = (dirPath: string, relativePath: string = '') => {
      if (!fs.existsSync(dirPath)) return

      const items = fs.readdirSync(dirPath, { withFileTypes: true })

      for (const item of items) {
        const fullPath = path.join(dirPath, item.name)
        const currentRelativePath = path.join(relativePath, item.name).replace(/\\/g, '/')

        if (item.isDirectory()) {
          // Skip certain directories
          if (['api', 'admin', 'auth', 'preview'].includes(item.name)) {
            continue
          }

          // Recursively scan subdirectories
          scanDirectory(fullPath, currentRelativePath)
        } else if (item.isFile() && item.name === 'page.tsx') {
          // Found a page file
          const sourceCode = fs.readFileSync(fullPath, 'utf-8')
          const stats = fs.statSync(fullPath)

          // Determine slug from path
          let slug = relativePath.replace(/\\/g, '/')
          if (slug === '') slug = 'home' // Root page

          // Extract metadata
          const metadata = extractPageMetadata(sourceCode, slug)

          // Convert to page builder components
          const components = convertToPageBuilderComponents(sourceCode, slug)

          pages.push({
            id: `scanned-${slug.replace(/[^a-zA-Z0-9]/g, '-')}`,
            title: metadata.title,
            slug: slug === 'home' ? '' : slug, // Empty slug for home page
            filePath: fullPath,
            description: metadata.description,
            metaTitle: metadata.metaTitle,
            metaDescription: metadata.metaDescription,
            isPublished: true, // Assume existing pages are published
            isHomePage: slug === 'home',
            layout: components,
            styles: {},
            seoSettings: {
              title: metadata.metaTitle,
              description: metadata.metaDescription,
              keywords: []
            },
            sourceCode,
            lastModified: stats.mtime,
            type: 'static',
            hasParams: false
          })
        }
      }
    }

    // Start scanning from the app directory
    if (fs.existsSync(appPath)) {
      scanDirectory(appPath)
    }

  } catch (error) {
    console.error('Error scanning app directory:', error)
  }

  return pages
}

function getPagePreviewData(page: ScannedPage): {
  thumbnail: string
  componentCount: number
  complexity: 'simple' | 'moderate' | 'complex'
} {
  const componentCount = page.layout.length

  let complexity: 'simple' | 'moderate' | 'complex' = 'simple'
  if (componentCount > 5) complexity = 'moderate'
  if (componentCount > 10) complexity = 'complex'

  // Generate a simple thumbnail representation
  const thumbnail = `data:image/svg+xml,${encodeURIComponent(`
    <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect width="300" height="200" fill="#f8f9fa"/>
      <rect x="20" y="20" width="260" height="40" fill="#e9ecef" rx="4"/>
      <rect x="20" y="80" width="120" height="20" fill="#dee2e6" rx="2"/>
      <rect x="20" y="110" width="200" height="20" fill="#dee2e6" rx="2"/>
      <rect x="20" y="140" width="160" height="20" fill="#dee2e6" rx="2"/>
      <text x="150" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="#6c757d">
        ${componentCount} components
      </text>
    </svg>
  `)}`

  return {
    thumbnail,
    componentCount,
    complexity
  }
}

// GET /api/page-builder/scan-pages - Scan and return actual website pages
export async function GET(request: NextRequest) {
  try {
    // Optional auth check - skip if auth is not available
    if (getServerSession && authOptions) {
      try {
        const session = await getServerSession(authOptions)
        if (!session || session.user.role !== 'ADMIN') {
          return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
        }
      } catch (authError) {
        console.warn('Auth check failed, continuing without auth:', authError)
      }
    } else {
      console.log('🔓 Auth not available, skipping auth check')
    }

    console.log('🔍 Starting page scan...')

    // Scan the app directory for pages
    const scannedPages = await scanAppDirectory()
    console.log(`📄 Found ${scannedPages.length} pages in filesystem`)

    // Get existing pages from database
    let dbPages: any[] = []
    if (prisma) {
      try {
        dbPages = await prisma.pages.findMany({
          include: {
            creator: {
              select: {
                id: true,
                firstname: true,
                lastname: true,
                email: true
              }
            }
          }
        })
        console.log(`🗄️ Found ${dbPages.length} pages in database`)
      } catch (dbError) {
        console.warn('⚠️ Database query failed, continuing with filesystem pages only:', dbError)
        // Continue without database pages if there's an error
      }
    } else {
      console.log('🗄️ Database not available, using filesystem pages only')
    }

    // Merge scanned pages with database pages
    const mergedPages = scannedPages.map(scannedPage => {
      // Check if this page exists in database
      const dbPage = dbPages.find(p => p.slug === scannedPage.slug)
      
      if (dbPage) {
        // Use database version but include scanned metadata
        return {
          id: dbPage.id.toString(),
          title: dbPage.title,
          slug: dbPage.slug,
          description: dbPage.description,
          metaTitle: dbPage.metaTitle,
          metaDescription: dbPage.metaDescription,
          isPublished: dbPage.isPublished,
          isHomePage: dbPage.isHomePage,
          layout: dbPage.layout || scannedPage.layout,
          styles: dbPage.styles || scannedPage.styles,
          seoSettings: dbPage.seoSettings || scannedPage.seoSettings,
          createdAt: dbPage.createdAt,
          updatedAt: dbPage.updatedAt,
          publishedAt: dbPage.publishedAt,
          creator: dbPage.creator,
          // Additional metadata from scanning
          sourceFile: scannedPage.filePath,
          sourceCode: scannedPage.sourceCode,
          lastModified: scannedPage.lastModified,
          type: scannedPage.type,
          hasParams: scannedPage.hasParams,
          preview: getPagePreviewData(scannedPage),
          source: 'database' as const
        }
      } else {
        // New page found in filesystem
        return {
          id: scannedPage.id,
          title: scannedPage.title,
          slug: scannedPage.slug,
          description: scannedPage.description,
          metaTitle: scannedPage.metaTitle,
          metaDescription: scannedPage.metaDescription,
          isPublished: scannedPage.isPublished,
          isHomePage: scannedPage.isHomePage,
          layout: scannedPage.layout,
          styles: scannedPage.styles,
          seoSettings: scannedPage.seoSettings,
          createdAt: scannedPage.lastModified,
          updatedAt: scannedPage.lastModified,
          publishedAt: scannedPage.isPublished ? scannedPage.lastModified : null,
          creator: null,
          // Additional metadata from scanning
          sourceFile: scannedPage.filePath,
          sourceCode: scannedPage.sourceCode,
          lastModified: scannedPage.lastModified,
          type: scannedPage.type,
          hasParams: scannedPage.hasParams,
          preview: getPagePreviewData(scannedPage),
          source: 'filesystem' as const
        }
      }
    })

    // Add database-only pages (pages that exist in DB but not in filesystem)
    const dbOnlyPages = dbPages.filter(dbPage => 
      !scannedPages.some(scannedPage => scannedPage.slug === dbPage.slug)
    ).map(dbPage => ({
      id: dbPage.id.toString(),
      title: dbPage.title,
      slug: dbPage.slug,
      description: dbPage.description,
      metaTitle: dbPage.metaTitle,
      metaDescription: dbPage.metaDescription,
      isPublished: dbPage.isPublished,
      isHomePage: dbPage.isHomePage,
      layout: dbPage.layout || [],
      styles: dbPage.styles || {},
      seoSettings: dbPage.seoSettings || {},
      createdAt: dbPage.createdAt,
      updatedAt: dbPage.updatedAt,
      publishedAt: dbPage.publishedAt,
      creator: dbPage.creator,
      // No source file data
      sourceFile: null,
      sourceCode: null,
      lastModified: dbPage.updatedAt,
      type: 'database' as const,
      hasParams: false,
      preview: {
        thumbnail: `data:image/svg+xml,${encodeURIComponent(`
          <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="300" height="200" fill="#f8f9fa"/>
            <rect x="20" y="20" width="260" height="40" fill="#e9ecef" rx="4"/>
            <text x="150" y="120" text-anchor="middle" font-family="Arial" font-size="14" fill="#6c757d">
              Page Builder Page
            </text>
          </svg>
        `)}`,
        componentCount: Array.isArray(dbPage.layout) ? dbPage.layout.length : 0,
        complexity: 'simple' as const
      },
      source: 'database' as const
    }))

    const allPages = [...mergedPages, ...dbOnlyPages]

    // If no pages found, create a sample page
    if (allPages.length === 0) {
      console.log('📝 No pages found, creating sample page')
      allPages.push({
        id: 'sample-home',
        title: 'Home Page',
        slug: '',
        description: 'Welcome to your website',
        metaTitle: 'Home Page',
        metaDescription: 'Welcome to your website',
        isPublished: true,
        isHomePage: true,
        layout: [{
          id: 'sample-hero',
          type: 'hero',
          position: { x: 0, y: 0, width: 1200, height: 600 },
          content: {
            title: 'Welcome to Your Website',
            subtitle: 'This is a sample page. Create your first page to get started!',
            buttonText: 'Get Started',
            buttonUrl: '#'
          },
          styles: {
            backgroundColor: '#ffffff',
            padding: '20px',
            borderRadius: '8px'
          },
          isLocked: false,
          isVisible: true,
          zIndex: 1
        }],
        styles: {},
        seoSettings: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        publishedAt: new Date(),
        creator: null,
        sourceFile: null,
        sourceCode: null,
        lastModified: new Date(),
        type: 'sample' as const,
        hasParams: false,
        preview: {
          thumbnail: `data:image/svg+xml,${encodeURIComponent(`
            <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
              <rect width="300" height="200" fill="#f8f9fa"/>
              <rect x="20" y="20" width="260" height="40" fill="#e9ecef" rx="4"/>
              <text x="150" y="120" text-anchor="middle" font-family="Arial" font-size="14" fill="#6c757d">
                Sample Page
              </text>
            </svg>
          `)}`,
          componentCount: 1,
          complexity: 'simple' as const
        },
        source: 'sample' as const
      })
    }

    // Sort pages by type and name
    allPages.sort((a, b) => {
      // Home page first
      if (a.isHomePage) return -1
      if (b.isHomePage) return 1

      // Then by source type (filesystem first, then database)
      if (a.source !== b.source) {
        if (a.source === 'filesystem') return -1
        if (b.source === 'filesystem') return 1
      }

      // Finally by title
      return a.title.localeCompare(b.title)
    })

    return NextResponse.json({
      success: true,
      data: allPages,
      meta: {
        total: allPages.length,
        scanned: scannedPages.length,
        database: dbPages.length,
        merged: mergedPages.length,
        dbOnly: dbOnlyPages.length
      }
    })

  } catch (error) {
    console.error('Error scanning pages:', error)
    return NextResponse.json(
      { 
        error: 'Failed to scan pages',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST /api/page-builder/scan-pages - Import a scanned page to database
export async function POST(request: NextRequest) {
  try {
    // Check if required dependencies are available
    if (!prisma) {
      return NextResponse.json({ error: 'Database not available' }, { status: 503 })
    }

    // Optional auth check
    if (getServerSession && authOptions) {
      try {
        const session = await getServerSession(authOptions)
        if (!session || session.user.role !== 'ADMIN') {
          return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
        }
      } catch (authError) {
        console.warn('Auth check failed for POST:', authError)
        return NextResponse.json({ error: 'Authentication error' }, { status: 401 })
      }
    }

    const body = await request.json()
    const { pageId, importOptions = {} } = body

    // Re-scan to get latest data
    const scannedPages = await scanAppDirectory()
    const pageToImport = scannedPages.find(p => p.id === pageId)

    if (!pageToImport) {
      return NextResponse.json({ error: 'Page not found' }, { status: 404 })
    }

    // Check if page already exists in database
    const existingPage = await prisma.pages.findUnique({
      where: { slug: pageToImport.slug }
    })

    if (existingPage && !importOptions.overwrite) {
      return NextResponse.json({ 
        error: 'Page already exists in database',
        suggestion: 'Use overwrite option to replace existing page'
      }, { status: 409 })
    }

    // Import the page
    const pageData = {
      title: pageToImport.title,
      slug: pageToImport.slug,
      description: pageToImport.description,
      metaTitle: pageToImport.metaTitle,
      metaDescription: pageToImport.metaDescription,
      isHomePage: pageToImport.isHomePage,
      isPublished: pageToImport.isPublished,
      layout: pageToImport.layout,
      styles: pageToImport.styles,
      seoSettings: pageToImport.seoSettings,
      createdBy: BigInt(session.user.id)
    }

    let savedPage
    if (existingPage && importOptions.overwrite) {
      // Update existing page
      savedPage = await prisma.pages.update({
        where: { id: existingPage.id },
        data: {
          ...pageData,
          updatedAt: new Date()
        }
      })
    } else {
      // Create new page
      savedPage = await prisma.pages.create({
        data: pageData
      })
    }

    return NextResponse.json({
      success: true,
      data: savedPage,
      message: `Page "${pageToImport.title}" imported successfully`
    })

  } catch (error) {
    console.error('Error importing page:', error)
    return NextResponse.json(
      { 
        error: 'Failed to import page',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
