import { prisma } from './prisma'
import bcrypt from 'bcryptjs'

export async function createAdminUser() {
  try {
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (existingAdmin) {
      console.log('Admin user already exists:', existingAdmin.email)
      return existingAdmin
    }

    // Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 12)
    
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Admin',
        lastName: 'User',
        role: 'ADMIN',
        emailVerified: new Date(),
      }
    })

    console.log('Admin user created successfully!')
    console.log('Email: <EMAIL>')
    console.log('Password: admin123')
    
    return adminUser
  } catch (error) {
    console.error('Error creating admin user:', error)
    throw error
  }
}

// Run this function to create admin user
if (require.main === module) {
  createAdminUser()
    .then(() => {
      console.log('Admin user setup completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Admin user setup failed:', error)
      process.exit(1)
    })
}
