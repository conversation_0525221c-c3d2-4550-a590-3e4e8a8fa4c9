import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/invoices - Get all invoices with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['invoiceNumber', 'description'])
  
  // Add status filter if provided
  if (filter) {
    searchQuery.status = filter
  }
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get invoices with pagination
  const [invoices, total] = await Promise.all([
    prisma.invoicess.findMany({
      where: searchQuery,
      include: {
        client: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        project: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        order: {
          select: {
            id: true,
            ordernumber: true,
            status: true
          }
        },
        contract: {
          select: {
            id: true,
            title: true,
            status: true
          }
        },
        items: true,
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paidat: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.invoicess.count({ where: searchQuery })
  ])

  return paginatedResponse(invoices, page, limit, total)
})

// POST /api/admin/invoices - Create a new invoice
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const body = await request.json()
  console.log('Received invoice data:', JSON.stringify(body, null, 2))

  const { items, ...invoiceData } = body

  // Validate invoice data
  console.log('Validating invoice data:', JSON.stringify(invoiceData, null, 2))
  const validatedData = schemas.invoice.create.parse(invoiceData)

  // Create invoice with items in a transaction
  const invoice = await prisma.$transaction(async (tx) => {
    // Create the invoice
    const newInvoice = await tx.invoice.create({
      data: validatedData,
    })

    // Create invoice items if provided
    if (items && Array.isArray(items) && items.length > 0) {
      const validItems = items.filter(item => item.description && item.description.trim() !== '')

      if (validItems.length > 0) {
        await tx.invoiceItem.createMany({
          data: validItems.map(item => ({
            invoiceId: newInvoice.id,
            description: item.description,
            quantity: Number(item.quantity) || 1,
            unitprice: Number(item.unitPrice) || 0,
            totalprice: Number(item.totalPrice) || 0,
          }))
        })
      }
    }

    // Return the complete invoice with relations
    return await tx.invoice.findUnique({
      where: { id: newInvoice.id },
      include: {
        client: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        project: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        items: true,
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paidat: true
          }
        }
      }
    })
  })

  return successResponse(invoice, 'Invoice created successfully', 201)
})
