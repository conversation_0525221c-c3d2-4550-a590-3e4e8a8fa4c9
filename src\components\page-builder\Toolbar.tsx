'use client'

import React from 'react'
import { usePageBuilderStore } from '@/lib/page-builder/store'
import { alignComponents, distributeComponents } from '@/lib/page-builder/utils'
import {
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  DocumentDuplicateIcon,
  TrashIcon,
  ViewfinderCircleIcon,
  Squares2X2Icon,
  MagnifyingGlassMinusIcon,
  MagnifyingGlassPlusIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon
} from '@heroicons/react/24/outline'

const Toolbar: React.FC = () => {
  const {
    selectedComponentIds,
    getSelectedComponents,
    updateComponent,
    deleteComponent,
    duplicateComponent,
    undo,
    redo,
    canUndo,
    canRedo,
    showGrid,
    snapToGrid,
    showRulers,
    showOutlines,
    toggleGrid,
    toggleSnapToGrid,
    toggleRulers,
    toggleOutlines,
    gridSize,
    setGridSize
  } = usePageBuilderStore()
  
  const selectedComponents = getSelectedComponents()
  const hasSelection = selectedComponents.length > 0
  const hasMultipleSelection = selectedComponents.length > 1
  
  // Handle alignment
  const handleAlign = (alignment: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom') => {
    if (selectedComponents.length < 2) return
    
    const alignedComponents = alignComponents(selectedComponents, alignment)
    alignedComponents.forEach(component => {
      updateComponent(component.id, { position: component.position })
    })
  }
  
  // Handle distribution
  const handleDistribute = (direction: 'horizontal' | 'vertical') => {
    if (selectedComponents.length < 3) return
    
    const distributedComponents = distributeComponents(selectedComponents, direction)
    distributedComponents.forEach(component => {
      updateComponent(component.id, { position: component.position })
    })
  }
  
  // Handle delete
  const handleDelete = () => {
    selectedComponentIds.forEach(id => deleteComponent(id))
  }
  
  // Handle duplicate
  const handleDuplicate = () => {
    selectedComponentIds.forEach(id => duplicateComponent(id))
  }
  
  return (
    <div className="h-12 bg-white border-b border-gray-200 flex items-center justify-between px-4">
      {/* Left section - History controls */}
      <div className="flex items-center space-x-2">
        <button
          onClick={undo}
          disabled={!canUndo()}
          className={`p-2 rounded-md ${
            canUndo()
              ? 'text-gray-600 hover:bg-gray-100'
              : 'text-gray-300 cursor-not-allowed'
          }`}
          title="Undo (Ctrl+Z)"
        >
          <ArrowUturnLeftIcon className="h-4 w-4" />
        </button>
        
        <button
          onClick={redo}
          disabled={!canRedo()}
          className={`p-2 rounded-md ${
            canRedo()
              ? 'text-gray-600 hover:bg-gray-100'
              : 'text-gray-300 cursor-not-allowed'
          }`}
          title="Redo (Ctrl+Y)"
        >
          <ArrowUturnRightIcon className="h-4 w-4" />
        </button>
        
        <div className="w-px h-6 bg-gray-300" />
        
        {/* Component actions */}
        <button
          onClick={handleDuplicate}
          disabled={!hasSelection}
          className={`p-2 rounded-md ${
            hasSelection
              ? 'text-gray-600 hover:bg-gray-100'
              : 'text-gray-300 cursor-not-allowed'
          }`}
          title="Duplicate (Ctrl+D)"
        >
          <DocumentDuplicateIcon className="h-4 w-4" />
        </button>
        
        <button
          onClick={handleDelete}
          disabled={!hasSelection}
          className={`p-2 rounded-md ${
            hasSelection
              ? 'text-red-600 hover:bg-red-50'
              : 'text-gray-300 cursor-not-allowed'
          }`}
          title="Delete (Del)"
        >
          <TrashIcon className="h-4 w-4" />
        </button>
      </div>
      
      {/* Center section - Alignment and distribution */}
      {hasMultipleSelection && (
        <div className="flex items-center space-x-2">
          {/* Alignment controls */}
          <div className="flex items-center space-x-1 px-2 py-1 bg-gray-50 rounded-md">
            <span className="text-xs text-gray-600 mr-2">Align:</span>
            
            <button
              onClick={() => handleAlign('left')}
              className="p-1 text-xs text-gray-600 hover:bg-gray-200 rounded"
              title="Align left"
            >
              ⫷
            </button>
            
            <button
              onClick={() => handleAlign('center')}
              className="p-1 text-xs text-gray-600 hover:bg-gray-200 rounded"
              title="Align center"
            >
              ⫸
            </button>
            
            <button
              onClick={() => handleAlign('right')}
              className="p-1 text-xs text-gray-600 hover:bg-gray-200 rounded"
              title="Align right"
            >
              ⫹
            </button>
            
            <div className="w-px h-4 bg-gray-300 mx-1" />
            
            <button
              onClick={() => handleAlign('top')}
              className="p-1 text-xs text-gray-600 hover:bg-gray-200 rounded"
              title="Align top"
            >
              ⫶
            </button>
            
            <button
              onClick={() => handleAlign('middle')}
              className="p-1 text-xs text-gray-600 hover:bg-gray-200 rounded"
              title="Align middle"
            >
              ⫽
            </button>
            
            <button
              onClick={() => handleAlign('bottom')}
              className="p-1 text-xs text-gray-600 hover:bg-gray-200 rounded"
              title="Align bottom"
            >
              ⫼
            </button>
          </div>
          
          {/* Distribution controls */}
          {selectedComponents.length >= 3 && (
            <div className="flex items-center space-x-1 px-2 py-1 bg-gray-50 rounded-md">
              <span className="text-xs text-gray-600 mr-2">Distribute:</span>
              
              <button
                onClick={() => handleDistribute('horizontal')}
                className="p-1 text-xs text-gray-600 hover:bg-gray-200 rounded"
                title="Distribute horizontally"
              >
                ⟷
              </button>
              
              <button
                onClick={() => handleDistribute('vertical')}
                className="p-1 text-xs text-gray-600 hover:bg-gray-200 rounded"
                title="Distribute vertically"
              >
                ↕
              </button>
            </div>
          )}
        </div>
      )}
      
      {/* Right section - View controls */}
      <div className="flex items-center space-x-2">
        {/* Grid controls */}
        <div className="flex items-center space-x-1">
          <button
            onClick={toggleGrid}
            className={`p-2 rounded-md ${
              showGrid
                ? 'bg-blue-50 text-blue-700'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            title="Toggle grid"
          >
            <Squares2X2Icon className="h-4 w-4" />
          </button>
          
          <button
            onClick={toggleSnapToGrid}
            className={`p-2 rounded-md ${
              snapToGrid
                ? 'bg-blue-50 text-blue-700'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            title="Toggle snap to grid"
          >
            <ViewfinderCircleIcon className="h-4 w-4" />
          </button>
          
          <select
            value={gridSize}
            onChange={(e) => setGridSize(Number(e.target.value))}
            className="text-xs border border-gray-300 rounded px-2 py-1"
            title="Grid size"
          >
            <option value={5}>5px</option>
            <option value={10}>10px</option>
            <option value={20}>20px</option>
            <option value={25}>25px</option>
            <option value={50}>50px</option>
          </select>
        </div>
        
        <div className="w-px h-6 bg-gray-300" />
        
        {/* View options */}
        <button
          onClick={toggleRulers}
          className={`p-2 rounded-md ${
            showRulers
              ? 'bg-blue-50 text-blue-700'
              : 'text-gray-600 hover:bg-gray-100'
          }`}
          title="Toggle rulers"
        >
          📏
        </button>
        
        <button
          onClick={toggleOutlines}
          className={`p-2 rounded-md ${
            showOutlines
              ? 'bg-blue-50 text-blue-700'
              : 'text-gray-600 hover:bg-gray-100'
          }`}
          title="Toggle component outlines"
        >
          ⬜
        </button>
        
        <div className="w-px h-6 bg-gray-300" />
        
        {/* Zoom controls */}
        <div className="flex items-center space-x-1">
          <button
            className="p-2 rounded-md text-gray-600 hover:bg-gray-100"
            title="Zoom out"
            onClick={() => {
              // TODO: Implement zoom functionality
              console.log('Zoom out')
            }}
          >
            <MagnifyingGlassMinusIcon className="h-4 w-4" />
          </button>
          
          <span className="text-xs text-gray-600 px-2">100%</span>
          
          <button
            className="p-2 rounded-md text-gray-600 hover:bg-gray-100"
            title="Zoom in"
            onClick={() => {
              // TODO: Implement zoom functionality
              console.log('Zoom in')
            }}
          >
            <MagnifyingGlassPlusIcon className="h-4 w-4" />
          </button>
          
          <button
            className="p-2 rounded-md text-gray-600 hover:bg-gray-100"
            title="Fit to screen"
            onClick={() => {
              // TODO: Implement fit to screen functionality
              console.log('Fit to screen')
            }}
          >
            <ArrowsPointingInIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default Toolbar
