import { NextAuthOptions } from 'next-auth'
import Credentials<PERSON>rovider from 'next-auth/providers/credentials'
import GoogleProvider from 'next-auth/providers/google'
import GitHub<PERSON>rovider from 'next-auth/providers/github'
import { PrismaAdapter } from '@next-auth/prisma-adapter'
import { prisma } from './prisma'
import bcrypt from 'bcryptjs'
import { AuditLogger } from './audit-log'

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    // Credentials provider for email/password login
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          // Validate email format
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(credentials.email)) {
            await AuditLogger.logAuth(
              'LOGIN_FAILED',
              undefined,
              { email: credentials.email, reason: 'Invalid email format' }
            )
            return null
          }

          // Find user in database
          const user = await prisma.users.findUnique({
            where: {
              email: credentials.email.toLowerCase().trim()
            }
          })

          // Check if user exists and is active
          if (!user) {
            await AuditLogger.logAuth(
              'LOGIN_FAILED',
              undefined,
              { email: credentials.email, reason: 'User not found' }
            )
            return null
          }

          if (!user.password) {
            await AuditLogger.logAuth(
              'LOGIN_FAILED',
              user.id.toString(),
              { email: credentials.email, reason: 'No password set for user' }
            )
            return null
          }

          if (!user.isactive) {
            await AuditLogger.logAuth(
              'LOGIN_FAILED',
              user.id.toString(),
              { email: credentials.email, reason: 'User account is inactive' }
            )
            return null
          }

          // Verify password
          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          )

          if (!isPasswordValid) {
            await AuditLogger.logAuth(
              'LOGIN_FAILED',
              user.id.toString(),
              { email: credentials.email, reason: 'Invalid password' }
            )
            return null
          }

          // Create user result object
          const userResult = {
            id: user.id.toString(),
            email: user.email,
            name: `${user.firstname || ''} ${user.lastname || ''}`.trim() || user.email,
            role: user.role,
            image: user.imageurl,
          }

          // Log successful login
          await AuditLogger.logAuth(
            'LOGIN_SUCCESS',
            user.id.toString(),
            { email: credentials.email, role: user.role }
          )

          return userResult
        } catch (error) {
          console.error('❌ Auth error:', error)
          return null
        }
      }
    }),

    // Google OAuth provider
    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? [
      GoogleProvider({
        clientId: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        profile(profile) {
          return {
            id: profile.sub,
            name: profile.name,
            email: profile.email,
            image: profile.picture,
            role: 'USER', // Default role for OAuth users
          }
        },
      })
    ] : []),

    // GitHub OAuth provider
    ...(process.env.GITHUB_ID && process.env.GITHUB_SECRET ? [
      GitHubProvider({
        clientId: process.env.GITHUB_ID,
        clientSecret: process.env.GITHUB_SECRET,
        profile(profile) {
          return {
            id: profile.id.toString(),
            name: profile.name || profile.login,
            email: profile.email || '',
            image: profile.avatar_url,
            role: 'USER', // Default role for OAuth users
          }
        },
      })
    ] : []),
  ],
  session: {
    strategy: 'jwt'
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as string
      }
      return session
    }
  },
  pages: {
    signIn: '/auth/signin',
    signOut: '/auth/signout',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET,
}

// Helper function to get server session
export async function getServerSession() {
  const { getServerSession } = await import('next-auth/next')
  return getServerSession(authOptions)
}

// Helper function to require authentication
export async function requireAuth() {
  const session = await getServerSession()
  if (!session) {
    throw new Error('Authentication required')
  }
  return session
}

// Helper function to require admin role
export async function requireAdmin() {
  const session = await requireAuth()
  if (session.user.role !== 'ADMIN') {
    throw new Error('Admin access required')
  }
  return session
}
