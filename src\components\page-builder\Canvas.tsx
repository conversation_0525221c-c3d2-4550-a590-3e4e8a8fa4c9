'use client'

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { Rnd } from 'react-rnd'
import { usePageBuilderStore, Component } from '@/lib/page-builder/store'
import { snapToGrid, snapPositionToGrid, snapSizeToGrid, handleKeyboardShortcut } from '@/lib/page-builder/utils'
import ComponentRenderer from './ComponentRenderer'

interface CanvasProps {
  className?: string
}

const Canvas: React.FC<CanvasProps> = ({ className = '' }) => {
  const canvasRef = useRef<HTMLDivElement>(null)
  const [canvasSize, setCanvasSize] = useState({ width: 1200, height: 800 })
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  
  const {
    components,
    selectedComponentIds,
    hoveredComponentId,
    mode,
    viewport,
    showGrid,
    snapToGrid: shouldSnapToGrid,
    gridSize,
    showRulers,
    showOutlines,
    selectComponent,
    selectMultipleComponents,
    clearSelection,
    setHoveredComponent,
    updateComponent,
    deleteComponent,
    duplicateComponent,
    saveToHistory,
    undo,
    redo,
    canUndo,
    canRedo,
    getSelectedComponents
  } = usePageBuilderStore()
  
  // Handle canvas click (deselect all)
  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      clearSelection()
    }
  }, [clearSelection])
  
  // Handle component selection
  const handleComponentSelect = useCallback((id: string, multiSelect = false) => {
    selectComponent(id, multiSelect)
  }, [selectComponent])
  
  // Handle component hover
  const handleComponentHover = useCallback((id: string | null) => {
    if (!isDragging && !isResizing) {
      setHoveredComponent(id)
    }
  }, [setHoveredComponent, isDragging, isResizing])
  
  // Handle component drag
  const handleComponentDrag = useCallback((id: string, position: { x: number; y: number }) => {
    const finalPosition = shouldSnapToGrid
      ? snapPositionToGrid(position, gridSize)
      : position

    const component = components.find(c => c.id === id)
    if (component) {
      updateComponent(id, { position: { ...component.position, ...finalPosition } })
    }
  }, [updateComponent, shouldSnapToGrid, gridSize, components])
  
  // Handle component resize
  const handleComponentResize = useCallback((
    id: string, 
    size: { width: number; height: number },
    position: { x: number; y: number }
  ) => {
    const finalSize = shouldSnapToGrid 
      ? snapSizeToGrid(size, gridSize)
      : size
    
    const finalPosition = shouldSnapToGrid 
      ? snapPositionToGrid(position, gridSize)
      : position
    
    updateComponent(id, { 
      position: { 
        ...finalPosition,
        ...finalSize
      }
    })
  }, [updateComponent, shouldSnapToGrid, gridSize])
  
  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (mode !== 'edit') return
      
      const selectedComponents = getSelectedComponents()
      
      handleKeyboardShortcut(e, selectedComponents, {
        copy: () => {
          // TODO: Implement copy functionality
          console.log('Copy components')
        },
        paste: () => {
          // TODO: Implement paste functionality
          console.log('Paste components')
        },
        delete: () => {
          selectedComponentIds.forEach(id => deleteComponent(id))
        },
        undo: () => {
          if (canUndo()) undo()
        },
        redo: () => {
          if (canRedo()) redo()
        },
        selectAll: () => {
          selectMultipleComponents(components.map(c => c.id))
        },
        duplicate: () => {
          selectedComponentIds.forEach(id => duplicateComponent(id))
        }
      })
    }
    
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [
    mode,
    selectedComponentIds,
    components,
    deleteComponent,
    duplicateComponent,
    undo,
    redo,
    canUndo,
    canRedo,
    selectMultipleComponents,
    getSelectedComponents
  ])
  
  // Update canvas size based on viewport
  useEffect(() => {
    switch (viewport) {
      case 'mobile':
        setCanvasSize({ width: 375, height: 812 })
        break
      case 'tablet':
        setCanvasSize({ width: 768, height: 1024 })
        break
      default:
        setCanvasSize({ width: 1200, height: 800 })
    }
  }, [viewport])
  
  // Grid background style
  const gridStyle = showGrid ? {
    backgroundImage: `
      linear-gradient(to right, #e5e7eb 1px, transparent 1px),
      linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
    `,
    backgroundSize: `${gridSize}px ${gridSize}px`
  } : {}
  
  // Render component with drag and resize functionality
  const renderEditableComponent = (component: Component) => {
    const isSelected = selectedComponentIds.includes(component.id)
    const isHovered = hoveredComponentId === component.id
    
    return (
      <Rnd
        key={component.id}
        size={{
          width: component.position.width,
          height: component.position.height
        }}
        position={{
          x: component.position.x,
          y: component.position.y
        }}
        onDragStart={() => {
          setIsDragging(true)
          if (!isSelected) {
            selectComponent(component.id)
          }
        }}
        onDrag={(e, data) => {
          handleComponentDrag(component.id, { x: data.x, y: data.y })
        }}
        onDragStop={() => {
          setIsDragging(false)
          saveToHistory()
        }}
        onResizeStart={() => {
          setIsResizing(true)
          if (!isSelected) {
            selectComponent(component.id)
          }
        }}
        onResize={(e, direction, ref, delta, position) => {
          handleComponentResize(
            component.id,
            {
              width: ref.offsetWidth,
              height: ref.offsetHeight
            },
            position
          )
        }}
        onResizeStop={() => {
          setIsResizing(false)
          saveToHistory()
        }}
        bounds="parent"
        dragGrid={shouldSnapToGrid ? [gridSize, gridSize] : undefined}
        resizeGrid={shouldSnapToGrid ? [gridSize, gridSize] : undefined}
        enableResizing={isSelected}
        disableDragging={component.isLocked}
        className={`
          ${isSelected ? 'z-50' : 'z-10'}
          ${component.isLocked ? 'opacity-75' : ''}
        `}
      >
        <ComponentRenderer
          component={component}
          isEditing={true}
          isSelected={isSelected}
          isHovered={isHovered}
          onSelect={handleComponentSelect}
          onHover={handleComponentHover}
        />
      </Rnd>
    )
  }
  
  // Render static component for preview mode
  const renderStaticComponent = (component: Component) => {
    return (
      <ComponentRenderer
        key={component.id}
        component={component}
        isEditing={false}
        isSelected={false}
        isHovered={false}
      />
    )
  }
  
  return (
    <div className={`relative bg-white ${className}`}>
      {/* Rulers */}
      {showRulers && mode === 'edit' && (
        <>
          {/* Horizontal ruler */}
          <div className="absolute top-0 left-8 right-0 h-8 bg-gray-100 border-b border-gray-300 z-40">
            <div className="relative h-full">
              {Array.from({ length: Math.ceil(canvasSize.width / 50) }, (_, i) => (
                <div
                  key={i}
                  className="absolute top-0 h-full border-l border-gray-400"
                  style={{ left: i * 50 }}
                >
                  <span className="absolute top-1 left-1 text-xs text-gray-600">
                    {i * 50}
                  </span>
                </div>
              ))}
            </div>
          </div>
          
          {/* Vertical ruler */}
          <div className="absolute top-8 left-0 bottom-0 w-8 bg-gray-100 border-r border-gray-300 z-40">
            <div className="relative w-full h-full">
              {Array.from({ length: Math.ceil(canvasSize.height / 50) }, (_, i) => (
                <div
                  key={i}
                  className="absolute left-0 w-full border-t border-gray-400"
                  style={{ top: i * 50 }}
                >
                  <span className="absolute top-1 left-1 text-xs text-gray-600 transform -rotate-90 origin-top-left">
                    {i * 50}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </>
      )}
      
      {/* Canvas */}
      <div
        ref={canvasRef}
        className={`
          relative overflow-auto
          ${showRulers && mode === 'edit' ? 'ml-8 mt-8' : ''}
        `}
        style={{
          width: canvasSize.width,
          height: canvasSize.height,
          minHeight: '100vh',
          ...gridStyle
        }}
        onClick={handleCanvasClick}
      >
        {/* Components */}
        {mode === 'edit' 
          ? components.map(renderEditableComponent)
          : components.map(renderStaticComponent)
        }
        
        {/* Canvas info overlay */}
        {mode === 'edit' && components.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="text-center text-gray-400">
              <div className="text-6xl mb-4">🎨</div>
              <h3 className="text-xl font-medium mb-2">Start Building Your Page</h3>
              <p className="text-sm">
                Drag components from the left panel to start creating your layout
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Canvas
