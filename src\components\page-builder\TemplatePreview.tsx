'use client'

import React from 'react'
import { PageTemplate } from '@/lib/page-builder/template-store'
import { Component } from '@/lib/page-builder/store'

interface TemplatePreviewProps {
  template: PageTemplate
  className?: string
  scale?: number
}

const TemplatePreview: React.FC<TemplatePreviewProps> = ({ 
  template, 
  className = '',
  scale = 0.2 
}) => {
  // Render a simplified preview of the template
  const renderComponentPreview = (component: Component, index: number) => {
    const scaledPosition = {
      x: component.position.x * scale,
      y: component.position.y * scale,
      width: component.position.width * scale,
      height: component.position.height * scale
    }

    const getComponentColor = (type: string) => {
      switch (type) {
        case 'hero':
          return 'bg-gradient-to-r from-blue-500 to-purple-600'
        case 'features':
          return 'bg-blue-100'
        case 'testimonial':
          return 'bg-gray-100'
        case 'cta':
          return 'bg-gradient-to-r from-green-500 to-blue-600'
        case 'stats':
          return 'bg-yellow-100'
        case 'team':
          return 'bg-purple-100'
        case 'blog':
          return 'bg-indigo-100'
        case 'gallery':
          return 'bg-pink-100'
        case 'pricing':
          return 'bg-emerald-100'
        case 'newsletter':
          return 'bg-orange-100'
        case 'navbar':
          return 'bg-gray-800'
        case 'footer':
          return 'bg-gray-700'
        default:
          return 'bg-gray-200'
      }
    }

    const getComponentIcon = (type: string) => {
      switch (type) {
        case 'hero':
          return '🎯'
        case 'features':
          return '⭐'
        case 'testimonial':
          return '💬'
        case 'cta':
          return '🚀'
        case 'stats':
          return '📊'
        case 'team':
          return '👥'
        case 'blog':
          return '📝'
        case 'gallery':
          return '🖼️'
        case 'pricing':
          return '💰'
        case 'newsletter':
          return '📧'
        case 'navbar':
          return '🧭'
        case 'footer':
          return '🔗'
        default:
          return '📦'
      }
    }

    return (
      <div
        key={`${component.id}-${index}`}
        className={`absolute rounded-sm ${getComponentColor(component.type)} border border-white/20 flex items-center justify-center text-white text-xs font-medium shadow-sm`}
        style={{
          left: scaledPosition.x,
          top: scaledPosition.y,
          width: scaledPosition.width,
          height: scaledPosition.height,
          minHeight: '8px',
          minWidth: '16px'
        }}
        title={`${component.type} component`}
      >
        <span className="opacity-80">
          {getComponentIcon(component.type)}
        </span>
      </div>
    )
  }

  // Calculate total height for the preview container
  const totalHeight = template.components.reduce((max, component) => {
    return Math.max(max, component.position.y + component.position.height)
  }, 0) * scale

  return (
    <div className={`relative bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>
      {/* Preview Container */}
      <div 
        className="relative bg-gray-50"
        style={{ 
          width: 1200 * scale,
          height: Math.max(totalHeight, 200),
          minHeight: '120px'
        }}
      >
        {/* Grid Background */}
        <div 
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `
              linear-gradient(to right, #e5e7eb 1px, transparent 1px),
              linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
            `,
            backgroundSize: `${20 * scale}px ${20 * scale}px`
          }}
        />
        
        {/* Components */}
        {template.components.map((component, index) => 
          renderComponentPreview(component, index)
        )}
        
        {/* Template Info Overlay */}
        <div className="absolute bottom-2 left-2 right-2">
          <div className="bg-black/60 text-white text-xs px-2 py-1 rounded backdrop-blur-sm">
            <div className="flex items-center justify-between">
              <span className="font-medium">{template.name}</span>
              <span className="opacity-75">{template.components.length} components</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Template Details */}
      <div className="p-3 bg-white">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-sm font-medium text-gray-900 truncate">
            {template.name}
          </h4>
          <div className="flex items-center space-x-1">
            {template.isPopular && (
              <span className="text-yellow-500 text-xs">⭐</span>
            )}
            <span className={`w-2 h-2 rounded-full ${
              template.difficulty === 'beginner' ? 'bg-green-500' :
              template.difficulty === 'intermediate' ? 'bg-yellow-500' :
              'bg-red-500'
            }`} title={template.difficulty} />
          </div>
        </div>
        
        <p className="text-xs text-gray-600 mb-2 line-clamp-2">
          {template.description}
        </p>
        
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span className="bg-gray-100 px-2 py-1 rounded">
            {template.category}
          </span>
          <span>{template.usageCount} uses</span>
        </div>
        
        {/* Tags */}
        <div className="flex flex-wrap gap-1 mt-2">
          {template.tags.slice(0, 3).map(tag => (
            <span key={tag} className="px-1.5 py-0.5 bg-blue-50 text-blue-700 text-xs rounded">
              {tag}
            </span>
          ))}
          {template.tags.length > 3 && (
            <span className="px-1.5 py-0.5 bg-gray-50 text-gray-500 text-xs rounded">
              +{template.tags.length - 3}
            </span>
          )}
        </div>
      </div>
    </div>
  )
}

export default TemplatePreview
