import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  validateMethod,
  requireAdmin
} from '@/lib/api-utils'
import { createClientSchema, updateClientSchema } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/clients - List all clients with pagination and search
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search) {
    Object.assign(where, buildSearchQuery(search, [
      'companyname', 
      'contactname', 
      'contactemail', 
      'city', 
      'state', 
      'country'
    ]))
  }
  
  // Add filter for clients with active projects
  if (filter === 'active') {
    where.projects = {
      some: {
        status: {
          in: ['PLANNING', 'IN_PROGRESS']
        }
      }
    }
  } else if (filter === 'inactive') {
    where.projects = {
      none: {
        status: {
          in: ['PLANNING', 'IN_PROGRESS']
        }
      }
    }
  }

  // Get total count for pagination
  const total = await prisma.clientss.count({ where })

  // Get clients with pagination
  const clients = await prisma.clientss.findMany({
    where,
    include: {
      projects: {
        select: {
          id: true,
          name: true,
          status: true,
          projstartdate: true,
          projcompletiondate: true,
          estimatecost: true,
        },
        orderBy: {
          createdat: 'desc',
        },
        take: 5, // Limit to recent projects
      },
      orders: {
        select: {
          id: true,
          ordernumber: true,
          totalamount: true,
          status: true,
          orderdate: true,
        },
        orderBy: {
          createdat: 'desc',
        },
        take: 3, // Limit to recent orders
      },
      invoices: {
        select: {
          id: true,
          invoiceNumber: true,
          totalamount: true,
          status: true,
          issueDate: true,
          duedate: true,
        },
        where: {
          status: {
            in: ['SENT', 'OVERDUE']
          }
        },
        orderBy: {
          duedate: 'asc',
        },
        take: 3, // Limit to pending invoices
      },
      _count: {
        select: {
          projects: true,
          orders: true,
          invoices: true,
          contracts: true,
        },
      },
    },
    orderBy: buildSortQuery(sortBy, sortOrder),
    skip,
    take,
  })

  return paginatedResponse(clients, page, limit, total)
})

// POST /api/clients - Create a new client
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['POST'])
  
  const validate = validateRequest(createClientSchema)
  const data = await validate(request)

  // Check if a client with the same email already exists
  const existingClient = await prisma.clientss.findFirst({
    where: {
      contactemail: data.contactemail,
    },
  })

  if (existingClient) {
    throw new Error('A client with this email already exists')
  }

  // If userId is provided, check if the user exists
  if (data.userId) {
    const user = await prisma.userss.findUnique({
      where: { id: data.userId },
    })

    if (!user) {
      throw new Error('User not found')
    }
  }

  const client = await prisma.clientss.create({
    data,
    include: {
      _count: {
        select: {
          projects: true,
          orders: true,
          invoices: true,
          contracts: true,
        },
      },
    },
  })

  return successResponse(client, 'Client created successfully', 201)
})

// PUT /api/clients - Bulk update clients (admin only)
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid client IDs provided')
  }

  const validate = validateRequest(updateClientSchema)
  const updateData = await validate({ json: () => data } as NextRequest)

  const updatedClients = await prisma.clientss.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: updateData,
  })

  return successResponse(
    { count: updatedClients.count },
    `${updatedClients.count} clients updated successfully`
  )
})

// DELETE /api/clients - Bulk delete clients (admin only)
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid client IDs provided')
  }

  // Check if any clients have associated data that should be preserved
  const clientsWithData = await prisma.clientss.findMany({
    where: {
      id: { in: ids },
      OR: [
        { projects: { some: {} } },
        { orders: { some: {} } },
        { invoices: { some: {} } },
        { contracts: { some: {} } },
      ],
    },
    select: { id: true, companyname: true },
  })

  if (clientsWithData.length > 0) {
    const clientNames = clientsWithData.map(c => c.companyname).join(', ')
    throw new Error(
      `Cannot delete clients with associated data: ${clientNames}. Please handle their projects, orders, invoices, and contracts first.`
    )
  }

  const deletedClients = await prisma.clientss.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedClients.count },
    `${deletedClients.count} clients deleted successfully`
  )
})
