import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/page-builder/preview/[slug] - Get page data for preview
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params
    
    const page = await prisma.pages.findUnique({
      where: { slug },
      include: {
        components: {
          where: { isVisible: true },
          orderBy: {
            order: 'asc'
          }
        }
      }
    })
    
    if (!page) {
      return NextResponse.json({ error: 'Page not found' }, { status: 404 })
    }
    
    // Return page data for rendering
    return NextResponse.json({
      id: page.id.toString(),
      title: page.title,
      slug: page.slug,
      description: page.description,
      metaTitle: page.metaTitle,
      metaDescription: page.metaDescription,
      isPublished: page.isPublished,
      isHomePage: page.isHomePage,
      layout: page.layout,
      styles: page.styles,
      seoSettings: page.seoSettings,
      components: page.components.map(component => ({
        id: component.id.toString(),
        componentId: component.componentId,
        type: component.type,
        name: component.name,
        content: component.content,
        styles: component.styles,
        position: component.position,
        parentId: component.parentId,
        order: component.order,
        isVisible: component.isVisible
      })),
      createdAt: page.createdAt,
      updatedAt: page.updatedAt,
      publishedAt: page.publishedAt
    })
  } catch (error) {
    console.error('Error fetching page for preview:', error)
    return NextResponse.json(
      { error: 'Failed to fetch page' },
      { status: 500 }
    )
  }
}
