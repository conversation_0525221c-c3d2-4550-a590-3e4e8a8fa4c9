import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/page-builder/pages - Get all pages
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const { searchParams } = new URL(request.url)
    const published = searchParams.get('published')
    const search = searchParams.get('search')
    
    const where: any = {}
    
    if (published !== null) {
      where.isPublished = published === 'true'
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { slug: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }
    
    const pages = await prisma.pages.findMany({
      where,
      include: {
        creator: {
          select: {
            id: true,
            firstname: true,
            lastname: true,
            email: true
          }
        },
        components: {
          orderBy: {
            order: 'asc'
          }
        },
        _count: {
          select: {
            components: true,
            versions: true
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    })

    // Convert BigInt to string for JSON serialization
    const serializedPages = pages.map(page => ({
      ...page,
      id: page.id.toString(),
      createdBy: page.createdBy?.toString(),
      creator: page.creator ? {
        ...page.creator,
        id: page.creator.id.toString()
      } : null,
      components: page.components.map(component => ({
        ...component,
        id: component.id.toString(),
        pageId: component.pageId.toString()
      })),
      _count: page._count
    }))

    return NextResponse.json(serializedPages)
  } catch (error) {
    console.error('Error fetching pages:', error)
    return NextResponse.json(
      { error: 'Failed to fetch pages' },
      { status: 500 }
    )
  }
}

// POST /api/page-builder/pages - Create a new page
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const body = await request.json()
    const {
      title,
      slug,
      description,
      metaTitle,
      metaDescription,
      isHomePage = false,
      layout = [],
      styles = {},
      seoSettings = {}
    } = body
    
    // Validate required fields
    if (!title || !slug) {
      return NextResponse.json(
        { error: 'Title and slug are required' },
        { status: 400 }
      )
    }
    
    // Check if slug already exists
    const existingPage = await prisma.pages.findUnique({
      where: { slug }
    })
    
    if (existingPage) {
      return NextResponse.json(
        { error: 'A page with this slug already exists' },
        { status: 400 }
      )
    }
    
    // If this is set as home page, unset other home pages
    if (isHomePage) {
      await prisma.pages.updateMany({
        where: { isHomePage: true },
        data: { isHomePage: false }
      })
    }
    
    const page = await prisma.pages.create({
      data: {
        title,
        slug,
        description,
        metaTitle,
        metaDescription,
        isHomePage,
        layout,
        styles,
        seoSettings,
        createdBy: BigInt(session.user.id)
      },
      include: {
        creator: {
          select: {
            id: true,
            firstname: true,
            lastname: true,
            email: true
          }
        },
        components: true,
        _count: {
          select: {
            components: true,
            versions: true
          }
        }
      }
    })
    
    // Create initial version
    await prisma.pageVersions.create({
      data: {
        pageId: page.id,
        version: 1,
        title: page.title,
        description: 'Initial version',
        layout: layout,
        createdBy: BigInt(session.user.id)
      }
    })
    
    // Convert BigInt to string for JSON serialization
    const serializedPage = {
      ...page,
      id: page.id.toString(),
      createdBy: page.createdBy?.toString(),
      creator: page.creator ? {
        ...page.creator,
        id: page.creator.id.toString()
      } : null,
      components: page.components.map(component => ({
        ...component,
        id: component.id.toString(),
        pageId: component.pageId.toString()
      }))
    }

    return NextResponse.json(serializedPage, { status: 201 })
  } catch (error) {
    console.error('Error creating page:', error)
    return NextResponse.json(
      { error: 'Failed to create page' },
      { status: 500 }
    )
  }
}
