'use client'

import React, { useState, useEffect } from 'react'
import { usePageBuilderStore, Page } from '@/lib/page-builder/store'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  GlobeAltIcon,
  DocumentDuplicateIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'
import { showSuccess, showError } from '@/lib/page-builder/notifications'

interface PageManagerProps {
  onClose: () => void
}

const PageManager: React.FC<PageManagerProps> = ({ onClose }) => {
  const [pages, setPages] = useState<Page[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingPage, setEditingPage] = useState<Page | null>(null)
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    description: ''
  })
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const { setCurrentPage, currentPage } = usePageBuilderStore()
  
  // Fetch pages from API
  useEffect(() => {
    const fetchPages = async () => {
      try {
        setIsLoading(true)
        const response = await fetch('/api/page-builder/pages')

        if (!response.ok) {
          throw new Error('Failed to fetch pages')
        }

        const data = await response.json()
        const formattedPages: Page[] = data.map((page: any) => ({
          id: page.id.toString(),
          title: page.title,
          slug: page.slug,
          description: page.description,
          metaTitle: page.metaTitle,
          metaDescription: page.metaDescription,
          isPublished: page.isPublished,
          isHomePage: page.isHomePage,
          layout: page.layout || [],
          styles: page.styles || {},
          seoSettings: page.seoSettings || {},
          createdAt: new Date(page.createdAt),
          updatedAt: new Date(page.updatedAt),
          publishedAt: page.publishedAt ? new Date(page.publishedAt) : undefined
        }))

        setPages(formattedPages)
      } catch (error) {
        console.error('Error fetching pages:', error)
        setError('Failed to load pages')
      } finally {
        setIsLoading(false)
      }
    }

    fetchPages()
  }, [])
  
  const filteredPages = pages.filter(page =>
    page.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    page.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
    page.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )
  
  const handleSelectPage = async (page: Page) => {
    try {
      // Fetch full page data including components
      const response = await fetch(`/api/page-builder/pages/${page.id}`)

      if (!response.ok) {
        throw new Error('Failed to load page data')
      }

      const pageData = await response.json()

      // Convert the page data to the format expected by the store
      const formattedPage: Page = {
        id: pageData.id,
        title: pageData.title,
        slug: pageData.slug,
        description: pageData.description,
        metaTitle: pageData.metaTitle,
        metaDescription: pageData.metaDescription,
        isPublished: pageData.isPublished,
        isHomePage: pageData.isHomePage,
        layout: pageData.layout || pageData.components || [],
        styles: pageData.styles || {},
        seoSettings: pageData.seoSettings || {},
        createdAt: new Date(pageData.createdAt),
        updatedAt: new Date(pageData.updatedAt),
        publishedAt: pageData.publishedAt ? new Date(pageData.publishedAt) : undefined
      }

      setCurrentPage(formattedPage)
      onClose()
    } catch (error) {
      console.error('Error loading page:', error)
      showError('Load Failed', 'Failed to load page data')
    }
  }
  
  const handleCreatePage = () => {
    setFormData({ title: '', slug: '', description: '' })
    setShowCreateForm(true)
  }
  
  const handleEditPage = (page: Page) => {
    setFormData({
      title: page.title,
      slug: page.slug,
      description: page.description || ''
    })
    setEditingPage(page)
  }
  
  const handleDeletePage = async (pageId: string) => {
    if (!confirm('Are you sure you want to delete this page?')) {
      return
    }

    try {
      const response = await fetch(`/api/page-builder/pages/${pageId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete page')
      }

      setPages(pages.filter(p => p.id !== pageId))
    } catch (error) {
      console.error('Error deleting page:', error)
      alert('Failed to delete page')
    }
  }
  
  const handleDuplicatePage = async (page: Page) => {
    try {
      const response = await fetch('/api/page-builder/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: `${page.title} (Copy)`,
          slug: `${page.slug}-copy-${Date.now()}`,
          description: page.description,
          metaTitle: page.metaTitle,
          metaDescription: page.metaDescription,
          isHomePage: false,
          layout: page.layout,
          styles: page.styles,
          seoSettings: page.seoSettings
        })
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate page')
      }

      const newPage = await response.json()
      const formattedPage: Page = {
        id: newPage.id,
        title: newPage.title,
        slug: newPage.slug,
        description: newPage.description,
        metaTitle: newPage.metaTitle,
        metaDescription: newPage.metaDescription,
        isPublished: newPage.isPublished,
        isHomePage: newPage.isHomePage,
        layout: newPage.layout || [],
        styles: newPage.styles || {},
        seoSettings: newPage.seoSettings || {},
        createdAt: new Date(newPage.createdAt),
        updatedAt: new Date(newPage.updatedAt),
        publishedAt: newPage.publishedAt ? new Date(newPage.publishedAt) : undefined
      }

      setPages([...pages, formattedPage])
    } catch (error) {
      console.error('Error duplicating page:', error)
      alert('Failed to duplicate page')
    }
  }
  
  const handleTogglePublish = async (pageId: string) => {
    const page = pages.find(p => p.id === pageId)
    if (!page) return

    try {
      const response = await fetch(`/api/page-builder/pages/${pageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isPublished: !page.isPublished
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update page')
      }

      const updatedPage = await response.json()
      setPages(pages.map(p =>
        p.id === pageId
          ? {
              ...p,
              isPublished: updatedPage.isPublished,
              updatedAt: new Date(updatedPage.updatedAt),
              publishedAt: updatedPage.publishedAt ? new Date(updatedPage.publishedAt) : undefined
            }
          : p
      ))
    } catch (error) {
      console.error('Error updating page:', error)
      alert('Failed to update page')
    }
  }

  const handleSavePage = async () => {
    if (!formData.title || !formData.slug) {
      alert('Title and slug are required')
      return
    }

    try {
      if (editingPage) {
        // Update existing page
        const response = await fetch(`/api/page-builder/pages/${editingPage.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            title: formData.title,
            slug: formData.slug,
            description: formData.description
          })
        })

        if (!response.ok) {
          throw new Error('Failed to update page')
        }

        const updatedPage = await response.json()
        setPages(pages.map(p =>
          p.id === editingPage.id
            ? {
                ...p,
                title: updatedPage.title,
                slug: updatedPage.slug,
                description: updatedPage.description,
                updatedAt: new Date(updatedPage.updatedAt)
              }
            : p
        ))
      } else {
        // Create new page
        const response = await fetch('/api/page-builder/pages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            title: formData.title,
            slug: formData.slug,
            description: formData.description,
            layout: [],
            styles: {},
            seoSettings: {}
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to create page')
        }

        const newPage = await response.json()
        const formattedPage: Page = {
          id: newPage.id,
          title: newPage.title,
          slug: newPage.slug,
          description: newPage.description,
          metaTitle: newPage.metaTitle,
          metaDescription: newPage.metaDescription,
          isPublished: newPage.isPublished,
          isHomePage: newPage.isHomePage,
          layout: newPage.layout || [],
          styles: newPage.styles || {},
          seoSettings: newPage.seoSettings || {},
          createdAt: new Date(newPage.createdAt),
          updatedAt: new Date(newPage.updatedAt),
          publishedAt: newPage.publishedAt ? new Date(newPage.publishedAt) : undefined
        }

        setPages([...pages, formattedPage])
      }

      // Close form
      setShowCreateForm(false)
      setEditingPage(null)
      setFormData({ title: '', slug: '', description: '' })

    } catch (error) {
      console.error('Error saving page:', error)
      alert(error instanceof Error ? error.message : 'Failed to save page')
    }
  }
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading pages...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-4xl mb-4">❌</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Pages</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }
  
  return (
    <div className="max-h-96 overflow-y-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex-1">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search pages..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        
        <button
          onClick={handleCreatePage}
          className="ml-4 flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          New Page
        </button>
      </div>
      
      {/* Pages list */}
      <div className="space-y-2">
        {filteredPages.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">📄</div>
            <p>No pages found</p>
            {searchTerm && (
              <p className="text-sm">Try adjusting your search term</p>
            )}
          </div>
        ) : (
          filteredPages.map(page => (
            <div
              key={page.id}
              className={`p-4 border rounded-lg hover:bg-gray-50 ${
                currentPage?.id === page.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1 cursor-pointer" onClick={() => handleSelectPage(page)}>
                  <div className="flex items-center space-x-2">
                    <h3 className="font-medium text-gray-900">{page.title}</h3>
                    {page.isHomePage && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Home
                      </span>
                    )}
                    {page.isPublished ? (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <GlobeAltIcon className="h-3 w-3 mr-1" />
                        Published
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Draft
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">/{page.slug}</p>
                  {page.description && (
                    <p className="text-sm text-gray-500 mt-1">{page.description}</p>
                  )}
                  <p className="text-xs text-gray-400 mt-2">
                    Updated {page.updatedAt.toLocaleDateString()}
                  </p>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => handleEditPage(page)}
                    className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded"
                    title="Edit page settings"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  
                  <button
                    onClick={() => handleDuplicatePage(page)}
                    className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded"
                    title="Duplicate page"
                  >
                    <DocumentDuplicateIcon className="h-4 w-4" />
                  </button>
                  
                  <button
                    onClick={() => handleTogglePublish(page.id)}
                    className={`p-2 rounded ${
                      page.isPublished
                        ? 'text-blue-600 hover:text-blue-800 hover:bg-blue-50'
                        : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                    }`}
                    title={page.isPublished ? 'Unpublish page' : 'Publish page'}
                  >
                    {page.isPublished ? <GlobeAltIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                  </button>
                  
                  {!page.isHomePage && (
                    <button
                      onClick={() => handleDeletePage(page.id)}
                      className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded"
                      title="Delete page"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
      
      {/* Create/Edit form modal */}
      {(showCreateForm || editingPage) && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingPage ? 'Edit Page' : 'Create New Page'}
                </h3>
                
                <form className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Page Title
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => {
                        const title = e.target.value
                        const slug = title.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-').trim()
                        setFormData({ ...formData, title, slug: editingPage ? formData.slug : slug })
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter page title"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      URL Slug
                    </label>
                    <input
                      type="text"
                      value={formData.slug}
                      onChange={(e) => setFormData({ ...formData, slug: e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '-') })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="page-url-slug"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Brief description of the page"
                    />
                  </div>
                </form>
              </div>
              
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleSavePage}
                >
                  {editingPage ? 'Save Changes' : 'Create Page'}
                </button>
                
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => {
                    setShowCreateForm(false)
                    setEditingPage(null)
                    setFormData({ title: '', slug: '', description: '' })
                  }}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PageManager
