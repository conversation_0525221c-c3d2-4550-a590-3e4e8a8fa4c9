'use client'

import React, { useState, useEffect } from 'react'
import { usePageBuilderStore, Page } from '@/lib/page-builder/store'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  GlobeAltIcon,
  DocumentDuplicateIcon,
  MagnifyingGlassIcon,
  PowerIcon,
  HomeIcon,
  DocumentTextIcon,
  CalendarIcon,
  UserIcon,
  ChartBarIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  CogIcon,
  CloudArrowUpIcon
} from '@heroicons/react/24/outline'
import { showSuccess, showError } from '@/lib/page-builder/notifications'

interface PageManagerProps {
  onClose: () => void
}

interface ExtendedPage extends Page {
  type: 'custom' | 'system'
  category: string
  lastModified: Date
  modifiedBy?: string
  viewCount?: number
  status: 'active' | 'inactive' | 'draft'
  // Additional properties for scanned pages
  sourceFile?: string | null
  sourceCode?: string | null
  source?: 'database' | 'filesystem'
  preview?: {
    thumbnail: string
    componentCount: number
    complexity: 'simple' | 'moderate' | 'complex'
  }
  hasParams?: boolean
}

const PageManager: React.FC<PageManagerProps> = ({ onClose }) => {
  const [pages, setPages] = useState<ExtendedPage[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingPage, setEditingPage] = useState<ExtendedPage | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState<'name' | 'modified' | 'status'>('modified')
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    description: '',
    metaTitle: '',
    metaDescription: '',
    category: 'general',
    isHomePage: false
  })
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { setCurrentPage, currentPage } = usePageBuilderStore()
  
  // Categories for page organization
  const categories = [
    'all',
    'landing',
    'content',
    'portfolio',
    'blog',
    'ecommerce',
    'contact',
    'legal',
    'general'
  ]

  // Fetch pages from API (now includes scanned pages)
  useEffect(() => {
    const fetchPages = async () => {
      try {
        setIsLoading(true)
        // Use the new scan-pages endpoint to get both database and filesystem pages
        const response = await fetch('/api/page-builder/scan-pages')

        if (!response.ok) {
          throw new Error('Failed to fetch pages')
        }

        const result = await response.json()
        const data = result.data || []

        const formattedPages: ExtendedPage[] = data.map((page: any) => ({
          id: page.id.toString(),
          title: page.title,
          slug: page.slug,
          description: page.description,
          metaTitle: page.metaTitle,
          metaDescription: page.metaDescription,
          isPublished: page.isPublished,
          isHomePage: page.isHomePage,
          layout: page.layout || [],
          styles: page.styles || {},
          seoSettings: page.seoSettings || {},
          createdAt: new Date(page.createdAt),
          updatedAt: new Date(page.updatedAt),
          publishedAt: page.publishedAt ? new Date(page.publishedAt) : undefined,
          // Extended properties
          type: page.source === 'filesystem' ? 'system' :
                page.isHomePage ? 'system' : 'custom',
          category: page.slug.includes('about') ? 'content' :
                   page.slug.includes('contact') ? 'contact' :
                   page.slug.includes('blog') ? 'blog' :
                   page.slug.includes('portfolio') || page.slug.includes('projects') ? 'portfolio' :
                   page.slug.includes('services') ? 'content' :
                   page.slug.includes('team') ? 'content' :
                   page.slug.includes('technologies') ? 'content' :
                   page.isHomePage ? 'landing' : 'general',
          lastModified: new Date(page.lastModified || page.updatedAt),
          modifiedBy: page.creator ? `${page.creator.firstname} ${page.creator.lastname}` :
                     page.source === 'filesystem' ? 'File System' : 'System',
          viewCount: page.preview?.componentCount || Math.floor(Math.random() * 1000),
          status: page.isPublished ? 'active' : 'draft',
          // Additional properties for scanned pages
          sourceFile: page.sourceFile,
          sourceCode: page.sourceCode,
          source: page.source,
          preview: page.preview,
          hasParams: page.hasParams
        }))

        setPages(formattedPages)

        // Show scan results in console for debugging
        if (result.meta) {
          console.log('📄 Page scan results:', result.meta)
        }
      } catch (error) {
        console.error('Error fetching pages:', error)
        setError('Failed to load pages')
      } finally {
        setIsLoading(false)
      }
    }

    fetchPages()
  }, [])

  // Enhanced filtering and sorting
  const filteredAndSortedPages = pages
    .filter(page => {
      const matchesSearch = page.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           page.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           page.description?.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || page.category === selectedCategory
      return matchesSearch && matchesCategory
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.title.localeCompare(b.title)
        case 'modified':
          return b.lastModified.getTime() - a.lastModified.getTime()
        case 'status':
          return a.status.localeCompare(b.status)
        default:
          return 0
      }
    })
  
  // New function to handle page activation/deactivation
  const handleTogglePageStatus = async (pageId: string) => {
    const page = pages.find(p => p.id === pageId)
    if (!page) return

    try {
      const newStatus = page.status === 'active' ? 'inactive' : 'active'
      const isPublished = newStatus === 'active'

      const response = await fetch(`/api/page-builder/pages/${pageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isPublished
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update page status')
      }

      const updatedPage = await response.json()
      setPages(pages.map(p =>
        p.id === pageId
          ? {
              ...p,
              isPublished: updatedPage.isPublished,
              status: updatedPage.isPublished ? 'active' : 'inactive',
              lastModified: new Date(updatedPage.updatedAt),
              publishedAt: updatedPage.publishedAt ? new Date(updatedPage.publishedAt) : undefined
            }
          : p
      ))

      showSuccess('Status Updated', `Page has been ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully!`)
    } catch (error) {
      console.error('Error updating page status:', error)
      showError('Update Failed', 'Failed to update page status. Please try again.')
    }
  }

  // Enhanced page preview function
  const handlePreviewPage = (page: ExtendedPage) => {
    if (page.isPublished) {
      window.open(`/${page.slug}`, '_blank')
    } else {
      window.open(`/preview/${page.slug}`, '_blank')
    }
  }

  // Function to set page as homepage
  const handleSetAsHomePage = async (pageId: string) => {
    try {
      const response = await fetch(`/api/page-builder/pages/${pageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isHomePage: true
        })
      })

      if (!response.ok) {
        throw new Error('Failed to set as home page')
      }

      // Update local state
      setPages(pages.map(p => ({
        ...p,
        isHomePage: p.id === pageId,
        type: p.id === pageId ? 'system' : (p.isHomePage ? 'custom' : p.type)
      })))

      showSuccess('Home Page Updated', 'Page has been set as the home page successfully!')
    } catch (error) {
      console.error('Error setting home page:', error)
      showError('Update Failed', 'Failed to set as home page. Please try again.')
    }
  }

  const handleSelectPage = async (page: ExtendedPage) => {
    try {
      if (page.source === 'filesystem') {
        // For filesystem pages, use the scanned data directly
        const formattedPage: Page = {
          id: page.id,
          title: page.title,
          slug: page.slug,
          description: page.description,
          metaTitle: page.metaTitle,
          metaDescription: page.metaDescription,
          isPublished: page.isPublished,
          isHomePage: page.isHomePage,
          layout: page.layout || [],
          styles: page.styles || {},
          seoSettings: page.seoSettings || {},
          createdAt: page.createdAt,
          updatedAt: page.updatedAt,
          publishedAt: page.publishedAt
        }

        setCurrentPage(formattedPage)
        onClose()
        showSuccess('Live Page Loaded', `"${page.title}" is now ready for editing! This is a live page from your website.`)
      } else {
        // For database pages, fetch full data including components
        const response = await fetch(`/api/page-builder/pages/${page.id}`)

        if (!response.ok) {
          throw new Error('Failed to load page data')
        }

        const pageData = await response.json()

        // Convert the page data to the format expected by the store
        const formattedPage: Page = {
          id: pageData.id,
          title: pageData.title,
          slug: pageData.slug,
          description: pageData.description,
          metaTitle: pageData.metaTitle,
          metaDescription: pageData.metaDescription,
          isPublished: pageData.isPublished,
          isHomePage: pageData.isHomePage,
          layout: pageData.layout || pageData.components || [],
          styles: pageData.styles || {},
          seoSettings: pageData.seoSettings || {},
          createdAt: new Date(pageData.createdAt),
          updatedAt: new Date(pageData.updatedAt),
          publishedAt: pageData.publishedAt ? new Date(pageData.publishedAt) : undefined
        }

        setCurrentPage(formattedPage)
        onClose()
        showSuccess('Page Loaded', `"${pageData.title}" is now ready for editing!`)
      }
    } catch (error) {
      console.error('Error loading page:', error)
      showError('Load Failed', 'Failed to load page data')
    }
  }

  const handleImportPage = async (page: ExtendedPage) => {
    try {
      const response = await fetch('/api/page-builder/scan-pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          pageId: page.id,
          importOptions: {
            overwrite: false
          }
        })
      })

      const result = await response.json()

      if (!response.ok) {
        if (response.status === 409) {
          // Page already exists, ask user if they want to overwrite
          const shouldOverwrite = confirm(
            `Page "${page.title}" already exists in the page builder. Do you want to overwrite it with the current file content?`
          )

          if (shouldOverwrite) {
            const overwriteResponse = await fetch('/api/page-builder/scan-pages', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                pageId: page.id,
                importOptions: {
                  overwrite: true
                }
              })
            })

            if (overwriteResponse.ok) {
              showSuccess('Page Imported', `"${page.title}" has been imported and can now be edited in the page builder!`)
              // Refresh the page list
              window.location.reload()
            } else {
              throw new Error('Failed to overwrite page')
            }
          }
        } else {
          throw new Error(result.error || 'Failed to import page')
        }
      } else {
        showSuccess('Page Imported', `"${page.title}" has been imported and can now be edited in the page builder!`)
        // Refresh the page list
        window.location.reload()
      }
    } catch (error) {
      console.error('Error importing page:', error)
      showError('Import Failed', error instanceof Error ? error.message : 'Failed to import page')
    }
  }

  const handleCreatePage = () => {
    setFormData({
      title: '',
      slug: '',
      description: '',
      metaTitle: '',
      metaDescription: '',
      category: 'general',
      isHomePage: false
    })
    setShowCreateForm(true)
  }

  const handleEditPage = (page: ExtendedPage) => {
    setFormData({
      title: page.title,
      slug: page.slug,
      description: page.description || '',
      metaTitle: page.metaTitle || '',
      metaDescription: page.metaDescription || '',
      category: page.category,
      isHomePage: page.isHomePage
    })
    setEditingPage(page)
  }
  
  const handleDeletePage = async (pageId: string) => {
    if (!confirm('Are you sure you want to delete this page?')) {
      return
    }

    try {
      const response = await fetch(`/api/page-builder/pages/${pageId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete page')
      }

      setPages(pages.filter(p => p.id !== pageId))
    } catch (error) {
      console.error('Error deleting page:', error)
      alert('Failed to delete page')
    }
  }
  
  const handleDuplicatePage = async (page: ExtendedPage) => {
    try {
      const response = await fetch('/api/page-builder/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: `${page.title} (Copy)`,
          slug: `${page.slug}-copy-${Date.now()}`,
          description: page.description,
          metaTitle: page.metaTitle,
          metaDescription: page.metaDescription,
          isHomePage: false,
          layout: page.layout,
          styles: page.styles,
          seoSettings: page.seoSettings
        })
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate page')
      }

      const newPage = await response.json()
      const formattedPage: ExtendedPage = {
        id: newPage.id,
        title: newPage.title,
        slug: newPage.slug,
        description: newPage.description,
        metaTitle: newPage.metaTitle,
        metaDescription: newPage.metaDescription,
        isPublished: newPage.isPublished,
        isHomePage: newPage.isHomePage,
        layout: newPage.layout || [],
        styles: newPage.styles || {},
        seoSettings: newPage.seoSettings || {},
        createdAt: new Date(newPage.createdAt),
        updatedAt: new Date(newPage.updatedAt),
        publishedAt: newPage.publishedAt ? new Date(newPage.publishedAt) : undefined,
        // Extended properties
        type: 'custom',
        category: page.category,
        lastModified: new Date(newPage.updatedAt),
        modifiedBy: 'Current User',
        viewCount: 0,
        status: 'draft'
      }

      setPages([...pages, formattedPage])
      showSuccess('Page Duplicated', `"${newPage.title}" has been created successfully!`)
    } catch (error) {
      console.error('Error duplicating page:', error)
      showError('Duplication Failed', 'Failed to duplicate page. Please try again.')
    }
  }
  
  const handleTogglePublish = async (pageId: string) => {
    const page = pages.find(p => p.id === pageId)
    if (!page) return

    try {
      const response = await fetch(`/api/page-builder/pages/${pageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isPublished: !page.isPublished
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update page')
      }

      const updatedPage = await response.json()
      setPages(pages.map(p =>
        p.id === pageId
          ? {
              ...p,
              isPublished: updatedPage.isPublished,
              updatedAt: new Date(updatedPage.updatedAt),
              publishedAt: updatedPage.publishedAt ? new Date(updatedPage.publishedAt) : undefined
            }
          : p
      ))
    } catch (error) {
      console.error('Error updating page:', error)
      alert('Failed to update page')
    }
  }

  const handleSavePage = async () => {
    if (!formData.title || !formData.slug) {
      showError('Validation Error', 'Title and slug are required')
      return
    }

    try {
      if (editingPage) {
        // Update existing page
        const response = await fetch(`/api/page-builder/pages/${editingPage.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            title: formData.title,
            slug: formData.slug,
            description: formData.description,
            metaTitle: formData.metaTitle,
            metaDescription: formData.metaDescription,
            isHomePage: formData.isHomePage
          })
        })

        if (!response.ok) {
          throw new Error('Failed to update page')
        }

        const updatedPage = await response.json()
        setPages(pages.map(p =>
          p.id === editingPage.id
            ? {
                ...p,
                title: updatedPage.title,
                slug: updatedPage.slug,
                description: updatedPage.description,
                metaTitle: updatedPage.metaTitle,
                metaDescription: updatedPage.metaDescription,
                isHomePage: updatedPage.isHomePage,
                lastModified: new Date(updatedPage.updatedAt),
                category: formData.category,
                type: updatedPage.isHomePage ? 'system' : 'custom'
              }
            : p.id === editingPage.id ? p : { ...p, isHomePage: false } // Unset other home pages
        ))
        showSuccess('Page Updated', `"${updatedPage.title}" has been updated successfully!`)
      } else {
        // Create new page
        const response = await fetch('/api/page-builder/pages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            title: formData.title,
            slug: formData.slug,
            description: formData.description,
            metaTitle: formData.metaTitle,
            metaDescription: formData.metaDescription,
            isHomePage: formData.isHomePage,
            layout: [],
            styles: {},
            seoSettings: {}
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to create page')
        }

        const newPage = await response.json()
        const formattedPage: ExtendedPage = {
          id: newPage.id,
          title: newPage.title,
          slug: newPage.slug,
          description: newPage.description,
          metaTitle: newPage.metaTitle,
          metaDescription: newPage.metaDescription,
          isPublished: newPage.isPublished,
          isHomePage: newPage.isHomePage,
          layout: newPage.layout || [],
          styles: newPage.styles || {},
          seoSettings: newPage.seoSettings || {},
          createdAt: new Date(newPage.createdAt),
          updatedAt: new Date(newPage.updatedAt),
          publishedAt: newPage.publishedAt ? new Date(newPage.publishedAt) : undefined,
          // Extended properties
          type: newPage.isHomePage ? 'system' : 'custom',
          category: formData.category,
          lastModified: new Date(newPage.updatedAt),
          modifiedBy: 'Current User',
          viewCount: 0,
          status: 'draft'
        }

        setPages([...pages, formattedPage])
        showSuccess('Page Created', `"${newPage.title}" has been created successfully!`)
      }

      // Close form
      setShowCreateForm(false)
      setEditingPage(null)
      setFormData({
        title: '',
        slug: '',
        description: '',
        metaTitle: '',
        metaDescription: '',
        category: 'general',
        isHomePage: false
      })

    } catch (error) {
      console.error('Error saving page:', error)
      showError('Save Failed', error instanceof Error ? error.message : 'Failed to save page')
    }
  }
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading pages...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-4xl mb-4">❌</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Pages</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }
  
  return (
    <div className="h-full flex flex-col">
      {/* Enhanced Header */}
      <div className="p-6 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Website Pages</h2>
            <p className="text-sm text-gray-600">Manage all your website pages and content</p>
          </div>
          <button
            onClick={handleCreatePage}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Page
          </button>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search pages..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'name' | 'modified' | 'status')}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="modified">Sort by Modified</option>
            <option value="name">Sort by Name</option>
            <option value="status">Sort by Status</option>
          </select>

          <div className="flex items-center space-x-1 bg-gray-200 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="Grid View"
            >
              <Squares2X2Icon className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="List View"
            >
              <ListBulletIcon className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-6 text-sm text-gray-600">
            <span>Total: {pages.length} pages</span>
            <span>Active: {pages.filter(p => p.status === 'active').length}</span>
            <span>Draft: {pages.filter(p => p.status === 'draft').length}</span>
            <span>Inactive: {pages.filter(p => p.status === 'inactive').length}</span>
          </div>

          {/* Source breakdown */}
          <div className="flex items-center space-x-3">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              📁 {pages.filter(p => p.source === 'filesystem').length} Live Pages
            </span>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              🏗️ {pages.filter(p => p.source === 'database').length} Builder Pages
            </span>
          </div>
        </div>
      </div>

      {/* Pages Content */}
      <div className="flex-1 overflow-y-auto p-6">
        {filteredAndSortedPages.length === 0 ? (
          <div className="text-center py-12">
            <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No pages found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedCategory !== 'all'
                ? 'Try adjusting your search or filter criteria'
                : 'Get started by creating your first page'
              }
            </p>
            {!searchTerm && selectedCategory === 'all' && (
              <button
                onClick={handleCreatePage}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create First Page
              </button>
            )}
          </div>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredAndSortedPages.map(page => (
              <div
                key={page.id}
                className={`bg-white border rounded-xl shadow-sm hover:shadow-md transition-shadow ${
                  currentPage?.id === page.id ? 'border-blue-500 ring-2 ring-blue-100' : 'border-gray-200'
                }`}
              >
                {/* Page Preview */}
                <div
                  className={`aspect-video rounded-t-xl relative overflow-hidden cursor-pointer transition-all ${
                    currentPage?.id === page.id
                      ? 'bg-gradient-to-br from-blue-50 to-blue-100 border-2 border-blue-300'
                      : 'bg-gradient-to-br from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-150'
                  }`}
                  onClick={() => handleSelectPage(page)}
                >
                  {/* Page thumbnail or default icon */}
                  {page.preview?.thumbnail ? (
                    <img
                      src={page.preview.thumbnail}
                      alt={`Preview of ${page.title}`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <DocumentTextIcon className={`h-12 w-12 ${
                        currentPage?.id === page.id ? 'text-blue-500' : 'text-gray-400'
                      }`} />
                    </div>
                  )}

                  {/* Source indicator */}
                  <div className="absolute top-2 left-2">
                    {page.source === 'filesystem' ? (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        📁 Live Page
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        🏗️ Builder
                      </span>
                    )}
                  </div>

                  {/* Selected indicator */}
                  {currentPage?.id === page.id && (
                    <div className="absolute inset-0 bg-blue-500/10 flex items-center justify-center">
                      <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg">
                        Currently Editing
                      </div>
                    </div>
                  )}

                  {/* Status Badge */}
                  <div className="absolute top-3 right-3">
                    {page.status === 'active' ? (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <CheckCircleIcon className="h-3 w-3 mr-1" />
                        Active
                      </span>
                    ) : page.status === 'inactive' ? (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <XCircleIcon className="h-3 w-3 mr-1" />
                        Inactive
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                        Draft
                      </span>
                    )}
                  </div>

                  {/* Type Badge */}
                  <div className="absolute top-3 left-3">
                    {page.isHomePage ? (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <HomeIcon className="h-3 w-3 mr-1" />
                        Home
                      </span>
                    ) : page.type === 'system' ? (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        System
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Custom
                      </span>
                    )}
                  </div>
                </div>

                {/* Page Info */}
                <div className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3
                      className="font-semibold text-gray-900 truncate cursor-pointer hover:text-blue-600"
                      onClick={() => handleSelectPage(page)}
                      title={page.title}
                    >
                      {page.title}
                    </h3>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full ml-2">
                      {page.category}
                    </span>
                  </div>

                  <p className="text-sm text-gray-600 mb-2">/{page.slug}</p>

                  {page.description && (
                    <p className="text-sm text-gray-500 mb-3 line-clamp-2">{page.description}</p>
                  )}

                  <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                    <div className="flex items-center space-x-3">
                      <span className="flex items-center">
                        <CalendarIcon className="h-3 w-3 mr-1" />
                        {page.lastModified.toLocaleDateString()}
                      </span>
                      {page.preview?.componentCount !== undefined ? (
                        <span className="flex items-center">
                          <Squares2X2Icon className="h-3 w-3 mr-1" />
                          {page.preview.componentCount} components
                        </span>
                      ) : page.viewCount !== undefined && (
                        <span className="flex items-center">
                          <ChartBarIcon className="h-3 w-3 mr-1" />
                          {page.viewCount} views
                        </span>
                      )}
                      {page.sourceFile && (
                        <span className="flex items-center text-green-600">
                          <DocumentTextIcon className="h-3 w-3 mr-1" />
                          Live File
                        </span>
                      )}
                    </div>
                    {page.modifiedBy && (
                      <span className="flex items-center">
                        <UserIcon className="h-3 w-3 mr-1" />
                        {page.modifiedBy}
                      </span>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    {page.source === 'filesystem' ? (
                      <button
                        onClick={() => handleImportPage(page)}
                        className="flex-1 flex items-center justify-center px-3 py-2 text-xs font-medium text-green-700 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
                        title="Import to Page Builder"
                      >
                        <CloudArrowUpIcon className="h-3 w-3 mr-1" />
                        Import to Builder
                      </button>
                    ) : (
                      <button
                        onClick={() => handleSelectPage(page)}
                        className={`flex-1 flex items-center justify-center px-3 py-2 text-xs font-medium rounded-lg transition-colors ${
                          currentPage?.id === page.id
                            ? 'text-blue-800 bg-blue-100 border border-blue-200'
                            : 'text-blue-700 bg-blue-50 hover:bg-blue-100'
                        }`}
                        title="Edit Page Content"
                      >
                        <PencilIcon className="h-3 w-3 mr-1" />
                        {currentPage?.id === page.id ? 'Editing' : 'Edit Content'}
                      </button>
                    )}

                    <button
                      onClick={() => handlePreviewPage(page)}
                      className="flex items-center justify-center px-3 py-2 text-xs font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                      title="Preview Page"
                    >
                      <EyeIcon className="h-3 w-3" />
                    </button>

                    <button
                      onClick={() => handleTogglePageStatus(page.id)}
                      className={`flex items-center justify-center px-3 py-2 text-xs font-medium rounded-lg ${
                        page.status === 'active'
                          ? 'text-red-700 bg-red-50 hover:bg-red-100'
                          : 'text-green-700 bg-green-50 hover:bg-green-100'
                      }`}
                      title={page.status === 'active' ? 'Deactivate Page' : 'Activate Page'}
                    >
                      <PowerIcon className="h-3 w-3" />
                    </button>

                    <div className="relative group">
                      <button className="flex items-center justify-center px-3 py-2 text-xs font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                        •••
                      </button>
                      <div className="absolute right-0 top-full mt-1 w-52 bg-white border border-gray-200 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                        <div className="py-1">
                          <button
                            onClick={() => handleEditPage(page)}
                            className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          >
                            <CogIcon className="h-4 w-4 mr-2" />
                            Edit Page Settings
                          </button>
                          <button
                            onClick={() => handleDuplicatePage(page)}
                            className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          >
                            <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
                            Duplicate Page
                          </button>
                          {!page.isHomePage && (
                            <button
                              onClick={() => handleSetAsHomePage(page.id)}
                              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                            >
                              <HomeIcon className="h-4 w-4 mr-2" />
                              Set as Home Page
                            </button>
                          )}
                          {!page.isHomePage && (
                            <>
                              <div className="border-t border-gray-100 my-1"></div>
                              <button
                                onClick={() => handleDeletePage(page.id)}
                                className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
                              >
                                <TrashIcon className="h-4 w-4 mr-2" />
                                Delete Page
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          // List View
          <div className="space-y-3">
            {filteredAndSortedPages.map(page => (
              <div
                key={page.id}
                className={`bg-white border rounded-lg p-4 hover:shadow-sm transition-all ${
                  currentPage?.id === page.id
                    ? 'border-blue-500 bg-blue-50 shadow-md ring-2 ring-blue-100'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 cursor-pointer" onClick={() => handleSelectPage(page)}>
                    <div className="flex items-center space-x-3">
                      <h3 className={`font-medium ${
                        currentPage?.id === page.id ? 'text-blue-900' : 'text-gray-900'
                      }`}>
                        {page.title}
                        {currentPage?.id === page.id && (
                          <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                            Currently Editing
                          </span>
                        )}
                      </h3>

                      {/* Status and Type Badges */}
                      <div className="flex items-center space-x-2">
                        {page.isHomePage && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <HomeIcon className="h-3 w-3 mr-1" />
                            Home
                          </span>
                        )}

                        {page.status === 'active' ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <CheckCircleIcon className="h-3 w-3 mr-1" />
                            Active
                          </span>
                        ) : page.status === 'inactive' ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <XCircleIcon className="h-3 w-3 mr-1" />
                            Inactive
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                            Draft
                          </span>
                        )}

                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                          {page.category}
                        </span>
                      </div>
                    </div>

                    <div className="mt-1 flex items-center space-x-4 text-sm text-gray-600">
                      <span>/{page.slug}</span>
                      <span>•</span>
                      <span>Modified {page.lastModified.toLocaleDateString()}</span>
                      {page.modifiedBy && (
                        <>
                          <span>•</span>
                          <span>by {page.modifiedBy}</span>
                        </>
                      )}
                      {page.viewCount !== undefined && (
                        <>
                          <span>•</span>
                          <span>{page.viewCount} views</span>
                        </>
                      )}
                    </div>

                    {page.description && (
                      <p className="text-sm text-gray-500 mt-1">{page.description}</p>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => handlePreviewPage(page)}
                      className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg"
                      title="Preview Page"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>

                    <button
                      onClick={() => handleEditPage(page)}
                      className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg"
                      title="Edit Page Settings"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>

                    <button
                      onClick={() => handleTogglePageStatus(page.id)}
                      className={`p-2 rounded-lg ${
                        page.status === 'active'
                          ? 'text-red-600 hover:text-red-800 hover:bg-red-50'
                          : 'text-green-600 hover:text-green-800 hover:bg-green-50'
                      }`}
                      title={page.status === 'active' ? 'Deactivate Page' : 'Activate Page'}
                    >
                      <PowerIcon className="h-4 w-4" />
                    </button>

                    <button
                      onClick={() => handleDuplicatePage(page)}
                      className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg"
                      title="Duplicate Page"
                    >
                      <DocumentDuplicateIcon className="h-4 w-4" />
                    </button>

                    {!page.isHomePage && (
                      <button
                        onClick={() => handleDeletePage(page.id)}
                        className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg"
                        title="Delete Page"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Enhanced Create/Edit form modal */}
      {(showCreateForm || editingPage) && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-900 bg-opacity-50 transition-opacity backdrop-blur-sm" />

            <div className="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-white px-6 pt-6 pb-4 sm:p-8 sm:pb-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">
                    {editingPage ? 'Edit Page Settings' : 'Create New Page'}
                  </h3>
                  <button
                    onClick={() => {
                      setShowCreateForm(false)
                      setEditingPage(null)
                      setFormData({
                        title: '',
                        slug: '',
                        description: '',
                        metaTitle: '',
                        metaDescription: '',
                        category: 'general',
                        isHomePage: false
                      })
                    }}
                    className="text-gray-400 hover:text-gray-600 p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    ×
                  </button>
                </div>

                <form className="space-y-6">
                  {/* Basic Information */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                      Basic Information
                    </h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Page Title *
                        </label>
                        <input
                          type="text"
                          value={formData.title}
                          onChange={(e) => {
                            const title = e.target.value
                            const slug = title.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-').trim()
                            setFormData({ ...formData, title, slug: editingPage ? formData.slug : slug })
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Enter page title"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          URL Slug *
                        </label>
                        <input
                          type="text"
                          value={formData.slug}
                          onChange={(e) => setFormData({ ...formData, slug: e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '-') })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="page-url-slug"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Description
                      </label>
                      <textarea
                        value={formData.description}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Brief description of the page"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Category
                      </label>
                      <select
                        value={formData.category}
                        onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        {categories.filter(cat => cat !== 'all').map(category => (
                          <option key={category} value={category}>
                            {category.charAt(0).toUpperCase() + category.slice(1)}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* SEO Settings */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                      SEO Settings
                    </h4>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Meta Title
                      </label>
                      <input
                        type="text"
                        value={formData.metaTitle}
                        onChange={(e) => setFormData({ ...formData, metaTitle: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="SEO title for search engines"
                        maxLength={60}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        {formData.metaTitle.length}/60 characters
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Meta Description
                      </label>
                      <textarea
                        value={formData.metaDescription}
                        onChange={(e) => setFormData({ ...formData, metaDescription: e.target.value })}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="SEO description for search engines"
                        maxLength={160}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        {formData.metaDescription.length}/160 characters
                      </p>
                    </div>
                  </div>

                  {/* Page Settings */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                      Page Settings
                    </h4>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isHomePage"
                        checked={formData.isHomePage}
                        onChange={(e) => setFormData({ ...formData, isHomePage: e.target.checked })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isHomePage" className="ml-2 block text-sm text-gray-900">
                        Set as home page
                      </label>
                    </div>

                    {formData.isHomePage && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <p className="text-sm text-blue-800">
                          <strong>Note:</strong> Setting this page as the home page will automatically unset any existing home page.
                        </p>
                      </div>
                    )}
                  </div>
                </form>
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:px-8 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-6 py-3 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm transition-colors"
                  onClick={handleSavePage}
                >
                  {editingPage ? 'Save Changes' : 'Create Page'}
                </button>

                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-6 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm transition-colors"
                  onClick={() => {
                    setShowCreateForm(false)
                    setEditingPage(null)
                    setFormData({
                      title: '',
                      slug: '',
                      description: '',
                      metaTitle: '',
                      metaDescription: '',
                      category: 'general',
                      isHomePage: false
                    })
                  }}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PageManager
