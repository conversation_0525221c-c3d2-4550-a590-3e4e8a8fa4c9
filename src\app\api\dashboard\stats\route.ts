import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin
} from '@/lib/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/dashboard/stats - Get dashboard statistics
export const GET = withError<PERSON><PERSON>ler(async (request: NextRequest) => {
  await requireAdmin(request)

  // Get current date for time-based queries
  const now = new Date()
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const startOfYear = new Date(now.getFullYear(), 0, 1)

  // Parallel queries for better performance
  const [
    totalClients,
    totalProjects,
    totalServices,
    totalTeamMembers,
    activeProjects,
    completedProjects,
    pendingInvoices,
    totalRevenue,
    monthlyRevenue,
    recentProjects,
    recentClients,
    projectsByStatus,
    invoicesByStatus,
    topServices,
    upcomingDeadlines,
  ] = await Promise.all([
    // Basic counts
    prisma.clients.count(),
    prisma.projects.count(),
    prisma.services.count({ where: { isactive: true } }),
    prisma.teammembers.count({ where: { isactive: true } }),
    
    // Project statistics
    prisma.projects.count({
      where: { status: { in: ['PLANNING', 'IN_PROGRESS'] } }
    }),
    prisma.projects.count({
      where: { status: 'COMPLETED' }
    }),
    
    // Financial statistics
    prisma.invoices.count({
      where: { status: { in: ['SENT', 'OVERDUE'] } }
    }),
    prisma.invoices.aggregate({
      where: { status: 'PAID' },
      _sum: { totalamount: true }
    }),
    prisma.invoices.aggregate({
      where: { 
        status: 'PAID',
        paidat: { gte: startOfMonth }
      },
      _sum: { totalamount: true }
    }),
    
    // Recent data
    prisma.projects.findMany({
      take: 5,
      orderBy: { createdat: 'desc' },
      include: {
        client: {
          select: {
            companyname: true,
          }
        }
      }
    }),
    prisma.clients.findMany({
      take: 5,
      orderBy: { createdat: 'desc' },
      select: {
        id: true,
        companyname: true,
        contactname: true,
        contactemail: true,
        createdat: true,
      }
    }),
    
    // Analytics data
    prisma.projects.groupBy({
      by: ['status'],
      _count: { status: true }
    }),
    prisma.invoices.groupBy({
      by: ['status'],
      _count: { status: true },
      _sum: { totalamount: true }
    }),
    
    // Top services by usage
    prisma.service.findMany({
      include: {
        _count: {
          select: {
            projects: true,
            orderDetails: true,
          }
        }
      },
      orderBy: {
        projects: {
          _count: 'desc'
        }
      },
      take: 5
    }),
    
    // Upcoming project deadlines
    prisma.projects.findMany({
      where: {
        projcompletiondate: {
          gte: now,
          lte: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // Next 30 days
        },
        status: { in: ['PLANNING', 'IN_PROGRESS'] }
      },
      include: {
        client: {
          select: {
            companyname: true,
          }
        }
      },
      orderBy: {
        projcompletiondate: 'asc'
      },
      take: 10
    }),
  ])

  // Calculate growth rates (simplified - you might want to implement proper month-over-month comparison)
  const projectGrowth = activeProjects > 0 ? ((activeProjects / totalProjects) * 100) : 0
  const clientGrowth = totalClients > 0 ? 5.2 : 0 // Placeholder - implement actual calculation

  const totalRevenueAmount = totalRevenue._sum.totalamount ? Number(totalRevenue._sum.totalamount) : 0
  const monthlyRevenueAmount = monthlyRevenue._sum.totalamount ? Number(monthlyRevenue._sum.totalamount) : 0
  const revenueGrowth = totalRevenueAmount > 0 && monthlyRevenueAmount > 0
    ? ((monthlyRevenueAmount / totalRevenueAmount) * 100)
    : 0

  // Format the response
  const stats = {
    overview: {
      totalClients,
      totalProjects,
      totalServices,
      totalTeamMembers,
      activeProjects,
      completedProjects,
      pendingInvoices,
      totalRevenue: totalRevenueAmount,
      monthlyRevenue: monthlyRevenueAmount,
    },
    growth: {
      projects: projectGrowth,
      clients: clientGrowth,
      revenue: revenueGrowth,
    },
    charts: {
      projectsByStatus: projectsByStatus.map(item => ({
        status: item.status,
        count: item._count.status,
      })),
      invoicesByStatus: invoicesByStatus.map(item => ({
        status: item.status,
        count: item._count.status,
        amount: item._sum.totalamount ? Number(item._sum.totalamount) : 0,
      })),
    },
    recent: {
      projects: recentProjects.map(project => ({
        id: project.id,
        name: project.name,
        status: project.status,
        clientname: project.client?.companyname,
        createdat: project.createdat,
      })),
      clients: recentClients,
    },
    insights: {
      topServices: topServices.map(service => ({
        id: service.id,
        name: service.name,
        projectCount: service._count.projects,
        orderCount: service._count.orderDetails,
        price: Number(service.price),
      })),
      upcomingDeadlines: upcomingDeadlines.map(project => ({
        id: project.id,
        name: project.name,
        clientname: project.client?.companyname,
        deadline: project.projcompletiondate,
        status: project.status,
      })),
    },
  }

  return successResponse(stats, 'Dashboard statistics retrieved successfully')
})
