'use client'

import React from 'react'
import { UserIcon } from '@heroicons/react/24/outline'

interface User {
  id: number
  email: string
  firstname?: string
  lastname?: string
  imageurl?: string
  role: 'ADMIN' | 'USER' | 'CLIENT'
  isactive: boolean
}

interface UserAvatarProps {
  user: User
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  showStatus?: boolean
  className?: string
}

export function UserAvatar({ 
  user, 
  size = 'md', 
  showStatus = false, 
  className = '' 
}: UserAvatarProps) {
  const sizeClasses = {
    xs: 'h-6 w-6',
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  }

  const iconSizeClasses = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
    xl: 'h-8 w-8'
  }

  const statusSizeClasses = {
    xs: 'h-1.5 w-1.5',
    sm: 'h-2 w-2',
    md: 'h-2.5 w-2.5',
    lg: 'h-3 w-3',
    xl: 'h-4 w-4'
  }

  const getInitials = () => {
    if (user.firstname && user.lastname) {
      return `${user.firstname.charAt(0)}${user.lastname.charAt(0)}`.toUpperCase()
    }
    if (user.firstname) {
      return user.firstname.charAt(0).toUpperCase()
    }
    if (user.lastname) {
      return user.lastname.charAt(0).toUpperCase()
    }
    return user.email.charAt(0).toUpperCase()
  }

  const getRoleColor = () => {
    switch (user.role) {
      case 'ADMIN':
        return 'bg-red-500'
      case 'CLIENT':
        return 'bg-green-500'
      case 'USER':
      default:
        return 'bg-blue-500'
    }
  }

  return (
    <div className={`relative inline-block ${className}`}>
      {user.imageurl ? (
        <img
          className={`${sizeClasses[size]} rounded-full object-cover border-2 border-gray-200`}
          src={user.imageurl}
          alt={`${user.firstname || ''} ${user.lastname || ''}`.trim() || user.email}
          onError={(e) => {
            // If image fails to load, hide it and show initials instead
            e.currentTarget.style.display = 'none'
            const fallback = e.currentTarget.nextElementSibling as HTMLElement
            if (fallback) {
              fallback.style.display = 'flex'
            }
          }}
        />
      ) : null}
      
      {/* Fallback initials avatar */}
      <div
        className={`${sizeClasses[size]} ${getRoleColor()} rounded-full flex items-center justify-center text-white font-medium border-2 border-gray-200 ${
          user.imageurl ? 'hidden' : 'flex'
        }`}
        style={{ display: user.imageurl ? 'none' : 'flex' }}
      >
        <span className={`${
          size === 'xs' ? 'text-xs' :
          size === 'sm' ? 'text-xs' :
          size === 'md' ? 'text-sm' :
          size === 'lg' ? 'text-base' :
          'text-lg'
        } font-semibold`}>
          {getInitials()}
        </span>
      </div>

      {/* Status indicator */}
      {showStatus && (
        <div className={`absolute -bottom-0 -right-0 ${statusSizeClasses[size]} rounded-full border-2 border-white ${
          user.isactive ? 'bg-green-400' : 'bg-gray-400'
        }`} />
      )}
    </div>
  )
}

// Alternative component for when we just need a simple user icon
export function UserIconAvatar({ 
  size = 'md', 
  className = '' 
}: { 
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  className?: string 
}) {
  const sizeClasses = {
    xs: 'h-6 w-6',
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  }

  const iconSizeClasses = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
    xl: 'h-8 w-8'
  }

  return (
    <div className={`${sizeClasses[size]} bg-gray-200 rounded-full flex items-center justify-center ${className}`}>
      <UserIcon className={`${iconSizeClasses[size]} text-gray-500`} />
    </div>
  )
}
