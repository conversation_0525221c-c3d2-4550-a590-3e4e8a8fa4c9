import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/page-builder/pages/[id] - Get a specific page
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const pageId = BigInt(id)
    
    const page = await prisma.pages.findUnique({
      where: { id: pageId },
      include: {
        creator: {
          select: {
            id: true,
            firstname: true,
            lastname: true,
            email: true
          }
        },
        components: {
          orderBy: {
            order: 'asc'
          }
        },
        versions: {
          orderBy: {
            version: 'desc'
          },
          take: 10,
          include: {
            creator: {
              select: {
                id: true,
                firstname: true,
                lastname: true,
                email: true
              }
            }
          }
        }
      }
    })
    
    if (!page) {
      return NextResponse.json({ error: 'Page not found' }, { status: 404 })
    }
    
    // Convert components to the format expected by the page builder
    const formattedComponents = page.components.map(component => ({
      id: component.componentId,
      type: component.type,
      name: component.name,
      content: component.content,
      styles: component.styles,
      position: component.position,
      isVisible: component.isVisible,
      isLocked: false,
      order: component.order
    }))

    // Convert BigInt to string for JSON serialization
    const serializedPage = {
      ...page,
      id: page.id.toString(),
      createdBy: page.createdBy?.toString(),
      creator: page.creator ? {
        ...page.creator,
        id: page.creator.id.toString()
      } : null,
      // Use the formatted components as layout
      layout: formattedComponents,
      components: page.components.map(component => ({
        ...component,
        id: component.id.toString(),
        pageId: component.pageId.toString()
      })),
      versions: page.versions.map(version => ({
        ...version,
        id: version.id.toString(),
        pageId: version.pageId.toString(),
        createdBy: version.createdBy?.toString(),
        creator: version.creator ? {
          ...version.creator,
          id: version.creator.id.toString()
        } : null
      }))
    }

    return NextResponse.json(serializedPage)
  } catch (error) {
    console.error('Error fetching page:', error)
    return NextResponse.json(
      { error: 'Failed to fetch page' },
      { status: 500 }
    )
  }
}

// PUT /api/page-builder/pages/[id] - Update a page
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const pageId = BigInt(id)
    const body = await request.json()
    
    const {
      title,
      slug,
      description,
      metaTitle,
      metaDescription,
      isPublished,
      isHomePage,
      layout,
      styles,
      seoSettings,
      createVersion = false
    } = body
    
    // Check if page exists
    const existingPage = await prisma.pages.findUnique({
      where: { id: pageId }
    })
    
    if (!existingPage) {
      return NextResponse.json({ error: 'Page not found' }, { status: 404 })
    }
    
    // Check if slug is unique (if changed)
    if (slug && slug !== existingPage.slug) {
      const slugExists = await prisma.pages.findUnique({
        where: { slug }
      })
      
      if (slugExists) {
        return NextResponse.json(
          { error: 'A page with this slug already exists' },
          { status: 400 }
        )
      }
    }
    
    // If this is set as home page, unset other home pages
    if (isHomePage && !existingPage.isHomePage) {
      await prisma.pages.updateMany({
        where: { 
          isHomePage: true,
          id: { not: pageId }
        },
        data: { isHomePage: false }
      })
    }
    
    // If layout is provided, update the components
    if (layout && Array.isArray(layout)) {
      // Delete existing components
      await prisma.pageComponents.deleteMany({
        where: { pageId }
      })

      // Create new components from layout
      for (let i = 0; i < layout.length; i++) {
        const component = layout[i]
        await prisma.pageComponents.create({
          data: {
            pageId,
            componentId: component.id || `component-${Date.now()}-${i}`,
            type: component.type,
            name: component.name || `Component ${i + 1}`,
            content: component.content || {},
            styles: component.styles || {},
            position: component.position || { x: 0, y: 0, width: 100, height: 100 },
            order: component.order || i + 1,
            isVisible: component.isVisible !== false
          }
        })
      }
    }

    // Update the page
    const updatedPage = await prisma.pages.update({
      where: { id: pageId },
      data: {
        ...(title && { title }),
        ...(slug && { slug }),
        ...(description !== undefined && { description }),
        ...(metaTitle !== undefined && { metaTitle }),
        ...(metaDescription !== undefined && { metaDescription }),
        ...(isPublished !== undefined && { isPublished }),
        ...(isHomePage !== undefined && { isHomePage }),
        ...(layout && { layout: { components: layout.map(c => c.id), version: '1.0.0' } }),
        ...(styles && { styles }),
        ...(seoSettings && { seoSettings }),
        ...(isPublished && !existingPage.isPublished && { publishedAt: new Date() })
      },
      include: {
        creator: {
          select: {
            id: true,
            firstname: true,
            lastname: true,
            email: true
          }
        },
        components: {
          orderBy: {
            order: 'asc'
          }
        }
      }
    })
    
    // Create a new version if requested
    if (createVersion && layout) {
      const latestVersion = await prisma.pageVersions.findFirst({
        where: { pageId },
        orderBy: { version: 'desc' }
      })
      
      const nextVersion = (latestVersion?.version || 0) + 1
      
      await prisma.pageVersions.create({
        data: {
          pageId,
          version: nextVersion,
          title: title || existingPage.title,
          description: `Version ${nextVersion}`,
          layout,
          createdBy: BigInt(session.user.id)
        }
      })
    }
    
    // Convert components to the format expected by the page builder
    const formattedComponents = updatedPage.components.map(component => ({
      id: component.componentId,
      type: component.type,
      name: component.name,
      content: component.content,
      styles: component.styles,
      position: component.position,
      isVisible: component.isVisible,
      isLocked: false,
      order: component.order
    }))

    // Convert BigInt to string for JSON serialization
    const serializedPage = {
      ...updatedPage,
      id: updatedPage.id.toString(),
      createdBy: updatedPage.createdBy?.toString(),
      creator: updatedPage.creator ? {
        ...updatedPage.creator,
        id: updatedPage.creator.id.toString()
      } : null,
      // Use the formatted components as layout
      layout: formattedComponents,
      components: updatedPage.components.map(component => ({
        ...component,
        id: component.id.toString(),
        pageId: component.pageId.toString()
      }))
    }

    return NextResponse.json(serializedPage)
  } catch (error) {
    console.error('Error updating page:', error)
    return NextResponse.json(
      { error: 'Failed to update page' },
      { status: 500 }
    )
  }
}

// DELETE /api/page-builder/pages/[id] - Delete a page
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const pageId = BigInt(id)
    
    // Check if page exists
    const page = await prisma.pages.findUnique({
      where: { id: pageId }
    })
    
    if (!page) {
      return NextResponse.json({ error: 'Page not found' }, { status: 404 })
    }
    
    // Prevent deletion of home page
    if (page.isHomePage) {
      return NextResponse.json(
        { error: 'Cannot delete the home page' },
        { status: 400 }
      )
    }
    
    // Delete the page (components and versions will be deleted due to cascade)
    await prisma.pages.delete({
      where: { id: pageId }
    })
    
    return NextResponse.json({ message: 'Page deleted successfully' })
  } catch (error) {
    console.error('Error deleting page:', error)
    return NextResponse.json(
      { error: 'Failed to delete page' },
      { status: 500 }
    )
  }
}
