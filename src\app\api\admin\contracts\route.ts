import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/contracts - Get all contracts with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['title', 'description', 'contractNumber'])
  
  // Add status filter if provided
  if (filter) {
    searchQuery.status = filter
  }
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get contracts with pagination
  const [contracts, total] = await Promise.all([
    prisma.contracts.findMany({
      where: searchQuery,
      include: {
        client: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        project: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        order: {
          select: {
            id: true,
            orderNumber: true,
            status: true
          }
        },
        invoices: {
          select: {
            id: true,
            invoiceNumber: true,
            status: true,
            totalamount: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.contracts.count({ where: searchQuery })
  ])

  return paginatedResponse(contracts, page, limit, total)
})

// POST /api/admin/contracts - Create a new contract
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.contract.create)
  const data = await validate(request)

  // Check if client exists
  if (data.clientid) {
    const client = await prisma.clients.findUnique({
      where: { id: data.clientid },
    })

    if (!client) {
      throw new Error('Client not found')
    }
  }

  // Check if project exists
  if (data.projectid) {
    const project = await prisma.projects.findUnique({
      where: { id: data.projectid },
    })

    if (!project) {
      throw new Error('Project not found')
    }
  }

  // Check if order exists
  if (data.orderid) {
    const order = await prisma.orders.findUnique({
      where: { id: data.orderid },
    })

    if (!order) {
      throw new Error('Order not found')
    }
  }

  const contract = await prisma.contracts.create({
    data,
    include: {
      client: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
          contactemail: true
        }
      },
      project: {
        select: {
          id: true,
          name: true,
          status: true
        }
      },
      order: {
        select: {
          id: true,
          orderNumber: true,
          status: true
        }
      }
    }
  })

  return successResponse(contract, 'Contract created successfully', 201)
})
