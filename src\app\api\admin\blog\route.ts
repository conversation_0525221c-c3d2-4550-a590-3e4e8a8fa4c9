import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON>andler, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  requireAdmin,
  generateSlug
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/blog - Get all blog posts with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['title', 'content', 'excerpt', 'categories', 'tags'])
  
  // Build sort query with field name mapping
  const fieldMapping: { [key: string]: string } = {
    'updatedAt': 'updatedat',
    'createdAt': 'createdat',
    'publishedAt': 'publishedat',
    'isPublished': 'ispublished',
    'featuredImageUrl': 'featuredimageurl',
    'authorId': 'authorid'
  }
  
  const mappedSortBy = fieldMapping[sortBy] || sortBy
  const sortQuery = buildSortQuery(mappedSortBy, sortOrder)

  // Get blog posts with pagination
  const [blogPosts, total] = await Promise.all([
    prisma.blogposts.findMany({
      where: searchQuery,
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.blogposts.count({ where: searchQuery })
  ])

  // Transform blog posts to frontend format
  const transformedBlogPosts = blogPosts.map(post => {
    const transformed = transformFromDbFields.blogPost(post)
    // Ensure ID is a string to avoid BigInt serialization issues
    return {
      ...transformed,
      id: String(transformed.id)
    }
  })

  return paginatedResponse(transformedBlogPosts, total, page, limit, 'Blog posts retrieved successfully')
})

// POST /api/admin/blog - Create a new blog post
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.blog.create)
  const data = await validate(request)

  // Generate slug if not provided
  if (!data.slug) {
    data.slug = generateSlug(data.title)
  }

  // Check if slug already exists
  const existingPost = await prisma.blogposts.findUnique({
    where: { slug: data.slug },
  })

  if (existingPost) {
    // Generate a unique slug
    let counter = 1
    let newSlug = `${data.slug}-${counter}`
    
    while (await prisma.blogposts.findUnique({ where: { slug: newSlug } })) {
      counter++
      newSlug = `${data.slug}-${counter}`
    }
    
    data.slug = newSlug
  }

  // Transform data to database format
  const dbData = transformToDbFields.blogPost(data)

  const blogPost = await prisma.blogposts.create({
    data: dbData,
  })

  const transformedBlogPost = transformFromDbFields.blogPost(blogPost)
  return successResponse({
    ...transformedBlogPost,
    id: String(transformedBlogPost.id)
  }, 'Blog post created successfully', 201)
})

// PUT /api/admin/blog - Bulk update blog posts
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid blog post IDs provided')
  }

  // Transform data to database format
  const dbData = transformToDbFields.blogPost(data)

  const updatedBlogPosts = await prisma.blogposts.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: {
      ...dbData,
      updatedat: new Date(),
    },
  })

  return successResponse(
    { count: updatedBlogPosts.count },
    `${updatedBlogPosts.count} blog posts updated successfully`
  )
})

// DELETE /api/admin/blog - Bulk delete blog posts
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid blog post IDs provided')
  }

  const deletedBlogPosts = await prisma.blogposts.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedBlogPosts.count },
    `${deletedBlogPosts.count} blog posts deleted successfully`
  )
})
