'use client'

import React from 'react'
import { Component } from '@/lib/page-builder/store'
import { ComponentConfig } from '@/lib/page-builder/component-registry'

interface GridComponentProps {
  component: Component
  isEditing: boolean
  isSelected: boolean
  isHovered: boolean
  config: ComponentConfig
  children?: React.ReactNode
}

const GridComponent: React.FC<GridComponentProps> = ({
  component,
  isEditing,
  isSelected,
  isHovered,
  config,
  children
}) => {
  const { 
    columns = 2,
    rows = 2,
    gap = 16
  } = component.content
  
  const gridStyles = {
    display: 'grid',
    gridTemplateColumns: component.styles.gridTemplateColumns || `repeat(${columns}, 1fr)`,
    gridTemplateRows: component.styles.gridTemplateRows || `repeat(${rows}, 1fr)`,
    gap: component.styles.gap || `${gap}px`,
    padding: component.styles.padding || '20px',
    backgroundColor: component.styles.backgroundColor || 'transparent',
    borderRadius: component.styles.borderRadius || '0px',
    border: component.styles.border || (isEditing ? '1px dashed #e5e7eb' : 'none'),
    width: '100%',
    height: '100%',
    boxShadow: component.styles.boxShadow || 'none'
  }
  
  return (
    <div
      style={gridStyles}
      className={`
        ${isEditing ? 'relative' : ''}
      `}
    >
      {children || (isEditing && (
        <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-sm pointer-events-none">
          Grid Container ({columns}x{rows})
        </div>
      ))}
    </div>
  )
}

export default GridComponent
