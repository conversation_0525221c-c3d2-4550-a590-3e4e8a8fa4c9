'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';
import {
  UserGroupIcon,
  BriefcaseIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';

interface DashboardStats {
  overview: {
    totalClients: number
    totalProjects: number
    totalServices: number
    totalTeamMembers: number
    activeProjects: number
    completedProjects: number
    pendingInvoices: number
    totalRevenue: number
    monthlyRevenue: number
  }
  growth: {
    projects: number
    clients: number
    revenue: number
  }
  charts: {
    projectsByStatus: Array<{ status: string; count: number }>
    invoicesByStatus: Array<{ status: string; count: number; amount: number }>
  }
  recent: {
    projects: Array<any>
    clients: Array<any>
  }
  insights: {
    topServices: Array<any>
    upcomingDeadlines: Array<any>
  }
}

const recentActivity = [
  { id: 1, type: 'project', title: 'New project created: E-commerce Platform', time: '2 hours ago', status: 'success' },
  { id: 2, type: 'client', title: 'New client registered: TechCorp Inc.', time: '4 hours ago', status: 'info' },
  { id: 3, type: 'invoice', title: 'Invoice #INV-2024-001 marked as paid', time: '6 hours ago', status: 'success' },
  { id: 4, type: 'contact', title: 'New contact form submission', time: '8 hours ago', status: 'warning' },
  { id: 5, type: 'application', title: 'New job application received', time: '1 day ago', status: 'info' },
];

const StatCard = ({ title, value, subtitle, icon: Icon, growth, color = 'blue' }: any) => {
  const isPositive = growth > 0;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white overflow-hidden shadow rounded-lg"
    >
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className={`h-6 w-6 text-${color}-600`} />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd>
                <div className="text-lg font-medium text-gray-900">{value}</div>
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 px-5 py-3">
        <div className="text-sm">
          <div className="flex items-center justify-between">
            <span className="text-gray-500">{subtitle}</span>
            <div className={`flex items-center ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {isPositive ? (
                <ArrowUpIcon className="h-4 w-4 mr-1" />
              ) : (
                <ArrowDownIcon className="h-4 w-4 mr-1" />
              )}
              <span className="font-medium">{Math.abs(growth)}%</span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const ActivityItem = ({ activity }: any) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'info': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return CheckCircleIcon;
      case 'warning': return ExclamationTriangleIcon;
      case 'info': return ClockIcon;
      default: return ClockIcon;
    }
  };

  const StatusIcon = getStatusIcon(activity.status);

  return (
    <div className="flex items-center space-x-3 py-3">
      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${getStatusColor(activity.status)}`}>
        <StatusIcon className="h-4 w-4" />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">{activity.title}</p>
        <p className="text-sm text-gray-500">{activity.time}</p>
      </div>
    </div>
  );
};

export default function AdminDashboard() {
  const { data: session } = useSession();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    const fetchDashboardStats = async () => {
      if (!session?.user || session.user.role !== 'ADMIN') {
        setError('Admin access required');
        setLoading(false);
        return;
      }

      try {
        const response = await fetch('/api/dashboard/stats');
        if (!response.ok) {
          throw new Error('Failed to fetch dashboard stats');
        }

        const result = await response.json();
        if (result.success) {
          setDashboardStats(result.data);
        } else {
          throw new Error(result.error || 'Failed to load dashboard data');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardStats();
  }, [session]);

  if (loading) {
    return (
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-white p-6 rounded-lg shadow">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading dashboard</h3>
                <p className="mt-1 text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Provide default values if dashboardStats is null or incomplete
  const safeStats = dashboardStats || {
    overview: {
      totalClients: 0,
      totalProjects: 0,
      totalServices: 0,
      totalTeamMembers: 0,
      activeProjects: 0,
      completedProjects: 0,
      pendingInvoices: 0,
      totalRevenue: 0,
      monthlyRevenue: 0,
    },
    growth: {
      projects: 0,
      clients: 0,
      revenue: 0,
    },
    charts: {
      projectsByStatus: [],
      invoicesByStatus: [],
    },
    recent: {
      projects: [],
      clients: [],
    },
    insights: {
      topServices: [],
      upcomingDeadlines: [],
    },
  }

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Dashboard
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Welcome back! Here's what's happening with your business today.
              <span className="ml-2 text-blue-600">
                {currentTime.toLocaleString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="mt-8">
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <StatCard
              title="Total Projects"
              value={safeStats.overview.totalProjects}
              subtitle={`${safeStats.overview.activeProjects} active, ${safeStats.overview.completedProjects} completed`}
              icon={BriefcaseIcon}
              growth={safeStats.growth.projects}
              color="blue"
            />
            <StatCard
              title="Total Clients"
              value={safeStats.overview.totalClients}
              subtitle={`${safeStats.overview.totalServices} services available`}
              icon={UserGroupIcon}
              growth={safeStats.growth.clients}
              color="green"
            />
            <StatCard
              title="Pending Invoices"
              value={safeStats.overview.pendingInvoices}
              subtitle={`Revenue tracking`}
              icon={DocumentTextIcon}
              growth={2.1}
              color="yellow"
            />
            <StatCard
              title="Total Revenue"
              value={`$${(safeStats.overview.totalRevenue / 1000).toFixed(0)}K`}
              subtitle={`$${(safeStats.overview.monthlyRevenue / 1000).toFixed(0)}K this month`}
              icon={CurrencyDollarIcon}
              growth={safeStats.growth.revenue}
              color="purple"
            />
          </div>
        </div>

        {/* Secondary Stats */}
        <div className="mt-8">
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <StatCard
              title="Team Members"
              value={safeStats.overview.totalTeamMembers}
              subtitle="Active team members"
              icon={UserGroupIcon}
              growth={5.2}
              color="indigo"
            />
            <StatCard
              title="Services"
              value={safeStats.overview.totalServices}
              subtitle="Available services"
              icon={BriefcaseIcon}
              growth={8.7}
              color="pink"
            />
            <StatCard
              title="Recent Projects"
              value={safeStats.recent.projects.length}
              subtitle="Latest projects"
              icon={ChartBarIcon}
              growth={12.1}
              color="green"
            />
            <StatCard
              title="Recent Clients"
              value={safeStats.recent.clients.length}
              subtitle="New clients"
              icon={DocumentTextIcon}
              growth={3.4}
              color="blue"
            />
          </div>
        </div>

        {/* Recent Activity */}
        <div className="mt-8">
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
              <div className="flow-root">
                <ul className="-mb-8">
                  {recentActivity.map((activity, index) => (
                    <li key={activity.id}>
                      <div className="relative pb-8">
                        {index !== recentActivity.length - 1 && (
                          <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" />
                        )}
                        <ActivityItem activity={activity} />
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <button className="relative group bg-gray-50 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg hover:bg-gray-100 transition-colors">
                  <div>
                    <span className="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                      <BriefcaseIcon className="h-6 w-6" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium">
                      <span className="absolute inset-0" />
                      Create Project
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      Start a new project for a client
                    </p>
                  </div>
                </button>

                <button className="relative group bg-gray-50 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg hover:bg-gray-100 transition-colors">
                  <div>
                    <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                      <UserGroupIcon className="h-6 w-6" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium">
                      <span className="absolute inset-0" />
                      Add Client
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      Register a new client
                    </p>
                  </div>
                </button>

                <button className="relative group bg-gray-50 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg hover:bg-gray-100 transition-colors">
                  <div>
                    <span className="rounded-lg inline-flex p-3 bg-yellow-50 text-yellow-700 ring-4 ring-white">
                      <DocumentTextIcon className="h-6 w-6" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium">
                      <span className="absolute inset-0" />
                      Create Invoice
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      Generate a new invoice
                    </p>
                  </div>
                </button>

                <button className="relative group bg-gray-50 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg hover:bg-gray-100 transition-colors">
                  <div>
                    <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                      <DocumentTextIcon className="h-6 w-6" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium">
                      <span className="absolute inset-0" />
                      Write Blog Post
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      Create a new blog article
                    </p>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
