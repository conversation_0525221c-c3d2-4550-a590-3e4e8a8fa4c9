'use client'

import { useState, useEffect } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'

interface Project {
  id: string
  name: string
  description: string
  projgoals?: string
  projmanager?: string
  clientid?: string
  orderid: string
  imageurl?: string
  projecturl?: string
  githuburl?: string
  tags?: string
  projstartdate?: string
  projcompletiondate?: string
  estimatecost?: number
  estimatetime?: string
  estimateeffort?: string
  status?: string
  isfeatured?: boolean
  ispublic?: boolean
  displayorder: number
}

interface ProjectModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
  title: string
  project?: Project | null
}

export default function ProjectModal({ isOpen, onClose, onSubmit, title, project }: ProjectModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    projgoals: '',
    projmanager: '',
    clientid: '',
    orderid: '',
    imageurl: '',
    projecturl: '',
    githuburl: '',
    tags: '',
    projstartdate: '',
    projcompletiondate: '',
    estimatecost: '',
    estimatetime: '',
    estimateeffort: '',
    status: 'PLANNING',
    isfeatured: false,
    ispublic: true,
    displayorder: 0,
  })

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Reset form when modal opens/closes or project changes
  useEffect(() => {
    if (isOpen) {
      if (project) {
        setFormData({
          name: project.name || '',
          description: project.description || '',
          projgoals: project.projgoals || '',
          projmanager: project.projmanager || '',
          clientid: project.clientid || '',
          orderid: project.orderid || '',
          imageurl: project.imageurl || '',
          projecturl: project.projecturl || '',
          githuburl: project.githuburl || '',
          tags: project.tags || '',
          projstartdate: (() => {
            if (!project.projstartdate) return ''
            try {
              const date = project.projstartdate instanceof Date
                ? project.projstartdate
                : new Date(project.projstartdate)
              return isNaN(date.getTime()) ? '' : date.toISOString().split('T')[0]
            } catch {
              return ''
            }
          })(),
          projcompletiondate: (() => {
            if (!project.projcompletiondate) return ''
            try {
              const date = project.projcompletiondate instanceof Date
                ? project.projcompletiondate
                : new Date(project.projcompletiondate)
              return isNaN(date.getTime()) ? '' : date.toISOString().split('T')[0]
            } catch {
              return ''
            }
          })(),
          estimatecost: project.estimatecost?.toString() || '',
          estimatetime: project.estimatetime || '',
          estimateeffort: project.estimateeffort || '',
          status: project.status || 'PLANNING',
          isfeatured: project.isfeatured || false,
          ispublic: project.ispublic || true,
          displayorder: project.displayorder || 0,
        })
      } else {
        setFormData({
          name: '',
          description: '',
          projgoals: '',
          projmanager: '',
          clientid: '',
          orderid: '',
          imageurl: '',
          projecturl: '',
          githuburl: '',
          tags: '',
          projstartdate: '',
          projcompletiondate: '',
          estimatecost: '',
          estimatetime: '',
          estimateeffort: '',
          status: 'PLANNING',
          isfeatured: false,
          ispublic: true,
          displayorder: 0,
        })
      }
      setError(null)
    }
  }, [isOpen, project])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // Convert form data to proper types
      const submitData = {
        ...formData,
        estimatecost: formData.estimatecost ? parseFloat(formData.estimatecost) : null,
        displayorder: parseInt(formData.displayorder.toString()) || 0,
        projstartdate: (() => {
          if (!formData.projstartdate) return null
          try {
            const date = new Date(formData.projstartdate)
            return isNaN(date.getTime()) ? null : date.toISOString()
          } catch {
            return null
          }
        })(),
        projcompletiondate: (() => {
          if (!formData.projcompletiondate) return null
          try {
            const date = new Date(formData.projcompletiondate)
            return isNaN(date.getTime()) ? null : date.toISOString()
          } catch {
            return null
          }
        })(),
      }

      await onSubmit(submitData)
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        <div className="relative w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium leading-6 text-gray-900">
              {title}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

                {error && (
                  <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    {error}
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        Project Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        required
                        value={formData.name}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter project name"
                      />
                    </div>

                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                        Status
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={formData.status}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="PLANNING">Planning</option>
                        <option value="IN_PROGRESS">In Progress</option>
                        <option value="COMPLETED">Completed</option>
                        <option value="ON_HOLD">On Hold</option>
                        <option value="CANCELLED">Cancelled</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                      Description *
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      required
                      rows={3}
                      value={formData.description}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter project description"
                    />
                  </div>

                  <div>
                    <label htmlFor="projgoals" className="block text-sm font-medium text-gray-700 mb-2">
                      Project Goals
                    </label>
                    <textarea
                      id="projgoals"
                      name="projgoals"
                      rows={2}
                      value={formData.projgoals}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter project goals"
                    />
                  </div>

                  {/* Project Details */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label htmlFor="estimatecost" className="block text-sm font-medium text-gray-700 mb-2">
                        Estimated Cost
                      </label>
                      <input
                        type="number"
                        id="estimatecost"
                        name="estimatecost"
                        step="0.01"
                        value={formData.estimatecost}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="0.00"
                      />
                    </div>

                    <div>
                      <label htmlFor="estimatetime" className="block text-sm font-medium text-gray-700 mb-2">
                        Estimated Time
                      </label>
                      <input
                        type="text"
                        id="estimatetime"
                        name="estimatetime"
                        value={formData.estimatetime}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="e.g., 3 months"
                      />
                    </div>

                    <div>
                      <label htmlFor="estimateeffort" className="block text-sm font-medium text-gray-700 mb-2">
                        Estimated Effort
                      </label>
                      <input
                        type="text"
                        id="estimateeffort"
                        name="estimateeffort"
                        value={formData.estimateeffort}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="e.g., 200 hours"
                      />
                    </div>
                  </div>

                  {/* Timeline */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="projstartdate" className="block text-sm font-medium text-gray-700 mb-2">
                        Start Date
                      </label>
                      <input
                        type="date"
                        id="projstartdate"
                        name="projstartdate"
                        value={formData.projstartdate}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    <div>
                      <label htmlFor="projcompletiondate" className="block text-sm font-medium text-gray-700 mb-2">
                        Completion Date
                      </label>
                      <input
                        type="date"
                        id="projcompletiondate"
                        name="projcompletiondate"
                        value={formData.projcompletiondate}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  {/* URLs and Media */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label htmlFor="imageurl" className="block text-sm font-medium text-gray-700 mb-2">
                        Image URL
                      </label>
                      <input
                        type="url"
                        id="imageurl"
                        name="imageurl"
                        value={formData.imageurl}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>

                    <div>
                      <label htmlFor="projecturl" className="block text-sm font-medium text-gray-700 mb-2">
                        Project URL
                      </label>
                      <input
                        type="url"
                        id="projecturl"
                        name="projecturl"
                        value={formData.projecturl}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="https://project.com"
                      />
                    </div>

                    <div>
                      <label htmlFor="githuburl" className="block text-sm font-medium text-gray-700 mb-2">
                        GitHub URL
                      </label>
                      <input
                        type="url"
                        id="githuburl"
                        name="githuburl"
                        value={formData.githuburl}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="https://github.com/user/repo"
                      />
                    </div>
                  </div>

                  {/* Tags and IDs */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
                        Tags
                      </label>
                      <input
                        type="text"
                        id="tags"
                        name="tags"
                        value={formData.tags}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="React, TypeScript, Node.js"
                      />
                    </div>

                    <div>
                      <label htmlFor="orderid" className="block text-sm font-medium text-gray-700 mb-2">
                        Order ID *
                      </label>
                      <input
                        type="text"
                        id="orderid"
                        name="orderid"
                        required
                        value={formData.orderid}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter order ID"
                      />
                    </div>

                    <div>
                      <label htmlFor="displayorder" className="block text-sm font-medium text-gray-700 mb-2">
                        Display Order
                      </label>
                      <input
                        type="number"
                        id="displayorder"
                        name="displayorder"
                        value={formData.displayorder}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="0"
                      />
                    </div>
                  </div>

                  {/* Flags */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="ispublic"
                        name="ispublic"
                        checked={formData.ispublic}
                        onChange={handleChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="ispublic" className="ml-2 block text-sm text-gray-700">
                        Public Project
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isfeatured"
                        name="isfeatured"
                        checked={formData.isfeatured}
                        onChange={handleChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isfeatured" className="ml-2 block text-sm text-gray-700">
                        Featured Project
                      </label>
                    </div>
                  </div>

                  {/* Form Actions */}
                  <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? 'Saving...' : project ? 'Update Project' : 'Create Project'}
                    </button>
                  </div>
                </form>
        </div>
      </div>
    </div>
  )
}
