'use client'

import React, { useState } from 'react'
import { usePageBuilderStore } from '@/lib/page-builder/store'
import { getComponentsByCategory, createDefaultComponent, extendedComponentRegistry } from '@/lib/page-builder/component-registry'
import { MagnifyingGlassIcon, Squares2X2Icon, ListBulletIcon, SparklesIcon, FunnelIcon } from '@heroicons/react/24/outline'

interface ComponentLibraryProps {
  className?: string
}

const ComponentLibrary: React.FC<ComponentLibraryProps> = ({ className = '' }) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showAdvanced, setShowAdvanced] = useState(false)

  const { addComponent } = usePageBuilderStore()

  // Use extended component registry for more components
  const componentsByCategory = Object.values(extendedComponentRegistry).reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = []
    }
    acc[config.category].push(config)
    return acc
  }, {} as Record<string, typeof extendedComponentRegistry[string][]>)

  const categories = ['all', ...Object.keys(componentsByCategory).sort()]
  
  // Filter components based on search and category
  const filteredComponents = Object.entries(componentsByCategory).reduce((acc, [category, components]) => {
    if (selectedCategory !== 'all' && category !== selectedCategory) {
      return acc
    }
    
    const filtered = components.filter(component =>
      component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      component.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
    
    if (filtered.length > 0) {
      acc[category] = filtered
    }
    
    return acc
  }, {} as Record<string, typeof componentsByCategory[string]>)
  
  // Handle component drag start
  const handleDragStart = (e: React.DragEvent, componentType: string) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'component',
      componentType
    }))
    e.dataTransfer.effectAllowed = 'copy'
  }
  
  // Handle component click (add to center of canvas)
  const handleComponentClick = (componentType: string) => {
    const component = createDefaultComponent(componentType as any, { x: 100, y: 100 })
    addComponent(component)
  }
  
  return (
    <div className={`flex flex-col h-full bg-white border-r border-gray-200 ${className}`}>
      {/* Enhanced Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Components</h2>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={`p-2 rounded-lg transition-colors ${
              showAdvanced
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            title="Toggle Advanced Components"
          >
            <SparklesIcon className="h-4 w-4" />
          </button>
        </div>

        {/* Search */}
        <div className="relative mb-4">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search components..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Filters */}
        <div className="space-y-3">
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">Category</label>
            <div className="relative">
              <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* View mode toggle */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">View Mode</label>
            <div className="flex rounded-lg border border-gray-300 p-1 bg-gray-100">
              <button
                onClick={() => setViewMode('grid')}
                className={`flex-1 flex items-center justify-center px-3 py-2 text-sm rounded-md transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Squares2X2Icon className="h-4 w-4 mr-1" />
                Grid
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`flex-1 flex items-center justify-center px-3 py-2 text-sm rounded-md transition-colors ${
                  viewMode === 'list'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <ListBulletIcon className="h-4 w-4 mr-1" />
                List
              </button>
            </div>
          </div>
        </div>

        {/* Component Count */}
        <div className="mt-4 text-xs text-gray-500">
          {Object.values(filteredComponents).reduce((total, components) => total + components.length, 0)} components available
        </div>
      </div>
      
      {/* Component list */}
      <div className="flex-1 overflow-y-auto p-4">
        {Object.keys(filteredComponents).length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <div className="text-4xl mb-2">🔍</div>
            <p>No components found</p>
            <p className="text-sm">Try adjusting your search or category filter</p>
          </div>
        ) : (
          Object.entries(filteredComponents).map(([category, components]) => (
            <div key={category} className="mb-6">
              <h3 className="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide">
                {category}
              </h3>
              
              <div className={`
                ${viewMode === 'grid'
                  ? 'grid grid-cols-2 gap-3'
                  : 'space-y-2'
                }
              `}>
                {components.map(component => (
                  <div
                    key={component.type}
                    draggable
                    onDragStart={(e) => handleDragStart(e, component.type)}
                    onClick={() => handleComponentClick(component.type)}
                    className={`
                      group cursor-pointer border border-gray-200 rounded-xl hover:border-blue-300 hover:shadow-md transition-all duration-200 bg-white
                      ${viewMode === 'grid'
                        ? 'p-4 text-center'
                        : 'p-3 flex items-center space-x-3'
                      }
                    `}
                  >
                    {/* Component Icon */}
                    <div className={`
                      ${viewMode === 'grid' ? 'mb-3' : 'flex-shrink-0'}
                    `}>
                      <div className={`
                        ${viewMode === 'grid'
                          ? 'w-12 h-12 mx-auto bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center text-2xl'
                          : 'w-8 h-8 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center text-lg'
                        }
                      `}>
                        {component.icon}
                      </div>
                    </div>

                    {/* Component Info */}
                    <div className={`
                      ${viewMode === 'grid' ? 'text-center' : 'flex-1 min-w-0'}
                    `}>
                      <div className="text-sm font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                        {component.name}
                      </div>
                      <div className={`text-xs text-gray-500 mt-1 ${viewMode === 'grid' ? 'line-clamp-2' : 'truncate'}`}>
                        {component.description}
                      </div>

                      {/* Category Badge */}
                      {viewMode === 'grid' && (
                        <div className="mt-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                            {component.category}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Action Indicators */}
                    <div className={`
                      opacity-0 group-hover:opacity-100 transition-opacity
                      ${viewMode === 'grid' ? 'absolute top-2 right-2' : 'flex-shrink-0'}
                    `}>
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                      </div>
                    </div>

                    {/* Hover Overlay */}
                    <div className={`
                      absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none
                      ${viewMode === 'grid' ? 'block' : 'hidden'}
                    `}></div>
                  </div>
                ))}
              </div>
            </div>
          ))
        )}
      </div>
      
      {/* Enhanced Footer */}
      <div className="p-4 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50">
        <div className="space-y-2">
          <div className="text-xs text-gray-600 text-center">
            <p className="font-medium">💡 Quick Tips</p>
          </div>
          <div className="text-xs text-gray-500 space-y-1">
            <p>• <strong>Click</strong> to add to canvas center</p>
            <p>• <strong>Drag</strong> to position precisely</p>
            <p>• Use <strong>Ctrl+Z</strong> to undo changes</p>
          </div>
          {showAdvanced && (
            <div className="text-xs text-blue-600 text-center pt-2 border-t border-gray-200">
              <p>✨ Advanced components enabled</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ComponentLibrary
