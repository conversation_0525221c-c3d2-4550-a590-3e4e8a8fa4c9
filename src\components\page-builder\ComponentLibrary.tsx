'use client'

import React, { useState } from 'react'
import { usePageBuilderStore } from '@/lib/page-builder/store'
import { getComponentsByCategory, createDefaultComponent } from '@/lib/page-builder/component-registry'
import { MagnifyingGlassIcon, Squares2X2Icon, ListBulletIcon } from '@heroicons/react/24/outline'

interface ComponentLibraryProps {
  className?: string
}

const ComponentLibrary: React.FC<ComponentLibraryProps> = ({ className = '' }) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  
  const { addComponent } = usePageBuilderStore()
  
  const componentsByCategory = getComponentsByCategory()
  const categories = ['all', ...Object.keys(componentsByCategory)]
  
  // Filter components based on search and category
  const filteredComponents = Object.entries(componentsByCategory).reduce((acc, [category, components]) => {
    if (selectedCategory !== 'all' && category !== selectedCategory) {
      return acc
    }
    
    const filtered = components.filter(component =>
      component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      component.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
    
    if (filtered.length > 0) {
      acc[category] = filtered
    }
    
    return acc
  }, {} as Record<string, typeof componentsByCategory[string]>)
  
  // Handle component drag start
  const handleDragStart = (e: React.DragEvent, componentType: string) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'component',
      componentType
    }))
    e.dataTransfer.effectAllowed = 'copy'
  }
  
  // Handle component click (add to center of canvas)
  const handleComponentClick = (componentType: string) => {
    const component = createDefaultComponent(componentType as any, { x: 100, y: 100 })
    addComponent(component)
  }
  
  return (
    <div className={`flex flex-col h-full bg-white border-r border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Components</h2>
        
        {/* Search */}
        <div className="relative mb-4">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search components..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        {/* Category filter */}
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="w-full mb-4 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          {categories.map(category => (
            <option key={category} value={category}>
              {category === 'all' ? 'All Categories' : category}
            </option>
          ))}
        </select>
        
        {/* View mode toggle */}
        <div className="flex rounded-md border border-gray-300">
          <button
            onClick={() => setViewMode('grid')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-l-md ${
              viewMode === 'grid'
                ? 'bg-blue-50 text-blue-700 border-blue-200'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <Squares2X2Icon className="h-4 w-4 mx-auto" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-r-md border-l ${
              viewMode === 'list'
                ? 'bg-blue-50 text-blue-700 border-blue-200'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <ListBulletIcon className="h-4 w-4 mx-auto" />
          </button>
        </div>
      </div>
      
      {/* Component list */}
      <div className="flex-1 overflow-y-auto p-4">
        {Object.keys(filteredComponents).length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <div className="text-4xl mb-2">🔍</div>
            <p>No components found</p>
            <p className="text-sm">Try adjusting your search or category filter</p>
          </div>
        ) : (
          Object.entries(filteredComponents).map(([category, components]) => (
            <div key={category} className="mb-6">
              <h3 className="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide">
                {category}
              </h3>
              
              <div className={`
                ${viewMode === 'grid' 
                  ? 'grid grid-cols-2 gap-3' 
                  : 'space-y-2'
                }
              `}>
                {components.map(component => (
                  <div
                    key={component.type}
                    draggable
                    onDragStart={(e) => handleDragStart(e, component.type)}
                    onClick={() => handleComponentClick(component.type)}
                    className={`
                      group cursor-pointer border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-all duration-200
                      ${viewMode === 'grid' 
                        ? 'p-3 text-center' 
                        : 'p-3 flex items-center space-x-3'
                      }
                    `}
                  >
                    <div className={`
                      text-2xl
                      ${viewMode === 'grid' ? 'mb-2' : ''}
                    `}>
                      {component.icon}
                    </div>
                    
                    <div className={`
                      ${viewMode === 'grid' ? 'text-center' : 'flex-1'}
                    `}>
                      <div className="text-sm font-medium text-gray-900 group-hover:text-blue-600">
                        {component.name}
                      </div>
                      {viewMode === 'list' && (
                        <div className="text-xs text-gray-500 mt-1">
                          {component.description}
                        </div>
                      )}
                    </div>
                    
                    {/* Drag indicator */}
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="text-xs text-gray-400">
                        {viewMode === 'grid' ? 'Click or drag' : 'Drag'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))
        )}
      </div>
      
      {/* Footer */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-xs text-gray-500 text-center">
          <p>💡 Tip: Drag components to the canvas or click to add</p>
        </div>
      </div>
    </div>
  )
}

export default ComponentLibrary
