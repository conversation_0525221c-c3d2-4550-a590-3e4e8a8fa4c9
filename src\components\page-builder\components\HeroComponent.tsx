'use client'

import React from 'react'
import { Component } from '@/lib/page-builder/store'
import { ComponentConfig } from '@/lib/page-builder/component-registry'

interface HeroComponentProps {
  component: Component
  isEditing: boolean
  isSelected: boolean
  isHovered: boolean
  config: ComponentConfig
  children?: React.ReactNode
}

const HeroComponent: React.FC<HeroComponentProps> = ({
  component,
  isEditing,
  isSelected,
  isHovered,
  config,
  children
}) => {
  const { 
    title = 'Hero Title',
    subtitle = 'Hero subtitle text',
    backgroundImage = '',
    backgroundType = 'color'
  } = component.content
  
  const getBackgroundStyle = () => {
    switch (backgroundType) {
      case 'image':
        return {
          backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
          backgroundSize: component.styles.backgroundSize || 'cover',
          backgroundPosition: component.styles.backgroundPosition || 'center',
          backgroundRepeat: 'no-repeat'
        }
      case 'gradient':
        return {
          background: component.styles.background || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }
      default:
        return {
          backgroundColor: component.styles.backgroundColor || '#f3f4f6'
        }
    }
  }
  
  const heroStyles = {
    ...getBackgroundStyle(),
    minHeight: component.styles.minHeight || '400px',
    display: 'flex',
    alignItems: component.styles.alignItems || 'center',
    justifyContent: component.styles.justifyContent || 'center',
    textAlign: component.styles.textAlign || 'center',
    padding: component.styles.padding || '60px 20px',
    position: 'relative' as const,
    width: '100%',
    height: '100%',
    borderRadius: component.styles.borderRadius || '0px',
    boxShadow: component.styles.boxShadow || 'none'
  }
  
  return (
    <section style={heroStyles}>
      {/* Background overlay */}
      {backgroundType === 'image' && component.styles.backgroundOverlay && (
        <div 
          className="absolute inset-0"
          style={{
            backgroundColor: component.styles.backgroundOverlay,
            opacity: component.styles.backgroundOverlayOpacity || 0.5
          }}
        />
      )}
      
      {/* Content */}
      <div className="relative z-10 max-w-4xl mx-auto">
        {children || (
          <div className="space-y-6">
            <h1 
              className="text-4xl md:text-6xl font-bold"
              style={{
                color: component.styles.titleColor || '#ffffff',
                fontSize: component.styles.titleFontSize || '3rem',
                fontWeight: component.styles.titleFontWeight || 'bold',
                lineHeight: component.styles.titleLineHeight || '1.2'
              }}
            >
              {title}
            </h1>
            
            {subtitle && (
              <p 
                className="text-lg md:text-xl"
                style={{
                  color: component.styles.subtitleColor || '#e5e7eb',
                  fontSize: component.styles.subtitleFontSize || '1.25rem',
                  fontWeight: component.styles.subtitleFontWeight || 'normal',
                  lineHeight: component.styles.subtitleLineHeight || '1.6'
                }}
              >
                {subtitle}
              </p>
            )}
          </div>
        )}
      </div>
      
      {/* Editing placeholder */}
      {isEditing && !children && !title && !subtitle && (
        <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-lg pointer-events-none">
          Hero Section - Add content or drop components here
        </div>
      )}
    </section>
  )
}

export default HeroComponent
