import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse, 
  validateRequest,
  validateMethod,
  requireAdmin,
  ApiError,
  generateSlug
} from '@/lib/api-utils'
import { updateBlogPostSchema } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteContext {
  params: Promise<{
    id: string
  }>
}

// GET /api/blog/[id] - Get a specific blog post
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  const { id } = await params

  const post = await prisma.blogposts.findUnique({
    where: { id },
    include: {
      author: {
        select: {
          id: true,
          firstname: true,
          lastname: true,
          email: true,
          imageurl: true,
        },
      },
    },
  })

  if (!post) {
    throw new ApiError('Blog post not found', 404, 'NOT_FOUND')
  }

  return successResponse(post)
})

// PUT /api/blog/[id] - Update a specific blog post
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])

  const { id } = await params
  const validate = validateRequest(updateBlogPostSchema)
  const data = await validate(request)

  // Check if blog post exists
  const existingPost = await prisma.blogposts.findUnique({
    where: { id },
  })

  if (!existingPost) {
    throw new ApiError('Blog post not found', 404, 'NOT_FOUND')
  }

  // If title is being updated and no slug provided, generate new slug
  if (data.title && !data.slug) {
    data.slug = generateSlug(data.title)
  }

  // If slug is being updated, check if it's unique
  if (data.slug && data.slug !== existingPost.slug) {
    const slugExists = await prisma.blogposts.findFirst({
      where: {
        slug: data.slug,
        id: { not: id },
      },
    })

    if (slugExists) {
      // Generate a unique slug
      let counter = 1
      let newSlug = `${data.slug}-${counter}`
      
      while (await prisma.blogposts.findFirst({ 
        where: { 
          slug: newSlug,
          id: { not: id }
        } 
      })) {
        counter++
        newSlug = `${data.slug}-${counter}`
      }
      
      data.slug = newSlug
    }
  }

  // If authorId is being updated, check if the new author exists
  if (data.authorid) {
    const author = await prisma.users.findUnique({
      where: { id: data.authorid },
    })

    if (!author) {
      throw new ApiError('Author not found', 404, 'AUTHOR_NOT_FOUND')
    }
  }

  const updatedPost = await prisma.blogposts.update({
    where: { id },
    data,
    include: {
      author: {
        select: {
          id: true,
          firstname: true,
          lastname: true,
          email: true,
          imageurl: true,
        },
      },
    },
  })

  return successResponse(updatedPost, 'Blog post updated successfully')
})

// DELETE /api/blog/[id] - Delete a specific blog post
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])

  const { id } = await params

  // Check if blog post exists
  const existingPost = await prisma.blogposts.findUnique({
    where: { id },
  })

  if (!existingPost) {
    throw new ApiError('Blog post not found', 404, 'NOT_FOUND')
  }

  // Delete the blog post
  await prisma.blogposts.delete({
    where: { id },
  })

  return successResponse(null, 'Blog post deleted successfully')
})

// PATCH /api/blog/[id] - Partial update (publish/unpublish, etc.)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  await requireAdmin(request)
  validateMethod(request, ['PATCH'])

  const { id } = await params
  const body = await request.json()

  // Check if blog post exists
  const existingPost = await prisma.blogposts.findUnique({
    where: { id },
  })

  if (!existingPost) {
    throw new ApiError('Blog post not found', 404, 'NOT_FOUND')
  }

  // Handle specific patch operations
  const allowedFields = ['ispublished', 'publishedat']
  const updateData: any = {}

  for (const field of allowedFields) {
    if (field in body) {
      if (field === 'ispublished' && body[field] === true && !existingPost.publishedat) {
        // Set publishedAt when publishing for the first time
        updateData.publishedat = new Date()
      }
      updateData[field] = body[field]
    }
  }

  if (Object.keys(updateData).length === 0) {
    throw new ApiError('No valid fields to update', 400, 'NO_VALID_FIELDS')
  }

  const updatedPost = await prisma.blogposts.update({
    where: { id },
    data: updateData,
    include: {
      author: {
        select: {
          id: true,
          firstname: true,
          lastname: true,
        },
      },
    },
  })

  return successResponse(updatedPost, 'Blog post updated successfully')
})
