import { PageTemplate } from './template-store'
import { Component } from './store'

// Helper function to create component with position
const createComponent = (
  type: string,
  x: number,
  y: number,
  width: number,
  height: number,
  content: any = {},
  styles: any = {}
): Component => ({
  id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  type: type as any,
  position: { x, y, width, height },
  content,
  styles,
  name: type.charAt(0).toUpperCase() + type.slice(1)
})

// Modern Landing Page Template
const modernLandingTemplate: PageTemplate = {
  id: 'modern-landing',
  name: 'Modern Landing Page',
  description: 'A clean, modern landing page with hero section, features, testimonials, and call-to-action',
  category: 'landing',
  thumbnail: '/templates/modern-landing.jpg',
  difficulty: 'intermediate',
  tags: ['modern', 'business', 'conversion', 'responsive'],
  isPopular: true,
  usageCount: 245,
  createdAt: new Date('2024-01-15'),
  updatedAt: new Date('2024-01-15'),
  version: '1.0',
  components: [
    // Hero Section
    createComponent('hero', 0, 0, 1200, 600, {
      title: 'Build Amazing Websites',
      subtitle: 'Create stunning web experiences with our powerful page builder',
      buttonText: 'Get Started',
      buttonUrl: '#features',
      backgroundType: 'gradient'
    }, {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: '#ffffff',
      textAlign: 'center',
      padding: '80px 20px'
    }),
    
    // Features Section
    createComponent('features', 0, 600, 1200, 500, {
      title: 'Amazing Features',
      subtitle: 'Everything you need to succeed',
      features: [
        {
          icon: '🚀',
          title: 'Fast Performance',
          description: 'Lightning fast loading times for better user experience.'
        },
        {
          icon: '🔒',
          title: 'Secure',
          description: 'Enterprise-grade security to protect your data.'
        },
        {
          icon: '📱',
          title: 'Mobile Ready',
          description: 'Fully responsive design that works on all devices.'
        },
        {
          icon: '🎨',
          title: 'Customizable',
          description: 'Easy to customize and make it your own.'
        }
      ]
    }, {
      backgroundColor: '#ffffff',
      padding: '80px 20px',
      textAlign: 'center'
    }),
    
    // Testimonials Section
    createComponent('testimonial', 0, 1100, 1200, 300, {
      quote: 'This service exceeded our expectations. The team was professional and delivered exactly what we needed.',
      author: 'Sarah Johnson',
      position: 'CEO',
      company: 'Tech Innovations',
      avatar: '/placeholder-avatar.jpg',
      rating: 5
    }, {
      backgroundColor: '#f9fafb',
      padding: '60px 20px',
      textAlign: 'center'
    }),
    
    // CTA Section
    createComponent('cta', 0, 1400, 1200, 300, {
      title: 'Ready to Get Started?',
      subtitle: 'Join thousands of satisfied customers today.',
      primaryButtonText: 'Start Free Trial',
      primaryButtonUrl: '/signup',
      secondaryButtonText: 'Learn More',
      secondaryButtonUrl: '/about'
    }, {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: '#ffffff',
      padding: '80px 20px',
      textAlign: 'center'
    })
  ],
  styles: {
    backgroundColor: '#ffffff',
    fontFamily: 'Inter, sans-serif'
  },
  seoSettings: {
    title: 'Modern Landing Page - Build Amazing Websites',
    description: 'Create stunning web experiences with our powerful page builder. Fast, secure, and fully customizable.',
    keywords: ['landing page', 'website builder', 'modern design', 'responsive']
  }
}

// Business Portfolio Template
const businessPortfolioTemplate: PageTemplate = {
  id: 'business-portfolio',
  name: 'Business Portfolio',
  description: 'Professional portfolio template showcasing services, projects, and team',
  category: 'business',
  thumbnail: '/templates/business-portfolio.jpg',
  difficulty: 'advanced',
  tags: ['portfolio', 'business', 'professional', 'services'],
  isPopular: true,
  usageCount: 189,
  createdAt: new Date('2024-01-20'),
  updatedAt: new Date('2024-01-20'),
  version: '1.0',
  components: [
    // Navigation
    createComponent('navbar', 0, 0, 1200, 80, {
      logo: 'Your Business',
      menuItems: [
        { label: 'Home', url: '/' },
        { label: 'Services', url: '#services' },
        { label: 'Portfolio', url: '#portfolio' },
        { label: 'Team', url: '#team' },
        { label: 'Contact', url: '#contact' }
      ],
      ctaText: 'Get Quote',
      ctaUrl: '/contact'
    }),
    
    // Hero Section
    createComponent('hero', 0, 80, 1200, 500, {
      title: 'Professional Business Solutions',
      subtitle: 'We help businesses grow with innovative strategies and cutting-edge technology',
      buttonText: 'View Our Work',
      buttonUrl: '#portfolio'
    }, {
      backgroundColor: '#1f2937',
      color: '#ffffff',
      textAlign: 'center',
      padding: '80px 20px'
    }),
    
    // Services Section
    createComponent('features', 0, 580, 1200, 400, {
      title: 'Our Services',
      subtitle: 'Comprehensive solutions for your business needs',
      features: [
        {
          icon: '💼',
          title: 'Business Consulting',
          description: 'Strategic guidance to help your business thrive.'
        },
        {
          icon: '🎨',
          title: 'Design Services',
          description: 'Beautiful designs that capture your brand essence.'
        },
        {
          icon: '⚡',
          title: 'Development',
          description: 'Custom solutions built with latest technologies.'
        }
      ]
    }),
    
    // Team Section
    createComponent('team', 0, 980, 1200, 500, {
      title: 'Meet Our Team',
      subtitle: 'The talented people behind our success',
      members: [
        {
          name: 'John Smith',
          position: 'CEO & Founder',
          bio: 'Visionary leader with 15+ years of experience',
          photo: '/placeholder-avatar.jpg'
        },
        {
          name: 'Sarah Davis',
          position: 'Creative Director',
          bio: 'Award-winning designer passionate about innovation',
          photo: '/placeholder-avatar.jpg'
        },
        {
          name: 'Mike Johnson',
          position: 'Lead Developer',
          bio: 'Full-stack developer with expertise in modern technologies',
          photo: '/placeholder-avatar.jpg'
        }
      ]
    }),
    
    // Footer
    createComponent('footer', 0, 1480, 1200, 200, {
      companyName: 'Your Business',
      description: 'Professional business solutions for the modern world.',
      columns: [
        {
          title: 'Services',
          links: [
            { label: 'Consulting', url: '/consulting' },
            { label: 'Design', url: '/design' },
            { label: 'Development', url: '/development' }
          ]
        },
        {
          title: 'Company',
          links: [
            { label: 'About', url: '/about' },
            { label: 'Team', url: '/team' },
            { label: 'Careers', url: '/careers' }
          ]
        }
      ]
    })
  ],
  styles: {
    backgroundColor: '#ffffff',
    fontFamily: 'Roboto, sans-serif'
  },
  seoSettings: {
    title: 'Professional Business Portfolio - Your Business',
    description: 'Professional business solutions with innovative strategies and cutting-edge technology.',
    keywords: ['business', 'portfolio', 'professional', 'services', 'consulting']
  }
}

// Creative Agency Template
const creativeAgencyTemplate: PageTemplate = {
  id: 'creative-agency',
  name: 'Creative Agency',
  description: 'Bold and creative template for design agencies and creative professionals',
  category: 'agency',
  thumbnail: '/templates/creative-agency.jpg',
  difficulty: 'advanced',
  tags: ['creative', 'agency', 'design', 'bold'],
  isPopular: false,
  usageCount: 134,
  createdAt: new Date('2024-02-01'),
  updatedAt: new Date('2024-02-01'),
  version: '1.0',
  components: [
    // Hero Section
    createComponent('hero', 0, 0, 1200, 700, {
      title: 'Creative Agency',
      subtitle: 'We create amazing digital experiences that inspire and engage',
      buttonText: 'View Our Work',
      buttonUrl: '#portfolio',
      backgroundType: 'image'
    }, {
      backgroundColor: '#000000',
      color: '#ffffff',
      textAlign: 'center',
      padding: '100px 20px'
    }),
    
    // Stats Section
    createComponent('stats', 0, 700, 1200, 300, {
      title: 'Our Impact',
      stats: [
        { number: '500+', label: 'Projects Completed' },
        { number: '100+', label: 'Happy Clients' },
        { number: '50+', label: 'Awards Won' },
        { number: '10+', label: 'Years Experience' }
      ]
    }, {
      backgroundColor: '#f3f4f6',
      padding: '60px 20px',
      textAlign: 'center'
    }),
    
    // Portfolio Gallery
    createComponent('gallery', 0, 1000, 1200, 500, {
      title: 'Our Work',
      images: [
        { src: '/placeholder-image.jpg', alt: 'Project 1', caption: 'Brand Identity Design' },
        { src: '/placeholder-image.jpg', alt: 'Project 2', caption: 'Web Development' },
        { src: '/placeholder-image.jpg', alt: 'Project 3', caption: 'Mobile App Design' },
        { src: '/placeholder-image.jpg', alt: 'Project 4', caption: 'Digital Marketing' }
      ],
      layout: 'masonry',
      columns: 2
    }),
    
    // CTA Section
    createComponent('cta', 0, 1500, 1200, 250, {
      title: 'Let\'s Create Something Amazing',
      subtitle: 'Ready to bring your vision to life?',
      primaryButtonText: 'Start Project',
      primaryButtonUrl: '/contact'
    }, {
      backgroundColor: '#1f2937',
      color: '#ffffff',
      padding: '60px 20px',
      textAlign: 'center'
    })
  ],
  styles: {
    backgroundColor: '#ffffff',
    fontFamily: 'Montserrat, sans-serif'
  },
  seoSettings: {
    title: 'Creative Agency - Amazing Digital Experiences',
    description: 'We create amazing digital experiences that inspire and engage. Award-winning creative agency.',
    keywords: ['creative agency', 'design', 'digital', 'branding', 'web design']
  }
}

// Personal Blog Template
const personalBlogTemplate: PageTemplate = {
  id: 'personal-blog',
  name: 'Personal Blog',
  description: 'Clean and minimal blog template focused on content and readability',
  category: 'blog',
  thumbnail: '/templates/personal-blog.jpg',
  difficulty: 'beginner',
  tags: ['blog', 'personal', 'minimal', 'content'],
  isPopular: false,
  usageCount: 98,
  createdAt: new Date('2024-02-05'),
  updatedAt: new Date('2024-02-05'),
  version: '1.0',
  components: [
    // Header
    createComponent('navbar', 0, 0, 1200, 80, {
      logo: 'My Blog',
      menuItems: [
        { label: 'Home', url: '/' },
        { label: 'About', url: '/about' },
        { label: 'Blog', url: '/blog' },
        { label: 'Contact', url: '/contact' }
      ]
    }, {
      backgroundColor: '#ffffff',
      borderBottom: '1px solid #e5e7eb'
    }),
    
    // Hero Section
    createComponent('hero', 0, 80, 1200, 400, {
      title: 'Welcome to My Blog',
      subtitle: 'Sharing thoughts, ideas, and experiences about life, technology, and everything in between',
      buttonText: 'Read Latest Posts',
      buttonUrl: '#blog'
    }, {
      backgroundColor: '#f9fafb',
      textAlign: 'center',
      padding: '80px 20px'
    }),
    
    // Blog Section
    createComponent('blog', 0, 480, 1200, 600, {
      title: 'Latest Posts',
      subtitle: 'Recent articles and insights',
      posts: [
        {
          title: 'Getting Started with Web Development',
          excerpt: 'Learn the fundamentals of modern web development...',
          author: 'John Doe',
          date: '2024-01-15',
          image: '/placeholder-image.jpg',
          category: 'Development'
        },
        {
          title: 'Design Trends for 2024',
          excerpt: 'Explore the latest design trends that will shape...',
          author: 'John Doe',
          date: '2024-01-10',
          image: '/placeholder-image.jpg',
          category: 'Design'
        }
      ],
      layout: 'list'
    }),
    
    // Newsletter Section
    createComponent('newsletter', 0, 1080, 1200, 200, {
      title: 'Stay Updated',
      subtitle: 'Subscribe to get the latest posts delivered to your inbox',
      placeholder: 'Enter your email',
      buttonText: 'Subscribe'
    })
  ],
  styles: {
    backgroundColor: '#ffffff',
    fontFamily: 'Georgia, serif'
  },
  seoSettings: {
    title: 'Personal Blog - Thoughts and Ideas',
    description: 'Personal blog sharing thoughts, ideas, and experiences about life, technology, and more.',
    keywords: ['blog', 'personal', 'thoughts', 'technology', 'life']
  }
}

// E-commerce Product Page Template
const ecommerceTemplate: PageTemplate = {
  id: 'ecommerce-product',
  name: 'E-commerce Product Page',
  description: 'Professional product showcase page with features and testimonials',
  category: 'ecommerce',
  thumbnail: '/templates/ecommerce-product.jpg',
  difficulty: 'intermediate',
  tags: ['ecommerce', 'product', 'sales', 'conversion'],
  isPopular: true,
  usageCount: 203,
  createdAt: new Date('2024-02-10'),
  updatedAt: new Date('2024-02-10'),
  version: '1.0',
  components: [
    // Product Hero
    createComponent('hero', 0, 0, 1200, 500, {
      title: 'Amazing Product',
      subtitle: 'The perfect solution for your needs',
      buttonText: 'Buy Now',
      buttonUrl: '#purchase'
    }, {
      backgroundColor: '#ffffff',
      textAlign: 'center',
      padding: '60px 20px'
    }),
    
    // Features
    createComponent('features', 0, 500, 1200, 400, {
      title: 'Product Features',
      subtitle: 'Why customers love our product',
      features: [
        {
          icon: '⭐',
          title: 'Premium Quality',
          description: 'Made with the finest materials and attention to detail.'
        },
        {
          icon: '🚚',
          title: 'Fast Shipping',
          description: 'Free shipping on orders over $50. Delivered in 2-3 days.'
        },
        {
          icon: '🔒',
          title: 'Secure Payment',
          description: 'Your payment information is always safe and secure.'
        }
      ]
    }),
    
    // Testimonials
    createComponent('testimonial', 0, 900, 1200, 250, {
      quote: 'This product exceeded my expectations. Great quality and fast delivery!',
      author: 'Happy Customer',
      position: 'Verified Buyer',
      rating: 5
    }, {
      backgroundColor: '#f9fafb',
      padding: '60px 20px'
    }),
    
    // Pricing CTA
    createComponent('pricing', 0, 1150, 1200, 400, {
      title: 'Choose Your Package',
      plans: [
        {
          name: 'Basic',
          price: '$29',
          features: ['Feature 1', 'Feature 2', 'Feature 3'],
          buttonText: 'Buy Now'
        },
        {
          name: 'Premium',
          price: '$49',
          features: ['Everything in Basic', 'Feature 4', 'Feature 5'],
          buttonText: 'Buy Now',
          popular: true
        }
      ]
    })
  ],
  styles: {
    backgroundColor: '#ffffff',
    fontFamily: 'Inter, sans-serif'
  },
  seoSettings: {
    title: 'Amazing Product - Premium Quality',
    description: 'The perfect solution for your needs. Premium quality with fast shipping and secure payment.',
    keywords: ['product', 'ecommerce', 'premium', 'quality', 'fast shipping']
  }
}

// Export all templates
export const defaultTemplates: PageTemplate[] = [
  modernLandingTemplate,
  businessPortfolioTemplate,
  creativeAgencyTemplate,
  personalBlogTemplate,
  ecommerceTemplate
]
