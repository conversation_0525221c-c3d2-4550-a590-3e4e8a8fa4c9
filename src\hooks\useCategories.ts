'use client'

import { useState, useEffect } from 'react'

export interface Category {
  id: string
  name: string
  description?: string
  isActive: boolean
  displayOrder: number
  _count?: {
    services: number
  }
}

export interface CategoriesResponse {
  success: boolean
  data: Category[]
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Hook for fetching categories
export function useCategories(params?: {
  page?: number
  limit?: number
  search?: string
}) {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<any>(null)

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true)
        setError(null)

        const searchParams = new URLSearchParams()
        if (params?.page) searchParams.set('page', params.page.toString())
        if (params?.limit) searchParams.set('limit', params.limit.toString())
        if (params?.search) searchParams.set('search', params.search)

        const response = await fetch(`/api/categories?${searchParams.toString()}`)
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result: CategoriesResponse = await response.json()
        
        if (result.success) {
          setCategories(result.data)
          setPagination(result.pagination)
        } else {
          throw new Error('Failed to fetch categories')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        setCategories([])
      } finally {
        setLoading(false)
      }
    }

    fetchCategories()
  }, [params?.page, params?.limit, params?.search])

  return { categories, loading, error, pagination }
}

// Hook for fetching a single category
export function useCategory(id: string) {
  const [category, setCategory] = useState<Category | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!id) return

    const fetchCategory = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/categories/${id}`)
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        
        if (result.success) {
          setCategory(result.data)
        } else {
          throw new Error('Failed to fetch category')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        setCategory(null)
      } finally {
        setLoading(false)
      }
    }

    fetchCategory()
  }, [id])

  return { category, loading, error }
}
