import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/team-members/[id] - Get a specific team member
export const GET = with<PERSON><PERSON>r<PERSON><PERSON><PERSON>(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  await requireAdmin(request)

  const { id } = await params

  const teamMember = await prisma.teammembers.findUnique({
    where: {
      id: BigInt(id),
    },
    include: {
      projects: {
        select: {
          id: true,
          name: true,
          status: true,
          projstartdate: true,
          projcompletiondate: true,
        },
        orderBy: {
          createdat: 'desc',
        },
      },
      payrollrecords: {
        select: {
          id: true,
          paydate: true,
          grosspay: true,
          netpay: true,
          status: true,
        },
        orderBy: {
          paydate: 'desc',
        },
        take: 10,
      },
      tasks: {
        select: {
          id: true,
          taskdesc: true,
          status: true,
          taskstartdate: true,
          taskenddate: true,
          workhours: true,
        },
        orderBy: {
          createdat: 'desc',
        },
        take: 20,
      },
      _count: {
        select: {
          projects: true,
          payrollrecords: true,
          tasks: true,
        },
      },
    },
  })

  if (!teamMember) {
    return NextResponse.json(
      { success: false, error: 'Team member not found' },
      { status: 404 }
    )
  }

  const transformedTeamMember = transformFromDbFields.teamMember(teamMember)
  return successResponse(transformedTeamMember, 'Team member retrieved successfully')
})

// PUT /api/admin/team-members/[id] - Update a specific team member
export const PUT = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  await requireAdmin(request)

  const { id } = await params

  const validate = validateRequest(schemas.teamMember.update)
  const validatedData = await validate(request)

  // Check if team member exists
  const existingTeamMember = await prisma.teammembers.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingTeamMember) {
    return NextResponse.json(
      { success: false, error: 'Team member not found' },
      { status: 404 }
    )
  }

  // Check if email is being changed and if it conflicts with another team member
  if (validatedData.email && validatedData.email !== existingTeamMember.email) {
    const emailConflict = await prisma.teammembers.findFirst({
      where: {
        email: validatedData.email,
        id: { not: BigInt(id) },
      },
    })

    if (emailConflict) {
      throw new Error('A team member with this email already exists')
    }
  }

  // Transform data to database format
  const dbData = transformToDbFields.teamMember(validatedData)

  const updatedTeamMember = await prisma.teammembers.update({
    where: {
      id: BigInt(id),
    },
    data: {
      ...dbData,
      updatedat: new Date(),
    },
    include: {
      projects: {
        select: {
          id: true,
          name: true,
          status: true,
        },
        take: 3,
      },
      payrollrecords: {
        select: {
          id: true,
          paydate: true,
          grosspay: true,
        },
        orderBy: {
          paydate: 'desc',
        },
        take: 3,
      },
      tasks: {
        select: {
          id: true,
          taskdesc: true,
          status: true,
        },
        take: 5,
      },
      _count: {
        select: {
          projects: true,
          payrollrecords: true,
          tasks: true,
        },
      },
    },
  })

  const transformedTeamMember = transformFromDbFields.teamMember(updatedTeamMember)
  return successResponse(transformedTeamMember, 'Team member updated successfully')
})

// PATCH /api/admin/team-members/[id] - Partial update (e.g., status toggle)
export const PATCH = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  await requireAdmin(request)

  const { id } = await params

  const body = await request.json()

  // Check if team member exists
  const existingTeamMember = await prisma.teammembers.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingTeamMember) {
    return NextResponse.json(
      { success: false, error: 'Team member not found' },
      { status: 404 }
    )
  }

  // Handle status toggle specifically
  if (body.hasOwnProperty('isActive')) {
    const updatedTeamMember = await prisma.teammembers.update({
      where: { id: BigInt(id) },
      data: {
        isactive: body.isActive,
        updatedat: new Date()
      }
    })

    return successResponse(
      {
        id: Number(updatedTeamMember.id),
        isActive: updatedTeamMember.isactive
      },
      `Team member ${body.isActive ? 'activated' : 'deactivated'} successfully`
    )
  }

  // Handle other partial updates
  const updateData: any = {}

  // Map frontend fields to database fields for partial updates
  if (body.name !== undefined) updateData.name = body.name
  if (body.position !== undefined) updateData.position = body.position
  if (body.email !== undefined) updateData.email = body.email
  if (body.phone !== undefined) updateData.phone = body.phone
  if (body.isActive !== undefined) updateData.isactive = body.isActive

  // Always update the timestamp
  updateData.updatedat = new Date()

  const updatedTeamMember = await prisma.teammembers.update({
    where: { id: BigInt(id) },
    data: updateData
  })

  const transformedTeamMember = transformFromDbFields.teamMember(updatedTeamMember)
  return successResponse(transformedTeamMember, 'Team member updated successfully')
})

// DELETE /api/admin/team-members/[id] - Delete a specific team member
export const DELETE = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if team member exists
  const existingTeamMember = await prisma.teammembers.findUnique({
    where: { id: BigInt(id) },
    include: {
      _count: {
        select: {
          projects: true,
          payrollrecords: true,
          tasks: true,
        },
      },
    },
  })

  if (!existingTeamMember) {
    return NextResponse.json(
      { success: false, error: 'Team member not found' },
      { status: 404 }
    )
  }

  // Check if team member has associated data
  const hasAssociatedData =
    existingTeamMember._count.projects > 0 ||
    existingTeamMember._count.payrollrecords > 0 ||
    existingTeamMember._count.tasks > 0

  if (hasAssociatedData) {
    return NextResponse.json(
      {
        success: false,
        error: `Cannot delete team member ${existingTeamMember.name}. They have associated projects, payroll records, or tasks. Please handle these first.`
      },
      { status: 400 }
    )
  }

  await prisma.teammembers.delete({
    where: {
      id: BigInt(id),
    },
  })

  return successResponse(
    { id: Number(id) },
    'Team member deleted successfully'
  )
})
