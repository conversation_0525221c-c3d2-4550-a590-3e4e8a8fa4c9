'use client';

import { useParams } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  CheckIcon,
  ClockIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ArrowLeftIcon,
  StarIcon,
} from '@heroicons/react/24/outline';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { useServiceBySlug } from '@/hooks/useServices';
import { ServiceCard } from '@/components/services/service-card';



export default function ServiceDetailsPage() {
  const params = useParams();
  const slug = params.slug as string;
  const { service, relatedServices, testimonials, loading, error } = useServiceBySlug(slug);

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <main className="pt-20">
          <div className="container py-24">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="h-12 bg-gray-200 rounded w-1/2 mb-6"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error || !service) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <main className="pt-20">
          <div className="container py-24 text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Service Not Found</h1>
            <p className="text-lg text-gray-600 mb-8">
              {error || "The service you're looking for doesn't exist."}
            </p>
            <Link href="/services" className="btn-primary">
              Back to Services
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const formatPrice = (price: number, discountRate?: number) => {
    const formattedPrice = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(price);

    if (discountRate && discountRate > 0) {
      const discountedPrice = price * (1 - discountRate / 100);
      const formattedDiscountedPrice = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
      }).format(discountedPrice);

      return `Starting at ${formattedDiscountedPrice} (was ${formattedPrice})`;
    }

    return `Starting at ${formattedPrice}`;
  };

  const renderStars = (rating: number) => {
    return [...Array(5)].map((_, i) => (
      <StarIcon
        key={i}
        className={`w-5 h-5 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="pt-20">
        {/* Breadcrumb */}
        <section className="py-8 bg-gray-50">
          <div className="container">
            <nav className="flex items-center space-x-2 text-sm text-gray-600">
              <Link href="/" className="hover:text-blue-600">Home</Link>
              <span>/</span>
              <Link href="/services" className="hover:text-blue-600">Services</Link>
              <span>/</span>
              <span className="text-gray-900">{service.name}</span>
            </nav>
          </div>
        </section>

        {/* Hero Section */}
        <section className="py-16 bg-white">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
              >
                <Link
                  href="/services"
                  className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-6 group"
                >
                  <ArrowLeftIcon className="w-4 h-4 mr-2 transition-transform group-hover:-translate-x-1" />
                  Back to Services
                </Link>

                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mr-4">
                    <div className="w-8 h-8 bg-blue-600 rounded-lg"></div>
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold text-gray-900">{service.name}</h1>
                    <p className="text-lg text-gray-600 mt-2">{service.category.name}</p>
                  </div>
                </div>

                <p className="text-xl text-gray-600 leading-relaxed mb-8">
                  {service.description}
                </p>

                <div className="grid grid-cols-2 gap-6 mb-8">
                  <div className="flex items-center">
                    <CurrencyDollarIcon className="w-5 h-5 text-blue-600 mr-2" />
                    <div>
                      <div className="text-sm text-gray-500">Pricing</div>
                      <div className="font-semibold text-gray-900">{formatPrice(service.price, service.discountRate)}</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <UserGroupIcon className="w-5 h-5 text-blue-600 mr-2" />
                    <div>
                      <div className="text-sm text-gray-500">Manager</div>
                      <div className="font-semibold text-gray-900">{service.manager || 'Contact us'}</div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/contact" className="btn-primary">
                    Get Started
                  </Link>
                  <Link href="/portfolio" className="btn-secondary">
                    View Examples
                  </Link>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="relative"
              >
                <div className="aspect-square bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 flex items-center justify-center">
                  <div className="w-32 h-32 bg-blue-600 rounded-2xl flex items-center justify-center">
                    <div className="w-16 h-16 bg-white rounded-lg"></div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-gray-50">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Service Options</h2>
                <div className="space-y-6">
                  {service.serviceOptions.map((option, index) => (
                    <motion.div
                      key={option.id}
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="bg-white p-6 rounded-lg border border-gray-200"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">{option.name}</h3>
                        {option.price && (
                          <span className="text-blue-600 font-medium">
                            ${option.price.toLocaleString()}
                          </span>
                        )}
                      </div>
                      {option.description && (
                        <p className="text-gray-600 mb-4">{option.description}</p>
                      )}
                      {option.features.length > 0 && (
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                          {option.features.map((feature) => (
                            <div key={feature.id} className="flex items-center">
                              <CheckIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                              <span className="text-sm text-gray-700">{feature.name}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Project Statistics</h2>
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center p-4 bg-white rounded-lg border border-gray-200">
                    <div className="text-2xl font-bold text-blue-600">{service._count.projects}</div>
                    <div className="text-sm text-gray-600">Projects Completed</div>
                  </div>
                  <div className="text-center p-4 bg-white rounded-lg border border-gray-200">
                    <div className="text-2xl font-bold text-blue-600">{service._count.serviceOptions}</div>
                    <div className="text-sm text-gray-600">Service Options</div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Projects Section */}
        {service.projects && service.projects.length > 0 && (
          <section className="py-16 bg-white">
            <div className="container">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center mb-12"
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Recent Projects</h2>
                <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                  Take a look at some of our recent {service.name.toLowerCase()} projects.
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {service.projects.map((project, index) => (
                  <motion.div
                    key={project.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-gray-50 p-6 rounded-lg"
                  >
                    {project.imageUrl && (
                      <div className="aspect-video bg-gray-200 rounded-lg mb-4 overflow-hidden">
                        <img
                          src={project.imageUrl}
                          alt={project.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{project.name}</h3>
                    {project.description && (
                      <p className="text-gray-600 mb-4 text-sm">{project.description}</p>
                    )}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{project.client.companyName}</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        project.status === 'COMPLETED'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {project.status}
                      </span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Process Section */}
        <section className="py-16 bg-gray-50">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Process</h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                We follow a proven methodology to ensure successful delivery of your {service.name.toLowerCase()} project.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  title: 'Discovery',
                  description: 'We understand your business goals, requirements, and technical needs through detailed consultation.'
                },
                {
                  title: 'Planning',
                  description: 'We create a comprehensive project plan with timelines, milestones, and resource allocation.'
                },
                {
                  title: 'Development',
                  description: 'Our expert team builds your solution using agile methodology with regular updates and feedback.'
                },
                {
                  title: 'Delivery',
                  description: 'We deploy your solution and provide ongoing support to ensure optimal performance.'
                }
              ].map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-lg font-bold mx-auto mb-4">
                    {index + 1}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        {testimonials && testimonials.length > 0 && (
          <section className="py-16 bg-white">
            <div className="container">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center mb-12"
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-4">What Our Clients Say</h2>
                <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                  Don't just take our word for it. Here's what our clients have to say about our services.
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {testimonials.map((testimonial, index) => (
                  <motion.div
                    key={testimonial.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-gray-50 p-8 rounded-lg"
                  >
                    <div className="flex items-center mb-4">
                      {renderStars(testimonial.rating)}
                    </div>
                    <blockquote className="text-gray-700 mb-6 text-lg leading-relaxed">
                      "{testimonial.content}"
                    </blockquote>
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.clientName}</div>
                      <div className="text-gray-600">
                        {testimonial.clientTitle && testimonial.clientCompany
                          ? `${testimonial.clientTitle} at ${testimonial.clientCompany}`
                          : testimonial.clientTitle || testimonial.clientCompany || 'Client'
                        }
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Related Services Section */}
        {relatedServices && relatedServices.length > 0 && (
          <section className="py-16 bg-gray-50">
            <div className="container">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center mb-12"
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Related Services</h2>
                <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                  Explore other services in the {service.category.name} category.
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {relatedServices.map((relatedService, index) => (
                  <ServiceCard
                    key={relatedService.id}
                    service={relatedService}
                    index={index}
                    variant="compact"
                    showCTA={true}
                  />
                ))}
              </div>
            </div>
          </section>
        )}

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <h2 className="text-3xl font-bold text-white mb-4">
                Ready to Start Your {service.name} Project?
              </h2>
              <p className="text-lg text-blue-100 max-w-3xl mx-auto mb-8">
                Let's discuss your requirements and create a custom solution that drives your business forward.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors"
                >
                  Get Started Today
                </Link>
                <Link
                  href="/services"
                  className="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors"
                >
                  View All Services
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
