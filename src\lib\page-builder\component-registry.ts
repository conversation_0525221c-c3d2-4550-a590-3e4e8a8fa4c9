import { ComponentType } from './store'

// Component configuration interface
export interface ComponentConfig {
  type: ComponentType
  name: string
  description: string
  icon: string
  category: string
  defaultProps: Record<string, any>
  defaultStyles: Record<string, any>
  defaultSize: {
    width: number
    height: number
  }
  resizable: boolean
  draggable: boolean
  deletable: boolean
  duplicatable: boolean
  hasChildren: boolean
  maxChildren?: number
  allowedChildren?: ComponentType[]
  allowedParents?: ComponentType[]
  styleGroups: StyleGroup[]
  contentFields: ContentField[]
}

// Style group for organizing style controls
export interface StyleGroup {
  name: string
  label: string
  icon?: string
  properties: StyleProperty[]
}

// Individual style property
export interface StyleProperty {
  name: string
  label: string
  type: 'text' | 'number' | 'color' | 'select' | 'slider' | 'toggle' | 'spacing' | 'border' | 'shadow'
  defaultValue: any
  options?: { label: string; value: any }[]
  min?: number
  max?: number
  step?: number
  unit?: string
  description?: string
}

// Content field for component content editing
export interface ContentField {
  name: string
  label: string
  type: 'text' | 'textarea' | 'rich-text' | 'image' | 'url' | 'select' | 'number' | 'toggle' | 'date'
  defaultValue: any
  required?: boolean
  placeholder?: string
  options?: { label: string; value: any }[]
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
  description?: string
  databaseBindable?: boolean
}

// Component registry
export const componentRegistry: Record<ComponentType, ComponentConfig> = {
  text: {
    type: 'text',
    name: 'Text',
    description: 'Simple text element',
    icon: '📝',
    category: 'Basic',
    defaultProps: {
      text: 'Your text here',
      tag: 'p'
    },
    defaultStyles: {
      fontSize: '16px',
      fontWeight: 'normal',
      color: '#000000',
      textAlign: 'left',
      lineHeight: '1.5'
    },
    defaultSize: { width: 200, height: 50 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'typography',
        label: 'Typography',
        icon: '🔤',
        properties: [
          { name: 'fontSize', label: 'Font Size', type: 'slider', defaultValue: 16, min: 8, max: 72, unit: 'px' },
          { name: 'fontWeight', label: 'Font Weight', type: 'select', defaultValue: 'normal', options: [
            { label: 'Light', value: '300' },
            { label: 'Normal', value: 'normal' },
            { label: 'Medium', value: '500' },
            { label: 'Bold', value: 'bold' }
          ]},
          { name: 'color', label: 'Text Color', type: 'color', defaultValue: '#000000' },
          { name: 'textAlign', label: 'Text Align', type: 'select', defaultValue: 'left', options: [
            { label: 'Left', value: 'left' },
            { label: 'Center', value: 'center' },
            { label: 'Right', value: 'right' },
            { label: 'Justify', value: 'justify' }
          ]},
          { name: 'lineHeight', label: 'Line Height', type: 'slider', defaultValue: 1.5, min: 1, max: 3, step: 0.1 }
        ]
      }
    ],
    contentFields: [
      { name: 'text', label: 'Text Content', type: 'textarea', defaultValue: 'Your text here', required: true, databaseBindable: true },
      { name: 'tag', label: 'HTML Tag', type: 'select', defaultValue: 'p', options: [
        { label: 'Paragraph', value: 'p' },
        { label: 'Heading 1', value: 'h1' },
        { label: 'Heading 2', value: 'h2' },
        { label: 'Heading 3', value: 'h3' },
        { label: 'Heading 4', value: 'h4' },
        { label: 'Heading 5', value: 'h5' },
        { label: 'Heading 6', value: 'h6' },
        { label: 'Span', value: 'span' }
      ]}
    ]
  },

  image: {
    type: 'image',
    name: 'Image',
    description: 'Image element with responsive options',
    icon: '🖼️',
    category: 'Media',
    defaultProps: {
      src: '/placeholder-image.svg',
      alt: 'Image description',
      objectFit: 'cover'
    },
    defaultStyles: {
      borderRadius: '0px',
      opacity: '1'
    },
    defaultSize: { width: 300, height: 200 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'borderRadius', label: 'Border Radius', type: 'slider', defaultValue: 0, min: 0, max: 50, unit: 'px' },
          { name: 'opacity', label: 'Opacity', type: 'slider', defaultValue: 1, min: 0, max: 1, step: 0.1 }
        ]
      }
    ],
    contentFields: [
      { name: 'src', label: 'Image URL', type: 'image', defaultValue: '/placeholder-image.svg', required: true, databaseBindable: true },
      { name: 'alt', label: 'Alt Text', type: 'text', defaultValue: 'Image description', databaseBindable: true },
      { name: 'objectFit', label: 'Object Fit', type: 'select', defaultValue: 'cover', options: [
        { label: 'Cover', value: 'cover' },
        { label: 'Contain', value: 'contain' },
        { label: 'Fill', value: 'fill' },
        { label: 'None', value: 'none' }
      ]}
    ]
  },

  button: {
    type: 'button',
    name: 'Button',
    description: 'Interactive button element',
    icon: '🔘',
    category: 'Interactive',
    defaultProps: {
      text: 'Click me',
      href: '#',
      target: '_self',
      variant: 'primary'
    },
    defaultStyles: {
      backgroundColor: '#3b82f6',
      color: '#ffffff',
      padding: '12px 24px',
      borderRadius: '6px',
      border: 'none',
      fontSize: '16px',
      fontWeight: '500',
      cursor: 'pointer'
    },
    defaultSize: { width: 120, height: 44 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'backgroundColor', label: 'Background Color', type: 'color', defaultValue: '#3b82f6' },
          { name: 'color', label: 'Text Color', type: 'color', defaultValue: '#ffffff' },
          { name: 'borderRadius', label: 'Border Radius', type: 'slider', defaultValue: 6, min: 0, max: 50, unit: 'px' },
          { name: 'fontSize', label: 'Font Size', type: 'slider', defaultValue: 16, min: 12, max: 24, unit: 'px' }
        ]
      }
    ],
    contentFields: [
      { name: 'text', label: 'Button Text', type: 'text', defaultValue: 'Click me', required: true, databaseBindable: true },
      { name: 'href', label: 'Link URL', type: 'url', defaultValue: '#', databaseBindable: true },
      { name: 'target', label: 'Link Target', type: 'select', defaultValue: '_self', options: [
        { label: 'Same Window', value: '_self' },
        { label: 'New Window', value: '_blank' }
      ]},
      { name: 'variant', label: 'Button Style', type: 'select', defaultValue: 'primary', options: [
        { label: 'Primary', value: 'primary' },
        { label: 'Secondary', value: 'secondary' },
        { label: 'Outline', value: 'outline' },
        { label: 'Ghost', value: 'ghost' }
      ]}
    ]
  },

  container: {
    type: 'container',
    name: 'Container',
    description: 'Container for grouping other elements',
    icon: '📦',
    category: 'Layout',
    defaultProps: {
      tag: 'div'
    },
    defaultStyles: {
      backgroundColor: 'transparent',
      padding: '20px',
      borderRadius: '0px',
      border: '1px dashed #e5e7eb'
    },
    defaultSize: { width: 400, height: 300 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: true,
    styleGroups: [
      {
        name: 'layout',
        label: 'Layout',
        properties: [
          { name: 'display', label: 'Display', type: 'select', defaultValue: 'block', options: [
            { label: 'Block', value: 'block' },
            { label: 'Flex', value: 'flex' },
            { label: 'Grid', value: 'grid' },
            { label: 'Inline Block', value: 'inline-block' }
          ]},
          { name: 'flexDirection', label: 'Flex Direction', type: 'select', defaultValue: 'row', options: [
            { label: 'Row', value: 'row' },
            { label: 'Column', value: 'column' }
          ]},
          { name: 'justifyContent', label: 'Justify Content', type: 'select', defaultValue: 'flex-start', options: [
            { label: 'Start', value: 'flex-start' },
            { label: 'Center', value: 'center' },
            { label: 'End', value: 'flex-end' },
            { label: 'Space Between', value: 'space-between' },
            { label: 'Space Around', value: 'space-around' }
          ]},
          { name: 'alignItems', label: 'Align Items', type: 'select', defaultValue: 'flex-start', options: [
            { label: 'Start', value: 'flex-start' },
            { label: 'Center', value: 'center' },
            { label: 'End', value: 'flex-end' },
            { label: 'Stretch', value: 'stretch' }
          ]}
        ]
      },
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'backgroundColor', label: 'Background Color', type: 'color', defaultValue: 'transparent' },
          { name: 'borderRadius', label: 'Border Radius', type: 'slider', defaultValue: 0, min: 0, max: 50, unit: 'px' }
        ]
      }
    ],
    contentFields: [
      { name: 'tag', label: 'HTML Tag', type: 'select', defaultValue: 'div', options: [
        { label: 'Div', value: 'div' },
        { label: 'Section', value: 'section' },
        { label: 'Article', value: 'article' },
        { label: 'Header', value: 'header' },
        { label: 'Footer', value: 'footer' },
        { label: 'Main', value: 'main' },
        { label: 'Aside', value: 'aside' }
      ]}
    ]
  },

  // Add more component types here...
  grid: {
    type: 'grid',
    name: 'Grid',
    description: 'CSS Grid layout container',
    icon: '⚏',
    category: 'Layout',
    defaultProps: {
      columns: 2,
      rows: 2,
      gap: 16
    },
    defaultStyles: {
      display: 'grid',
      gridTemplateColumns: 'repeat(2, 1fr)',
      gridTemplateRows: 'repeat(2, 1fr)',
      gap: '16px',
      padding: '20px'
    },
    defaultSize: { width: 400, height: 300 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: true,
    styleGroups: [
      {
        name: 'grid',
        label: 'Grid Layout',
        properties: [
          { name: 'gridTemplateColumns', label: 'Columns', type: 'text', defaultValue: 'repeat(2, 1fr)' },
          { name: 'gridTemplateRows', label: 'Rows', type: 'text', defaultValue: 'repeat(2, 1fr)' },
          { name: 'gap', label: 'Gap', type: 'slider', defaultValue: 16, min: 0, max: 50, unit: 'px' }
        ]
      }
    ],
    contentFields: [
      { name: 'columns', label: 'Number of Columns', type: 'number', defaultValue: 2, validation: { min: 1, max: 12 } },
      { name: 'rows', label: 'Number of Rows', type: 'number', defaultValue: 2, validation: { min: 1, max: 12 } },
      { name: 'gap', label: 'Grid Gap', type: 'number', defaultValue: 16, validation: { min: 0, max: 100 } }
    ]
  },

  hero: {
    type: 'hero',
    name: 'Hero Section',
    description: 'Hero section with background and content',
    icon: '🦸',
    category: 'Sections',
    defaultProps: {
      title: 'Hero Title',
      subtitle: 'Hero subtitle text',
      backgroundImage: '',
      backgroundType: 'color'
    },
    defaultStyles: {
      minHeight: '400px',
      backgroundColor: '#f3f4f6',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      textAlign: 'center',
      padding: '60px 20px'
    },
    defaultSize: { width: 800, height: 400 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: true,
    styleGroups: [
      {
        name: 'background',
        label: 'Background',
        properties: [
          { name: 'backgroundColor', label: 'Background Color', type: 'color', defaultValue: '#f3f4f6' },
          { name: 'backgroundSize', label: 'Background Size', type: 'select', defaultValue: 'cover', options: [
            { label: 'Cover', value: 'cover' },
            { label: 'Contain', value: 'contain' },
            { label: 'Auto', value: 'auto' }
          ]},
          { name: 'backgroundPosition', label: 'Background Position', type: 'select', defaultValue: 'center', options: [
            { label: 'Center', value: 'center' },
            { label: 'Top', value: 'top' },
            { label: 'Bottom', value: 'bottom' },
            { label: 'Left', value: 'left' },
            { label: 'Right', value: 'right' }
          ]}
        ]
      }
    ],
    contentFields: [
      { name: 'title', label: 'Hero Title', type: 'text', defaultValue: 'Hero Title', required: true, databaseBindable: true },
      { name: 'subtitle', label: 'Hero Subtitle', type: 'textarea', defaultValue: 'Hero subtitle text', databaseBindable: true },
      { name: 'backgroundImage', label: 'Background Image', type: 'image', defaultValue: '', databaseBindable: true },
      { name: 'backgroundType', label: 'Background Type', type: 'select', defaultValue: 'color', options: [
        { label: 'Color', value: 'color' },
        { label: 'Image', value: 'image' },
        { label: 'Gradient', value: 'gradient' }
      ]}
    ]
  },

  // Enhanced Layout Components
  column: {
    type: 'column',
    name: 'Column',
    description: 'Flexible column layout container',
    icon: '📄',
    category: 'Layout',
    defaultProps: {
      gap: 16,
      alignment: 'stretch'
    },
    defaultStyles: {
      display: 'flex',
      flexDirection: 'column',
      gap: '16px',
      padding: '20px',
      backgroundColor: 'transparent'
    },
    defaultSize: { width: 200, height: 300 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: true,
    styleGroups: [
      {
        name: 'layout',
        label: 'Layout',
        properties: [
          { name: 'gap', label: 'Gap', type: 'slider', defaultValue: 16, min: 0, max: 50, unit: 'px' },
          { name: 'padding', label: 'Padding', type: 'spacing', defaultValue: '20px' },
          { name: 'alignItems', label: 'Alignment', type: 'select', defaultValue: 'stretch', options: [
            { label: 'Stretch', value: 'stretch' },
            { label: 'Start', value: 'flex-start' },
            { label: 'Center', value: 'center' },
            { label: 'End', value: 'flex-end' }
          ]}
        ]
      }
    ],
    contentFields: []
  },

  row: {
    type: 'row',
    name: 'Row',
    description: 'Flexible row layout container',
    icon: '📏',
    category: 'Layout',
    defaultProps: {
      gap: 16,
      alignment: 'center'
    },
    defaultStyles: {
      display: 'flex',
      flexDirection: 'row',
      gap: '16px',
      padding: '20px',
      alignItems: 'center',
      backgroundColor: 'transparent'
    },
    defaultSize: { width: 400, height: 100 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: true,
    styleGroups: [
      {
        name: 'layout',
        label: 'Layout',
        properties: [
          { name: 'gap', label: 'Gap', type: 'slider', defaultValue: 16, min: 0, max: 50, unit: 'px' },
          { name: 'padding', label: 'Padding', type: 'spacing', defaultValue: '20px' },
          { name: 'alignItems', label: 'Alignment', type: 'select', defaultValue: 'center', options: [
            { label: 'Top', value: 'flex-start' },
            { label: 'Center', value: 'center' },
            { label: 'Bottom', value: 'flex-end' },
            { label: 'Stretch', value: 'stretch' }
          ]}
        ]
      }
    ],
    contentFields: []
  },

  // Enhanced Content Components
  card: {
    type: 'card',
    name: 'Card',
    description: 'Modern card component with shadow and border',
    icon: '🃏',
    category: 'Content',
    defaultProps: {
      title: 'Card Title',
      content: 'Card content goes here...',
      imageUrl: '',
      showImage: false,
      showButton: true,
      buttonText: 'Learn More',
      buttonUrl: '#'
    },
    defaultStyles: {
      backgroundColor: '#ffffff',
      borderRadius: '12px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      border: '1px solid #e5e7eb',
      padding: '24px',
      textAlign: 'left'
    },
    defaultSize: { width: 300, height: 250 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'backgroundColor', label: 'Background', type: 'color', defaultValue: '#ffffff' },
          { name: 'borderRadius', label: 'Border Radius', type: 'slider', defaultValue: 12, min: 0, max: 50, unit: 'px' },
          { name: 'boxShadow', label: 'Shadow', type: 'select', defaultValue: '0 4px 6px rgba(0, 0, 0, 0.1)', options: [
            { label: 'None', value: 'none' },
            { label: 'Small', value: '0 1px 3px rgba(0, 0, 0, 0.1)' },
            { label: 'Medium', value: '0 4px 6px rgba(0, 0, 0, 0.1)' },
            { label: 'Large', value: '0 10px 15px rgba(0, 0, 0, 0.1)' }
          ]}
        ]
      }
    ],
    contentFields: [
      { name: 'title', label: 'Title', type: 'text', defaultValue: 'Card Title' },
      { name: 'content', label: 'Content', type: 'textarea', defaultValue: 'Card content goes here...' },
      { name: 'imageUrl', label: 'Image URL', type: 'text', defaultValue: '' },
      { name: 'buttonText', label: 'Button Text', type: 'text', defaultValue: 'Learn More' },
      { name: 'buttonUrl', label: 'Button URL', type: 'text', defaultValue: '#' }
    ]
  },

  list: {
    type: 'list',
    name: 'List',
    description: 'Customizable list component',
    icon: '📋',
    category: 'Content',
    defaultProps: {
      items: ['List item 1', 'List item 2', 'List item 3'],
      listType: 'unordered',
      showIcons: false,
      iconType: '•'
    },
    defaultStyles: {
      padding: '20px',
      backgroundColor: 'transparent',
      fontSize: '16px',
      lineHeight: '1.6'
    },
    defaultSize: { width: 250, height: 200 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'typography',
        label: 'Typography',
        properties: [
          { name: 'fontSize', label: 'Font Size', type: 'slider', defaultValue: 16, min: 12, max: 24, unit: 'px' },
          { name: 'lineHeight', label: 'Line Height', type: 'slider', defaultValue: 1.6, min: 1, max: 2, step: 0.1 },
          { name: 'color', label: 'Text Color', type: 'color', defaultValue: '#374151' }
        ]
      }
    ],
    contentFields: [
      { name: 'items', label: 'List Items', type: 'array', defaultValue: ['List item 1', 'List item 2', 'List item 3'] },
      { name: 'listType', label: 'List Type', type: 'select', defaultValue: 'unordered', options: [
        { label: 'Unordered', value: 'unordered' },
        { label: 'Ordered', value: 'ordered' }
      ]}
    ]
  },

  // Enhanced Interactive Components
  form: {
    type: 'form',
    name: 'Form',
    description: 'Contact form with validation',
    icon: '📝',
    category: 'Interactive',
    defaultProps: {
      title: 'Contact Form',
      fields: [
        { name: 'name', label: 'Name', type: 'text', required: true },
        { name: 'email', label: 'Email', type: 'email', required: true },
        { name: 'message', label: 'Message', type: 'textarea', required: true }
      ],
      submitText: 'Send Message',
      action: '/api/contact',
      method: 'POST'
    },
    defaultStyles: {
      backgroundColor: '#ffffff',
      padding: '32px',
      borderRadius: '12px',
      border: '1px solid #e5e7eb'
    },
    defaultSize: { width: 400, height: 350 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'backgroundColor', label: 'Background', type: 'color', defaultValue: '#ffffff' },
          { name: 'borderRadius', label: 'Border Radius', type: 'slider', defaultValue: 12, min: 0, max: 50, unit: 'px' },
          { name: 'padding', label: 'Padding', type: 'spacing', defaultValue: '32px' }
        ]
      }
    ],
    contentFields: [
      { name: 'title', label: 'Form Title', type: 'text', defaultValue: 'Contact Form' },
      { name: 'submitText', label: 'Submit Button Text', type: 'text', defaultValue: 'Send Message' }
    ]
  },

  // Enhanced Media Components
  video: {
    type: 'video',
    name: 'Video',
    description: 'Responsive video player',
    icon: '🎥',
    category: 'Media',
    defaultProps: {
      src: '',
      poster: '',
      autoplay: false,
      controls: true,
      loop: false,
      muted: false,
      aspectRatio: '16:9'
    },
    defaultStyles: {
      width: '100%',
      borderRadius: '8px',
      backgroundColor: '#000000'
    },
    defaultSize: { width: 400, height: 225 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'borderRadius', label: 'Border Radius', type: 'slider', defaultValue: 8, min: 0, max: 50, unit: 'px' }
        ]
      }
    ],
    contentFields: [
      { name: 'src', label: 'Video URL', type: 'text', defaultValue: '' },
      { name: 'poster', label: 'Poster Image', type: 'text', defaultValue: '' },
      { name: 'autoplay', label: 'Autoplay', type: 'checkbox', defaultValue: false },
      { name: 'controls', label: 'Show Controls', type: 'checkbox', defaultValue: true },
      { name: 'loop', label: 'Loop', type: 'checkbox', defaultValue: false },
      { name: 'muted', label: 'Muted', type: 'checkbox', defaultValue: false }
    ]
  },

  embed: {
    type: 'embed',
    name: 'Embed',
    description: 'Embed external content (YouTube, maps, etc.)',
    icon: '🔗',
    category: 'Media',
    defaultProps: {
      embedCode: '',
      embedType: 'iframe',
      src: '',
      title: 'Embedded Content'
    },
    defaultStyles: {
      width: '100%',
      height: '100%',
      border: 'none',
      borderRadius: '8px'
    },
    defaultSize: { width: 400, height: 300 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'borderRadius', label: 'Border Radius', type: 'slider', defaultValue: 8, min: 0, max: 50, unit: 'px' }
        ]
      }
    ],
    contentFields: [
      { name: 'src', label: 'Embed URL', type: 'text', defaultValue: '' },
      { name: 'title', label: 'Title', type: 'text', defaultValue: 'Embedded Content' },
      { name: 'embedCode', label: 'Custom Embed Code', type: 'textarea', defaultValue: '' }
    ]
  },

  // Enhanced Layout Utilities
  spacer: {
    type: 'spacer',
    name: 'Spacer',
    description: 'Add spacing between elements',
    icon: '⬜',
    category: 'Layout',
    defaultProps: {
      height: 50,
      width: 100
    },
    defaultStyles: {
      backgroundColor: 'transparent',
      border: '1px dashed #d1d5db',
      opacity: '0.5'
    },
    defaultSize: { width: 100, height: 50 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'dimensions',
        label: 'Dimensions',
        properties: [
          { name: 'height', label: 'Height', type: 'slider', defaultValue: 50, min: 10, max: 200, unit: 'px' }
        ]
      }
    ],
    contentFields: []
  },

  divider: {
    type: 'divider',
    name: 'Divider',
    description: 'Visual separator line',
    icon: '➖',
    category: 'Layout',
    defaultProps: {
      style: 'solid',
      thickness: 1,
      color: '#e5e7eb'
    },
    defaultStyles: {
      width: '100%',
      height: '1px',
      backgroundColor: '#e5e7eb',
      border: 'none',
      margin: '20px 0'
    },
    defaultSize: { width: 300, height: 1 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'backgroundColor', label: 'Color', type: 'color', defaultValue: '#e5e7eb' },
          { name: 'height', label: 'Thickness', type: 'slider', defaultValue: 1, min: 1, max: 10, unit: 'px' },
          { name: 'margin', label: 'Margin', type: 'spacing', defaultValue: '20px 0' }
        ]
      }
    ],
    contentFields: []
  }
}

// Add new advanced section components to the component types
export type ExtendedComponentType = ComponentType |
  'navbar' | 'footer' | 'testimonial' | 'pricing' | 'features' |
  'cta' | 'stats' | 'team' | 'gallery' | 'blog' | 'newsletter'

// Extended component registry with advanced sections
export const extendedComponentRegistry: Record<string, ComponentConfig> = {
  ...componentRegistry,

  // Navigation Components
  navbar: {
    type: 'navbar' as any,
    name: 'Navigation Bar',
    description: 'Responsive navigation header',
    icon: '🧭',
    category: 'Navigation',
    defaultProps: {
      logo: 'Your Logo',
      logoUrl: '#',
      menuItems: [
        { label: 'Home', url: '/' },
        { label: 'About', url: '/about' },
        { label: 'Services', url: '/services' },
        { label: 'Contact', url: '/contact' }
      ],
      showCTA: true,
      ctaText: 'Get Started',
      ctaUrl: '#',
      sticky: true
    },
    defaultStyles: {
      backgroundColor: '#ffffff',
      borderBottom: '1px solid #e5e7eb',
      padding: '16px 24px',
      position: 'sticky',
      top: '0',
      zIndex: '50',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    },
    defaultSize: { width: 1200, height: 80 },
    resizable: false,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'backgroundColor', label: 'Background', type: 'color', defaultValue: '#ffffff' },
          { name: 'borderBottom', label: 'Border', type: 'text', defaultValue: '1px solid #e5e7eb' },
          { name: 'boxShadow', label: 'Shadow', type: 'select', defaultValue: '0 1px 3px rgba(0, 0, 0, 0.1)', options: [
            { label: 'None', value: 'none' },
            { label: 'Small', value: '0 1px 3px rgba(0, 0, 0, 0.1)' },
            { label: 'Medium', value: '0 4px 6px rgba(0, 0, 0, 0.1)' }
          ]}
        ]
      }
    ],
    contentFields: [
      { name: 'logo', label: 'Logo Text', type: 'text', defaultValue: 'Your Logo' },
      { name: 'ctaText', label: 'CTA Button Text', type: 'text', defaultValue: 'Get Started' },
      { name: 'ctaUrl', label: 'CTA Button URL', type: 'text', defaultValue: '#' }
    ]
  },

  footer: {
    type: 'footer' as any,
    name: 'Footer',
    description: 'Website footer with links and info',
    icon: '🦶',
    category: 'Navigation',
    defaultProps: {
      companyName: 'Your Company',
      description: 'Building amazing digital experiences.',
      columns: [
        {
          title: 'Company',
          links: [
            { label: 'About', url: '/about' },
            { label: 'Careers', url: '/careers' },
            { label: 'Contact', url: '/contact' }
          ]
        },
        {
          title: 'Services',
          links: [
            { label: 'Web Design', url: '/services/web-design' },
            { label: 'Development', url: '/services/development' },
            { label: 'Consulting', url: '/services/consulting' }
          ]
        }
      ],
      socialLinks: [
        { platform: 'twitter', url: '#' },
        { platform: 'linkedin', url: '#' },
        { platform: 'github', url: '#' }
      ],
      copyright: '© 2024 Your Company. All rights reserved.'
    },
    defaultStyles: {
      backgroundColor: '#1f2937',
      color: '#ffffff',
      padding: '60px 24px 24px',
      marginTop: 'auto'
    },
    defaultSize: { width: 1200, height: 300 },
    resizable: false,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'backgroundColor', label: 'Background', type: 'color', defaultValue: '#1f2937' },
          { name: 'color', label: 'Text Color', type: 'color', defaultValue: '#ffffff' },
          { name: 'padding', label: 'Padding', type: 'spacing', defaultValue: '60px 24px 24px' }
        ]
      }
    ],
    contentFields: [
      { name: 'companyName', label: 'Company Name', type: 'text', defaultValue: 'Your Company' },
      { name: 'description', label: 'Description', type: 'textarea', defaultValue: 'Building amazing digital experiences.' },
      { name: 'copyright', label: 'Copyright Text', type: 'text', defaultValue: '© 2024 Your Company. All rights reserved.' }
    ]
  },

  // Content Sections
  testimonial: {
    type: 'testimonial' as any,
    name: 'Testimonial',
    description: 'Customer testimonial section',
    icon: '💬',
    category: 'Sections',
    defaultProps: {
      quote: 'This service exceeded our expectations. Highly recommended!',
      author: 'John Doe',
      position: 'CEO',
      company: 'Tech Corp',
      avatar: '/placeholder-avatar.jpg',
      rating: 5,
      showRating: true
    },
    defaultStyles: {
      backgroundColor: '#f9fafb',
      padding: '60px 24px',
      textAlign: 'center',
      borderRadius: '12px'
    },
    defaultSize: { width: 600, height: 300 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'backgroundColor', label: 'Background', type: 'color', defaultValue: '#f9fafb' },
          { name: 'borderRadius', label: 'Border Radius', type: 'slider', defaultValue: 12, min: 0, max: 50, unit: 'px' },
          { name: 'textAlign', label: 'Text Alignment', type: 'select', defaultValue: 'center', options: [
            { label: 'Left', value: 'left' },
            { label: 'Center', value: 'center' },
            { label: 'Right', value: 'right' }
          ]}
        ]
      }
    ],
    contentFields: [
      { name: 'quote', label: 'Quote', type: 'textarea', defaultValue: 'This service exceeded our expectations. Highly recommended!' },
      { name: 'author', label: 'Author Name', type: 'text', defaultValue: 'John Doe' },
      { name: 'position', label: 'Position', type: 'text', defaultValue: 'CEO' },
      { name: 'company', label: 'Company', type: 'text', defaultValue: 'Tech Corp' },
      { name: 'avatar', label: 'Avatar URL', type: 'text', defaultValue: '/placeholder-avatar.jpg' },
      { name: 'rating', label: 'Rating (1-5)', type: 'number', defaultValue: 5 }
    ]
  },

  pricing: {
    type: 'pricing' as any,
    name: 'Pricing Table',
    description: 'Pricing plans comparison table',
    icon: '💰',
    category: 'Sections',
    defaultProps: {
      title: 'Choose Your Plan',
      subtitle: 'Select the perfect plan for your needs',
      plans: [
        {
          name: 'Basic',
          price: '$9',
          period: '/month',
          features: ['Feature 1', 'Feature 2', 'Feature 3'],
          buttonText: 'Get Started',
          buttonUrl: '#',
          popular: false
        },
        {
          name: 'Pro',
          price: '$29',
          period: '/month',
          features: ['Everything in Basic', 'Feature 4', 'Feature 5', 'Feature 6'],
          buttonText: 'Get Started',
          buttonUrl: '#',
          popular: true
        },
        {
          name: 'Enterprise',
          price: '$99',
          period: '/month',
          features: ['Everything in Pro', 'Feature 7', 'Feature 8', 'Priority Support'],
          buttonText: 'Contact Sales',
          buttonUrl: '#',
          popular: false
        }
      ]
    },
    defaultStyles: {
      backgroundColor: '#ffffff',
      padding: '80px 24px',
      textAlign: 'center'
    },
    defaultSize: { width: 900, height: 600 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'backgroundColor', label: 'Background', type: 'color', defaultValue: '#ffffff' },
          { name: 'padding', label: 'Padding', type: 'spacing', defaultValue: '80px 24px' },
          { name: 'textAlign', label: 'Text Alignment', type: 'select', defaultValue: 'center', options: [
            { label: 'Left', value: 'left' },
            { label: 'Center', value: 'center' },
            { label: 'Right', value: 'right' }
          ]}
        ]
      }
    ],
    contentFields: [
      { name: 'title', label: 'Section Title', type: 'text', defaultValue: 'Choose Your Plan' },
      { name: 'subtitle', label: 'Section Subtitle', type: 'text', defaultValue: 'Select the perfect plan for your needs' }
    ]
  },

  features: {
    type: 'features' as any,
    name: 'Features Grid',
    description: 'Grid of features with icons',
    icon: '⭐',
    category: 'Sections',
    defaultProps: {
      title: 'Amazing Features',
      subtitle: 'Everything you need to succeed',
      features: [
        {
          icon: '🚀',
          title: 'Fast Performance',
          description: 'Lightning fast loading times for better user experience.'
        },
        {
          icon: '🔒',
          title: 'Secure',
          description: 'Enterprise-grade security to protect your data.'
        },
        {
          icon: '📱',
          title: 'Mobile Ready',
          description: 'Fully responsive design that works on all devices.'
        },
        {
          icon: '🎨',
          title: 'Customizable',
          description: 'Easy to customize and make it your own.'
        }
      ],
      columns: 2
    },
    defaultStyles: {
      backgroundColor: '#ffffff',
      padding: '80px 24px',
      textAlign: 'center'
    },
    defaultSize: { width: 800, height: 500 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'layout',
        label: 'Layout',
        properties: [
          { name: 'columns', label: 'Columns', type: 'select', defaultValue: 2, options: [
            { label: '1 Column', value: 1 },
            { label: '2 Columns', value: 2 },
            { label: '3 Columns', value: 3 },
            { label: '4 Columns', value: 4 }
          ]}
        ]
      },
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'backgroundColor', label: 'Background', type: 'color', defaultValue: '#ffffff' },
          { name: 'padding', label: 'Padding', type: 'spacing', defaultValue: '80px 24px' }
        ]
      }
    ],
    contentFields: [
      { name: 'title', label: 'Section Title', type: 'text', defaultValue: 'Amazing Features' },
      { name: 'subtitle', label: 'Section Subtitle', type: 'text', defaultValue: 'Everything you need to succeed' }
    ]
  },

  cta: {
    type: 'cta' as any,
    name: 'Call to Action',
    description: 'Call to action section',
    icon: '📢',
    category: 'Sections',
    defaultProps: {
      title: 'Ready to Get Started?',
      subtitle: 'Join thousands of satisfied customers today.',
      primaryButtonText: 'Get Started Now',
      primaryButtonUrl: '#',
      secondaryButtonText: 'Learn More',
      secondaryButtonUrl: '#',
      showSecondaryButton: true,
      backgroundType: 'gradient'
    },
    defaultStyles: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: '#ffffff',
      padding: '80px 24px',
      textAlign: 'center',
      borderRadius: '12px'
    },
    defaultSize: { width: 800, height: 300 },
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
    hasChildren: false,
    styleGroups: [
      {
        name: 'appearance',
        label: 'Appearance',
        properties: [
          { name: 'background', label: 'Background', type: 'text', defaultValue: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
          { name: 'color', label: 'Text Color', type: 'color', defaultValue: '#ffffff' },
          { name: 'borderRadius', label: 'Border Radius', type: 'slider', defaultValue: 12, min: 0, max: 50, unit: 'px' }
        ]
      }
    ],
    contentFields: [
      { name: 'title', label: 'Title', type: 'text', defaultValue: 'Ready to Get Started?' },
      { name: 'subtitle', label: 'Subtitle', type: 'text', defaultValue: 'Join thousands of satisfied customers today.' },
      { name: 'primaryButtonText', label: 'Primary Button Text', type: 'text', defaultValue: 'Get Started Now' },
      { name: 'primaryButtonUrl', label: 'Primary Button URL', type: 'text', defaultValue: '#' },
      { name: 'secondaryButtonText', label: 'Secondary Button Text', type: 'text', defaultValue: 'Learn More' },
      { name: 'secondaryButtonUrl', label: 'Secondary Button URL', type: 'text', defaultValue: '#' }
    ]
  }
}

// Helper functions
export const getComponentConfig = (type: ComponentType): ComponentConfig => {
  return componentRegistry[type]
}

export const getComponentsByCategory = () => {
  const categories: Record<string, ComponentConfig[]> = {}
  
  Object.values(componentRegistry).forEach(config => {
    if (!categories[config.category]) {
      categories[config.category] = []
    }
    categories[config.category].push(config)
  })
  
  return categories
}

export const createDefaultComponent = (type: ComponentType, position: { x: number; y: number }): Omit<import('./store').Component, 'id'> => {
  const config = getComponentConfig(type)
  
  return {
    type,
    name: config.name,
    content: { ...config.defaultProps },
    styles: { ...config.defaultStyles },
    position: {
      ...position,
      width: config.defaultSize.width,
      height: config.defaultSize.height
    },
    order: 0,
    isVisible: true,
    isLocked: false,
    isSelected: false
  }
}
