import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  validateMethod,
  requireAdmin
} from '@/lib/api-utils'
import { createProjectSchema, updateProjectSchema } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/projects - List all public projects with pagination and search
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause for public projects only
  const where: any = {
    ispublic: true,
    status: 'COMPLETED' // Only show completed projects to public
  }

  // Add search functionality
  if (search && search.trim()) {
    const searchQuery = buildSearchQuery(search.trim(), ['name', 'description', 'excerpt', 'tags'])
    Object.assign(where, searchQuery)
  }

  // Add filters
  if (filter) {
    try {
      const filters = JSON.parse(filter)
      if (filters.featured === 'true') where.isfeatured = true
      if (filters.technology) {
        where.technologies = {
          some: {
            name: {
              contains: filters.technology
            }
          }
        }
      }
      if (filters.service) {
        where.services = {
          some: {
            name: {
              contains: filters.service
            }
          }
        }
      }
    } catch (e) {
      // Invalid filter JSON, ignore
    }
  }

  const [projects, total] = await Promise.all([
    prisma.projects.findMany({
      where,
      skip,
      take,
      orderBy: [
        { isfeatured: 'desc' },
        { displayorder: 'asc' },
        { createdat: 'desc' }
      ],
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
          },
        },
        _count: {
          select: {
            projecttechnologies: true,
          },
        },
      },
    }),
    prisma.projects.count({ where }),
  ])

  // Transform the data for frontend and add computed fields
  const transformedProjects = projects.map(project => transformFromDbFields.project(project))

  const enrichedProjects = transformedProjects.map(project => ({
    ...project,
    averageRating: project.feedbacks && project.feedbacks.length > 0
      ? project.feedbacks.reduce((sum: number, feedback: any) => sum + feedback.rating, 0) / project.feedbacks.length
      : null,
    tagsArray: project.tags ? project.tags.split(',').map((tag: string) => tag.trim()) : [],
  }))

  return paginatedResponse(enrichedProjects, page, limit, total)
})


