import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// Data binding types
export interface DataBinding {
  id: string
  componentId: string
  source: string // table name
  field?: string // specific field name
  query?: DataQuery
  transform?: DataTransform
  cache?: CachedData
  lastUpdated: Date
}

export interface DataQuery {
  filters?: Record<string, any>
  sort?: { field: string; direction: 'asc' | 'desc' }
  limit?: number
  offset?: number
}

export interface DataTransform {
  type: 'format' | 'filter' | 'map' | 'aggregate'
  config: Record<string, any>
}

export interface CachedData {
  data: any[]
  timestamp: Date
  ttl: number // time to live in milliseconds
}

export interface DataSource {
  name: string
  displayName: string
  endpoint: string
  fields: DataField[]
  isConnected: boolean
  lastSync?: Date
}

export interface DataField {
  name: string
  type: string
  displayName?: string
  description?: string
  isRequired: boolean
}

// Data binding store
interface DataBindingStore {
  // State
  bindings: DataBinding[]
  dataSources: DataSource[]
  previewData: Record<string, any[]>
  isLoading: boolean
  error: string | null

  // Actions
  addBinding: (binding: Omit<DataBinding, 'id' | 'lastUpdated'>) => void
  removeBinding: (bindingId: string) => void
  updateBinding: (bindingId: string, updates: Partial<DataBinding>) => void
  getBindingsByComponent: (componentId: string) => DataBinding[]
  
  // Data source management
  addDataSource: (source: Omit<DataSource, 'isConnected' | 'lastSync'>) => void
  updateDataSource: (sourceName: string, updates: Partial<DataSource>) => void
  testConnection: (sourceName: string) => Promise<boolean>
  
  // Data fetching
  fetchData: (source: string, query?: DataQuery) => Promise<any[]>
  refreshBinding: (bindingId: string) => Promise<void>
  refreshAllBindings: () => Promise<void>
  
  // Preview data
  setPreviewData: (source: string, data: any[]) => void
  getPreviewData: (source: string) => any[]
  clearPreviewData: (source?: string) => void
  
  // Cache management
  clearCache: (bindingId?: string) => void
  isCacheValid: (binding: DataBinding) => boolean
}

export const useDataBindingStore = create<DataBindingStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      bindings: [],
      dataSources: [
        {
          name: 'services',
          displayName: 'Services',
          endpoint: '/api/services',
          fields: [
            { name: 'id', type: 'number', isRequired: true },
            { name: 'name', type: 'string', displayName: 'Service Name', isRequired: true },
            { name: 'description', type: 'text', displayName: 'Description', isRequired: true },
            { name: 'price', type: 'decimal', displayName: 'Price', isRequired: true },
            { name: 'iconclass', type: 'string', displayName: 'Icon Class', isRequired: false },
            { name: 'isactive', type: 'boolean', displayName: 'Is Active', isRequired: true }
          ],
          isConnected: false
        },
        {
          name: 'projects',
          displayName: 'Projects',
          endpoint: '/api/projects',
          fields: [
            { name: 'id', type: 'number', isRequired: true },
            { name: 'name', type: 'string', displayName: 'Project Name', isRequired: true },
            { name: 'description', type: 'text', displayName: 'Description', isRequired: true },
            { name: 'imageurl', type: 'string', displayName: 'Image URL', isRequired: false },
            { name: 'projecturl', type: 'string', displayName: 'Project URL', isRequired: false },
            { name: 'githuburl', type: 'string', displayName: 'GitHub URL', isRequired: false },
            { name: 'isfeatured', type: 'boolean', displayName: 'Is Featured', isRequired: true },
            { name: 'ispublic', type: 'boolean', displayName: 'Is Public', isRequired: true }
          ],
          isConnected: false
        },
        {
          name: 'teammembers',
          displayName: 'Team Members',
          endpoint: '/api/team-members',
          fields: [
            { name: 'id', type: 'number', isRequired: true },
            { name: 'firstname', type: 'string', displayName: 'First Name', isRequired: true },
            { name: 'lastname', type: 'string', displayName: 'Last Name', isRequired: true },
            { name: 'position', type: 'string', displayName: 'Position', isRequired: true },
            { name: 'email', type: 'string', displayName: 'Email', isRequired: true },
            { name: 'photourl', type: 'string', displayName: 'Photo URL', isRequired: false },
            { name: 'bio', type: 'text', displayName: 'Biography', isRequired: false },
            { name: 'isactive', type: 'boolean', displayName: 'Is Active', isRequired: true }
          ],
          isConnected: false
        },
        {
          name: 'testimonials',
          displayName: 'Testimonials',
          endpoint: '/api/testimonials',
          fields: [
            { name: 'id', type: 'number', isRequired: true },
            { name: 'clientname', type: 'string', displayName: 'Client Name', isRequired: true },
            { name: 'clienttitle', type: 'string', displayName: 'Client Title', isRequired: true },
            { name: 'clientcompany', type: 'string', displayName: 'Client Company', isRequired: true },
            { name: 'content', type: 'text', displayName: 'Testimonial Content', isRequired: true },
            { name: 'rating', type: 'number', displayName: 'Rating', isRequired: true },
            { name: 'clientphotourl', type: 'string', displayName: 'Client Photo URL', isRequired: false },
            { name: 'isfeatured', type: 'boolean', displayName: 'Is Featured', isRequired: true }
          ],
          isConnected: false
        },
        {
          name: 'blogposts',
          displayName: 'Blog Posts',
          endpoint: '/api/blog',
          fields: [
            { name: 'id', type: 'number', isRequired: true },
            { name: 'title', type: 'string', displayName: 'Title', isRequired: true },
            { name: 'content', type: 'text', displayName: 'Content', isRequired: true },
            { name: 'excerpt', type: 'text', displayName: 'Excerpt', isRequired: false },
            { name: 'featuredimageurl', type: 'string', displayName: 'Featured Image URL', isRequired: false },
            { name: 'ispublished', type: 'boolean', displayName: 'Is Published', isRequired: true },
            { name: 'publishedat', type: 'datetime', displayName: 'Published Date', isRequired: false },
            { name: 'categories', type: 'string', displayName: 'Categories', isRequired: false },
            { name: 'tags', type: 'string', displayName: 'Tags', isRequired: false }
          ],
          isConnected: false
        }
      ],
      previewData: {},
      isLoading: false,
      error: null,

      // Binding management
      addBinding: (bindingData) => {
        const binding: DataBinding = {
          ...bindingData,
          id: `binding_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          lastUpdated: new Date()
        }
        
        set((state) => ({
          bindings: [...state.bindings, binding]
        }))
      },

      removeBinding: (bindingId) => {
        set((state) => ({
          bindings: state.bindings.filter(b => b.id !== bindingId)
        }))
      },

      updateBinding: (bindingId, updates) => {
        set((state) => ({
          bindings: state.bindings.map(b =>
            b.id === bindingId
              ? { ...b, ...updates, lastUpdated: new Date() }
              : b
          )
        }))
      },

      getBindingsByComponent: (componentId) => {
        return get().bindings.filter(b => b.componentId === componentId)
      },

      // Data source management
      addDataSource: (sourceData) => {
        const source: DataSource = {
          ...sourceData,
          isConnected: false
        }
        
        set((state) => ({
          dataSources: [...state.dataSources, source]
        }))
      },

      updateDataSource: (sourceName, updates) => {
        set((state) => ({
          dataSources: state.dataSources.map(s =>
            s.name === sourceName
              ? { ...s, ...updates, lastSync: new Date() }
              : s
          )
        }))
      },

      testConnection: async (sourceName) => {
        const source = get().dataSources.find(s => s.name === sourceName)
        if (!source) return false

        try {
          const response = await fetch(source.endpoint)
          const isConnected = response.ok
          
          get().updateDataSource(sourceName, { isConnected })
          return isConnected
        } catch (error) {
          get().updateDataSource(sourceName, { isConnected: false })
          return false
        }
      },

      // Data fetching
      fetchData: async (source, query) => {
        const dataSource = get().dataSources.find(s => s.name === source)
        if (!dataSource) throw new Error(`Data source '${source}' not found`)

        set({ isLoading: true, error: null })

        try {
          let url = dataSource.endpoint
          if (query) {
            const params = new URLSearchParams()
            if (query.limit) params.append('limit', query.limit.toString())
            if (query.offset) params.append('offset', query.offset.toString())
            if (query.sort) params.append('sort', `${query.sort.field}:${query.sort.direction}`)
            if (query.filters) {
              Object.entries(query.filters).forEach(([key, value]) => {
                params.append(key, String(value))
              })
            }
            if (params.toString()) url += `?${params.toString()}`
          }

          const response = await fetch(url)
          if (!response.ok) throw new Error(`Failed to fetch data from ${source}`)

          const data = await response.json()
          
          // Update preview data
          get().setPreviewData(source, Array.isArray(data) ? data : [data])
          
          set({ isLoading: false })
          return Array.isArray(data) ? data : [data]
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Unknown error' 
          })
          throw error
        }
      },

      refreshBinding: async (bindingId) => {
        const binding = get().bindings.find(b => b.id === bindingId)
        if (!binding) return

        try {
          const data = await get().fetchData(binding.source, binding.query)
          
          // Update binding cache
          get().updateBinding(bindingId, {
            cache: {
              data,
              timestamp: new Date(),
              ttl: 5 * 60 * 1000 // 5 minutes
            }
          })
        } catch (error) {
          console.error('Failed to refresh binding:', error)
        }
      },

      refreshAllBindings: async () => {
        const bindings = get().bindings
        await Promise.all(bindings.map(b => get().refreshBinding(b.id)))
      },

      // Preview data management
      setPreviewData: (source, data) => {
        set((state) => ({
          previewData: {
            ...state.previewData,
            [source]: data
          }
        }))
      },

      getPreviewData: (source) => {
        return get().previewData[source] || []
      },

      clearPreviewData: (source) => {
        if (source) {
          set((state) => {
            const newPreviewData = { ...state.previewData }
            delete newPreviewData[source]
            return { previewData: newPreviewData }
          })
        } else {
          set({ previewData: {} })
        }
      },

      // Cache management
      clearCache: (bindingId) => {
        if (bindingId) {
          get().updateBinding(bindingId, { cache: undefined })
        } else {
          set((state) => ({
            bindings: state.bindings.map(b => ({ ...b, cache: undefined }))
          }))
        }
      },

      isCacheValid: (binding) => {
        if (!binding.cache) return false
        const now = new Date().getTime()
        const cacheTime = binding.cache.timestamp.getTime()
        return (now - cacheTime) < binding.cache.ttl
      }
    }),
    {
      name: 'data-binding-store'
    }
  )
)
