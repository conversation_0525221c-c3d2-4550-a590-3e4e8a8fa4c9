'use client';

import { TeamMembersManager } from '@/components/admin/team-members/team-members-manager';
import { CrudConfig } from '@/components/admin/crud/types';

interface TeamMember {
  id: number
  name: string
  position: string
  email?: string
  phone: string
  birthDate?: string
  gender?: string
  maritalStatus?: string
  socialSecurityNo?: string
  hireDate?: string
  address?: string
  city?: string
  state?: string
  zipCode?: string
  country?: string
  salary?: number
  payrollMethod?: string
  resumeUrl?: string
  notes?: string
  bio?: string
  photoUrl?: string
  linkedinUrl?: string
  twitterUrl?: string
  githubUrl?: string
  displayOrder: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  projects?: any[]
  payrollRecords?: any[]
  tasks?: any[]
  _count?: {
    projects: number
    payrollRecords: number
    tasks: number
  }
}

const teamMemberConfig: CrudConfig<TeamMember> = {
  title: 'Team Members',
  description: 'Manage your team members, roles, and information',
  endpoint: 'team-members', // API endpoint

  columns: [
    {
      key: 'name',
      label: 'Name',
      sortable: true,
      searchable: true,
      renderType: 'text',
      width: '200px',
      hideable: false, // Always visible
      defaultVisible: true
    },
    {
      key: 'position',
      label: 'Position',
      sortable: true,
      searchable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'email',
      label: 'Email',
      sortable: true,
      searchable: true,
      renderType: 'email',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'phone',
      label: 'Phone',
      sortable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'hireDate',
      label: 'Hire Date',
      sortable: true,
      renderType: 'date',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'salary',
      label: 'Salary',
      sortable: true,
      renderType: 'currency',
      hideable: true,
      defaultVisible: false
    },
    {
      key: 'city',
      label: 'City',
      sortable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: false
    },
    {
      key: 'updatedAt',
      label: 'Last Active',
      sortable: true,
      renderType: 'date',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'isActive',
      label: 'Status',
      sortable: true,
      renderType: 'status',
      renderProps: {
        trueLabel: 'Active',
        falseLabel: 'Inactive',
        statusColors: {
          true: 'bg-green-100 text-green-800',
          false: 'bg-red-100 text-red-800'
        }
      },
      hideable: true,
      defaultVisible: true
    }
  ],

  // Action buttons for each row
  actions: [
    {
      action: 'view',
      label: 'View',
      icon: 'EyeIcon',
      variant: 'secondary',
      tooltip: 'View team member details'
    },
    {
      action: 'edit',
      label: 'Edit',
      icon: 'PencilIcon',
      variant: 'primary',
      tooltip: 'Edit team member'
    },
    {
      action: 'toggle-status',
      label: 'Toggle Status',
      icon: 'PowerIcon',
      variant: 'warning',
      tooltip: 'Activate/Deactivate team member'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      tooltip: 'Delete team member'
    }
  ],



  fields: [
    {
      key: 'name',
      label: 'Full Name',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'Enter full name'
    },
    {
      key: 'position',
      label: 'Position',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'e.g., Senior Developer'
    },
    {
      key: 'email',
      label: 'Email',
      type: 'email',
      searchable: true,
      placeholder: '<EMAIL>'
    },
    {
      key: 'phone',
      label: 'Phone',
      type: 'text',
      required: true,
      searchable: false,
      placeholder: '+****************'
    },
    {
      key: 'birthDate',
      label: 'Birth Date',
      type: 'date',
      searchable: false,
    },
    {
      key: 'gender',
      label: 'Gender',
      type: 'select',
      options: [
        { value: '', label: 'Select Gender' },
        { value: 'Male', label: 'Male' },
        { value: 'Female', label: 'Female' },
        { value: 'Other', label: 'Other' },
      ],
      searchable: false,
    },
    {
      key: 'maritalStatus',
      label: 'Marital Status',
      type: 'select',
      options: [
        { value: '', label: 'Select Status' },
        { value: 'Single', label: 'Single' },
        { value: 'Married', label: 'Married' },
        { value: 'Divorced', label: 'Divorced' },
        { value: 'Widowed', label: 'Widowed' },
      ],
      searchable: false,
    },
    {
      key: 'socialSecurityNo',
      label: 'SSN',
      type: 'text',
      searchable: false,
      placeholder: 'XXX-XX-XXXX'
    },
    {
      key: 'hireDate',
      label: 'Hire Date',
      type: 'date',
      searchable: false,
      required: true
    },
    {
      key: 'address',
      label: 'Address',
      type: 'text',
      searchable: false,
      placeholder: '123 Main Street'
    },
    {
      key: 'city',
      label: 'City',
      type: 'text',
      searchable: false,
      placeholder: 'New York'
    },
    {
      key: 'state',
      label: 'State',
      type: 'text',
      searchable: false,
      placeholder: 'NY'
    },
    {
      key: 'zipCode',
      label: 'ZIP Code',
      type: 'text',
      searchable: false,
      placeholder: '10001'
    },
    {
      key: 'country',
      label: 'Country',
      type: 'text',
      searchable: false,
      placeholder: 'United States',
      defaultValue: 'United States'
    },
    {
      key: 'salary',
      label: 'Annual Salary',
      type: 'number',
      searchable: false,
      placeholder: '75000'
    },
    {
      key: 'payrollMethod',
      label: 'Payroll Method',
      type: 'select',
      options: [
        { value: '', label: 'Select Method' },
        { value: 'Direct Deposit', label: 'Direct Deposit' },
        { value: 'Check', label: 'Check' },
        { value: 'Cash', label: 'Cash' },
      ],
      searchable: false,
      defaultValue: 'Direct Deposit'
    },
    {
      key: 'resumeUrl',
      label: 'Resume File',
      type: 'url',
      searchable: false,
      placeholder: 'Enter file path or click Upload to select resume'
    },
    {
      key: 'notes',
      label: 'Internal Notes',
      type: 'textarea',
      searchable: true,
      placeholder: 'Internal notes about this team member...',
      rows: 3
    },
    {
      key: 'bio',
      label: 'Public Bio',
      type: 'textarea',
      searchable: true,
      placeholder: 'Brief professional biography for public display...',
      rows: 3
    },
    {
      key: 'photoUrl',
      label: 'Profile Photo',
      type: 'url',
      searchable: false,
      placeholder: 'Enter photo path or click Upload to select file'
    },
    {
      key: 'linkedinUrl',
      label: 'LinkedIn',
      type: 'url',
      searchable: false,
      placeholder: 'https://linkedin.com/in/username'
    },
    {
      key: 'githubUrl',
      label: 'GitHub',
      type: 'url',
      searchable: false,
      placeholder: 'https://github.com/username'
    },
    {
      key: 'displayOrder',
      label: 'Display Order',
      type: 'number',
      defaultValue: 0,
      searchable: false,
      placeholder: '0'
    },
    {
      key: 'isActive',
      label: 'Active Status',
      type: 'boolean',
      defaultValue: true,
      searchable: false,
    },
  ],

  filters: [
    {
      key: 'position',
      label: 'Position',
      type: 'select',
      options: [
        { value: '', label: 'All Positions' },
        { value: 'CEO', label: 'CEO' },
        { value: 'CTO', label: 'CTO' },
        { value: 'Developer', label: 'Developer' },
        { value: 'Designer', label: 'Designer' },
        { value: 'Manager', label: 'Manager' },
        { value: 'Analyst', label: 'Analyst' },
      ],
    },
    {
      key: 'isActive',
      label: 'Status',
      type: 'select',
      options: [
        { value: '', label: 'All' },
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' },
      ],
    },
  ],

  bulkActions: [
    {
      label: 'Activate Selected',
      action: 'activate',
      variant: 'success'
    },
    {
      label: 'Deactivate Selected',
      action: 'deactivate',
      variant: 'warning'
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search team members by name, email, position...',
  defaultSort: { field: 'updatedAt', direction: 'desc' }, // Sort by Last Active (most recent)
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['name', 'position', 'email', 'phone', 'hireDate', 'updatedAt', 'isActive']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 3,
    sections: [
      {
        title: 'Basic Information',
        fields: ['name', 'position', 'email', 'phone']
      },
      {
        title: 'Personal Details',
        fields: ['birthDate', 'gender', 'maritalStatus', 'socialSecurityNo']
      },
      {
        title: 'Address Information',
        fields: ['address', 'city', 'state', 'zipCode', 'country']
      },
      {
        title: 'Employment Details',
        fields: ['hireDate', 'salary', 'payrollMethod']
      },
      {
        title: 'Profile & Documents',
        fields: ['photoUrl', 'resumeUrl']
      },
      {
        title: 'Social Media Links',
        fields: ['linkedinUrl', 'githubUrl']
      },
      {
        title: 'Additional Information',
        fields: ['bio', 'notes', 'displayOrder', 'isActive']
      }
    ]
  }
};

export default function TeamMembersPage() {
  return <TeamMembersManager config={teamMemberConfig} />;
}

