import { ComponentType } from './store'

// Component category definitions
export interface ComponentCategory {
  id: string
  name: string
  description: string
  icon: string
  color: string
  components: ComponentType[]
  isAdvanced?: boolean
}

// Enhanced component categorization
export const componentCategories: ComponentCategory[] = [
  {
    id: 'basic',
    name: 'Basic Elements',
    description: 'Essential building blocks for any page',
    icon: '🧱',
    color: '#3b82f6',
    components: ['text', 'image', 'button', 'spacer', 'divider'],
    isAdvanced: false
  },
  {
    id: 'layout',
    name: 'Layout & Structure',
    description: 'Organize and structure your content',
    icon: '📐',
    color: '#8b5cf6',
    components: ['container', 'grid', 'column', 'row'],
    isAdvanced: false
  },
  {
    id: 'content',
    name: 'Content Blocks',
    description: 'Rich content components',
    icon: '📄',
    color: '#10b981',
    components: ['card', 'list', 'blog'],
    isAdvanced: false
  },
  {
    id: 'media',
    name: 'Media & Visuals',
    description: 'Images, videos, and galleries',
    icon: '🎬',
    color: '#f59e0b',
    components: ['image', 'video', 'embed', 'gallery'],
    isAdvanced: false
  },
  {
    id: 'interactive',
    name: 'Interactive Elements',
    description: 'Forms and user interactions',
    icon: '⚡',
    color: '#ef4444',
    components: ['button', 'form', 'newsletter'],
    isAdvanced: false
  },
  {
    id: 'navigation',
    name: 'Navigation',
    description: 'Headers, footers, and navigation',
    icon: '🧭',
    color: '#6366f1',
    components: ['navbar', 'footer'],
    isAdvanced: true
  },
  {
    id: 'sections',
    name: 'Page Sections',
    description: 'Complete page sections and layouts',
    icon: '🏗️',
    color: '#ec4899',
    components: ['hero', 'features', 'testimonial', 'pricing', 'cta', 'stats', 'team'],
    isAdvanced: true
  }
]

// Helper functions
export const getCategoryById = (id: string): ComponentCategory | undefined => {
  return componentCategories.find(cat => cat.id === id)
}

export const getCategoryByComponent = (componentType: ComponentType): ComponentCategory | undefined => {
  return componentCategories.find(cat => cat.components.includes(componentType))
}

export const getComponentsByCategory = (categoryId: string): ComponentType[] => {
  const category = getCategoryById(categoryId)
  return category ? category.components : []
}

export const getBasicCategories = (): ComponentCategory[] => {
  return componentCategories.filter(cat => !cat.isAdvanced)
}

export const getAdvancedCategories = (): ComponentCategory[] => {
  return componentCategories.filter(cat => cat.isAdvanced)
}

export const getAllComponents = (): ComponentType[] => {
  return componentCategories.reduce((acc, cat) => [...acc, ...cat.components], [] as ComponentType[])
}

// Component difficulty levels
export enum ComponentDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

// Component metadata for enhanced organization
export interface ComponentMetadata {
  type: ComponentType
  difficulty: ComponentDifficulty
  tags: string[]
  useCases: string[]
  dependencies?: ComponentType[]
  isResponsive: boolean
  hasDataBinding: boolean
  customizable: boolean
}

export const componentMetadata: Record<ComponentType, ComponentMetadata> = {
  // Basic Elements
  text: {
    type: 'text',
    difficulty: ComponentDifficulty.BEGINNER,
    tags: ['typography', 'content', 'basic'],
    useCases: ['headings', 'paragraphs', 'labels'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },
  image: {
    type: 'image',
    difficulty: ComponentDifficulty.BEGINNER,
    tags: ['media', 'visual', 'basic'],
    useCases: ['photos', 'illustrations', 'logos'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },
  button: {
    type: 'button',
    difficulty: ComponentDifficulty.BEGINNER,
    tags: ['interactive', 'action', 'basic'],
    useCases: ['call-to-action', 'navigation', 'forms'],
    isResponsive: true,
    hasDataBinding: false,
    customizable: true
  },
  spacer: {
    type: 'spacer',
    difficulty: ComponentDifficulty.BEGINNER,
    tags: ['layout', 'spacing', 'utility'],
    useCases: ['spacing', 'layout', 'separation'],
    isResponsive: true,
    hasDataBinding: false,
    customizable: true
  },
  divider: {
    type: 'divider',
    difficulty: ComponentDifficulty.BEGINNER,
    tags: ['layout', 'separator', 'utility'],
    useCases: ['section separation', 'visual break'],
    isResponsive: true,
    hasDataBinding: false,
    customizable: true
  },

  // Layout Components
  container: {
    type: 'container',
    difficulty: ComponentDifficulty.INTERMEDIATE,
    tags: ['layout', 'wrapper', 'structure'],
    useCases: ['content wrapping', 'layout structure'],
    isResponsive: true,
    hasDataBinding: false,
    customizable: true
  },
  grid: {
    type: 'grid',
    difficulty: ComponentDifficulty.INTERMEDIATE,
    tags: ['layout', 'grid', 'responsive'],
    useCases: ['complex layouts', 'card grids', 'galleries'],
    isResponsive: true,
    hasDataBinding: false,
    customizable: true
  },
  column: {
    type: 'column',
    difficulty: ComponentDifficulty.INTERMEDIATE,
    tags: ['layout', 'flexbox', 'vertical'],
    useCases: ['vertical layouts', 'stacking content'],
    isResponsive: true,
    hasDataBinding: false,
    customizable: true
  },
  row: {
    type: 'row',
    difficulty: ComponentDifficulty.INTERMEDIATE,
    tags: ['layout', 'flexbox', 'horizontal'],
    useCases: ['horizontal layouts', 'side-by-side content'],
    isResponsive: true,
    hasDataBinding: false,
    customizable: true
  },

  // Content Components
  card: {
    type: 'card',
    difficulty: ComponentDifficulty.INTERMEDIATE,
    tags: ['content', 'container', 'modern'],
    useCases: ['product cards', 'feature highlights', 'content blocks'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },
  list: {
    type: 'list',
    difficulty: ComponentDifficulty.BEGINNER,
    tags: ['content', 'text', 'organization'],
    useCases: ['feature lists', 'navigation', 'content organization'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },
  blog: {
    type: 'blog',
    difficulty: ComponentDifficulty.ADVANCED,
    tags: ['content', 'dynamic', 'cms'],
    useCases: ['blog sections', 'news', 'articles'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },

  // Media Components
  video: {
    type: 'video',
    difficulty: ComponentDifficulty.INTERMEDIATE,
    tags: ['media', 'video', 'multimedia'],
    useCases: ['video content', 'tutorials', 'presentations'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },
  embed: {
    type: 'embed',
    difficulty: ComponentDifficulty.INTERMEDIATE,
    tags: ['media', 'external', 'integration'],
    useCases: ['YouTube videos', 'maps', 'external content'],
    isResponsive: true,
    hasDataBinding: false,
    customizable: true
  },
  gallery: {
    type: 'gallery',
    difficulty: ComponentDifficulty.ADVANCED,
    tags: ['media', 'images', 'showcase'],
    useCases: ['photo galleries', 'portfolios', 'image showcases'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },

  // Interactive Components
  form: {
    type: 'form',
    difficulty: ComponentDifficulty.ADVANCED,
    tags: ['interactive', 'input', 'contact'],
    useCases: ['contact forms', 'surveys', 'data collection'],
    isResponsive: true,
    hasDataBinding: false,
    customizable: true
  },
  newsletter: {
    type: 'newsletter',
    difficulty: ComponentDifficulty.INTERMEDIATE,
    tags: ['interactive', 'email', 'subscription'],
    useCases: ['email signup', 'newsletters', 'marketing'],
    isResponsive: true,
    hasDataBinding: false,
    customizable: true
  },

  // Navigation Components
  navbar: {
    type: 'navbar',
    difficulty: ComponentDifficulty.ADVANCED,
    tags: ['navigation', 'header', 'menu'],
    useCases: ['site navigation', 'headers', 'menus'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },
  footer: {
    type: 'footer',
    difficulty: ComponentDifficulty.ADVANCED,
    tags: ['navigation', 'footer', 'links'],
    useCases: ['site footer', 'contact info', 'links'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },

  // Section Components
  hero: {
    type: 'hero',
    difficulty: ComponentDifficulty.INTERMEDIATE,
    tags: ['section', 'hero', 'landing'],
    useCases: ['landing pages', 'page headers', 'introductions'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },
  features: {
    type: 'features',
    difficulty: ComponentDifficulty.ADVANCED,
    tags: ['section', 'features', 'grid'],
    useCases: ['feature showcases', 'service listings', 'benefits'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },
  testimonial: {
    type: 'testimonial',
    difficulty: ComponentDifficulty.INTERMEDIATE,
    tags: ['section', 'social proof', 'reviews'],
    useCases: ['customer reviews', 'testimonials', 'social proof'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },
  pricing: {
    type: 'pricing',
    difficulty: ComponentDifficulty.ADVANCED,
    tags: ['section', 'pricing', 'plans'],
    useCases: ['pricing tables', 'subscription plans', 'packages'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },
  cta: {
    type: 'cta',
    difficulty: ComponentDifficulty.INTERMEDIATE,
    tags: ['section', 'call-to-action', 'conversion'],
    useCases: ['call-to-action', 'conversion', 'signup'],
    isResponsive: true,
    hasDataBinding: false,
    customizable: true
  },
  stats: {
    type: 'stats',
    difficulty: ComponentDifficulty.INTERMEDIATE,
    tags: ['section', 'statistics', 'numbers'],
    useCases: ['statistics', 'achievements', 'metrics'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  },
  team: {
    type: 'team',
    difficulty: ComponentDifficulty.ADVANCED,
    tags: ['section', 'team', 'people'],
    useCases: ['team pages', 'about us', 'staff'],
    isResponsive: true,
    hasDataBinding: true,
    customizable: true
  }
}

// Get components by difficulty
export const getComponentsByDifficulty = (difficulty: ComponentDifficulty): ComponentType[] => {
  return Object.values(componentMetadata)
    .filter(meta => meta.difficulty === difficulty)
    .map(meta => meta.type)
}

// Get components with data binding support
export const getDataBindingComponents = (): ComponentType[] => {
  return Object.values(componentMetadata)
    .filter(meta => meta.hasDataBinding)
    .map(meta => meta.type)
}

// Search components by tags
export const searchComponentsByTags = (tags: string[]): ComponentType[] => {
  return Object.values(componentMetadata)
    .filter(meta => tags.some(tag => meta.tags.includes(tag)))
    .map(meta => meta.type)
}
