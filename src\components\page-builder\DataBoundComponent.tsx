'use client'

import React, { useEffect, useState } from 'react'
import { useDataBindingStore } from '@/lib/page-builder/data-binding-store'
import { Component } from '@/lib/page-builder/store'

interface DataBoundComponentProps {
  component: Component
  children?: React.ReactNode
}

interface DataTemplate {
  template: string
  fields: string[]
}

const DataBoundComponent: React.FC<DataBoundComponentProps> = ({ component, children }) => {
  const [boundData, setBoundData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const { getBindingsByComponent, fetchData, isCacheValid } = useDataBindingStore()
  
  // Get bindings for this component
  const bindings = getBindingsByComponent(component.id)
  
  useEffect(() => {
    const loadBoundData = async () => {
      if (bindings.length === 0) return
      
      setIsLoading(true)
      setError(null)
      
      try {
        const dataPromises = bindings.map(async (binding) => {
          // Check if we have valid cached data
          if (binding.cache && isCacheValid(binding)) {
            return binding.cache.data
          }
          
          // Fetch fresh data
          const data = await fetchData(binding.source, binding.query)
          return data
        })
        
        const results = await Promise.all(dataPromises)
        const combinedData = results.flat()
        setBoundData(combinedData)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data')
        console.error('Error loading bound data:', err)
      } finally {
        setIsLoading(false)
      }
    }
    
    loadBoundData()
  }, [bindings, fetchData, isCacheValid])
  
  // Template rendering functions
  const renderTemplate = (template: string, data: any): string => {
    return template.replace(/\{\{(\w+)\}\}/g, (match, field) => {
      return data[field] || match
    })
  }
  
  const extractFields = (template: string): string[] => {
    const matches = template.match(/\{\{(\w+)\}\}/g)
    return matches ? matches.map(match => match.replace(/[{}]/g, '')) : []
  }
  
  // Component-specific data rendering
  const renderDataBoundContent = () => {
    if (bindings.length === 0) {
      return children
    }
    
    if (isLoading) {
      return (
        <div className="flex items-center justify-center p-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-sm text-gray-600">Loading data...</span>
        </div>
      )
    }
    
    if (error) {
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="text-sm text-red-600">
            <strong>Data Error:</strong> {error}
          </div>
        </div>
      )
    }
    
    if (boundData.length === 0) {
      return (
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="text-sm text-yellow-600">
            No data available for this component
          </div>
        </div>
      )
    }
    
    // Render based on component type
    switch (component.type) {
      case 'text':
        return renderTextComponent()
      case 'card':
        return renderCardComponent()
      case 'list':
        return renderListComponent()
      case 'testimonial':
        return renderTestimonialComponent()
      case 'team':
        return renderTeamComponent()
      case 'blog':
        return renderBlogComponent()
      case 'stats':
        return renderStatsComponent()
      case 'features':
        return renderFeaturesComponent()
      default:
        return children
    }
  }
  
  const renderTextComponent = () => {
    const binding = bindings[0]
    if (!binding.field) return children
    
    const data = boundData[0]
    if (!data) return children
    
    const value = data[binding.field]
    return <span>{value}</span>
  }
  
  const renderCardComponent = () => {
    const data = boundData[0]
    if (!data) return children
    
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold mb-2">{data.name || data.title}</h3>
        <p className="text-gray-600 mb-4">{data.description || data.content}</p>
        {data.imageurl && (
          <img 
            src={data.imageurl} 
            alt={data.name || data.title}
            className="w-full h-48 object-cover rounded-lg"
          />
        )}
        {(data.price || data.rating) && (
          <div className="mt-4 flex items-center justify-between">
            {data.price && <span className="text-lg font-bold text-green-600">${data.price}</span>}
            {data.rating && (
              <div className="flex items-center">
                <span className="text-yellow-500">★</span>
                <span className="ml-1 text-sm text-gray-600">{data.rating}</span>
              </div>
            )}
          </div>
        )}
      </div>
    )
  }
  
  const renderListComponent = () => {
    return (
      <ul className="space-y-2">
        {boundData.slice(0, 5).map((item, index) => (
          <li key={index} className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
            <span>{item.name || item.title || item.content}</span>
          </li>
        ))}
      </ul>
    )
  }
  
  const renderTestimonialComponent = () => {
    const data = boundData[0]
    if (!data) return children
    
    return (
      <div className="bg-gray-50 rounded-lg p-6 text-center">
        <blockquote className="text-lg italic text-gray-700 mb-4">
          "{data.content}"
        </blockquote>
        <div className="flex items-center justify-center space-x-4">
          {data.clientphotourl && (
            <img 
              src={data.clientphotourl} 
              alt={data.clientname}
              className="w-12 h-12 rounded-full object-cover"
            />
          )}
          <div>
            <div className="font-semibold text-gray-900">{data.clientname}</div>
            <div className="text-sm text-gray-600">{data.clienttitle}</div>
            {data.clientcompany && (
              <div className="text-sm text-gray-500">{data.clientcompany}</div>
            )}
          </div>
        </div>
        {data.rating && (
          <div className="mt-4 flex justify-center">
            {Array.from({ length: 5 }, (_, i) => (
              <span 
                key={i} 
                className={`text-lg ${i < data.rating ? 'text-yellow-500' : 'text-gray-300'}`}
              >
                ★
              </span>
            ))}
          </div>
        )}
      </div>
    )
  }
  
  const renderTeamComponent = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {boundData.slice(0, 6).map((member, index) => (
          <div key={index} className="bg-white rounded-lg shadow-md p-6 text-center">
            {member.photourl && (
              <img 
                src={member.photourl} 
                alt={`${member.firstname} ${member.lastname}`}
                className="w-20 h-20 rounded-full object-cover mx-auto mb-4"
              />
            )}
            <h3 className="text-lg font-semibold text-gray-900">
              {member.firstname} {member.lastname}
            </h3>
            <p className="text-blue-600 text-sm mb-2">{member.position}</p>
            {member.bio && (
              <p className="text-gray-600 text-sm">{member.bio}</p>
            )}
          </div>
        ))}
      </div>
    )
  }
  
  const renderBlogComponent = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {boundData.slice(0, 6).map((post, index) => (
          <article key={index} className="bg-white rounded-lg shadow-md overflow-hidden">
            {post.featuredimageurl && (
              <img 
                src={post.featuredimageurl} 
                alt={post.title}
                className="w-full h-48 object-cover"
              />
            )}
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{post.title}</h3>
              {post.excerpt && (
                <p className="text-gray-600 text-sm mb-4">{post.excerpt}</p>
              )}
              <div className="flex items-center justify-between text-sm text-gray-500">
                {post.publishedat && (
                  <span>{new Date(post.publishedat).toLocaleDateString()}</span>
                )}
                {post.categories && (
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                    {post.categories}
                  </span>
                )}
              </div>
            </div>
          </article>
        ))}
      </div>
    )
  }
  
  const renderStatsComponent = () => {
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {boundData.slice(0, 4).map((stat, index) => (
          <div key={index} className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {stat.number || stat.value || stat.count}
            </div>
            <div className="text-gray-600 text-sm">
              {stat.label || stat.name || stat.title}
            </div>
          </div>
        ))}
      </div>
    )
  }
  
  const renderFeaturesComponent = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {boundData.slice(0, 6).map((feature, index) => (
          <div key={index} className="text-center p-6">
            {feature.iconclass && (
              <div className="text-4xl mb-4">
                <i className={feature.iconclass}></i>
              </div>
            )}
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {feature.name || feature.title}
            </h3>
            <p className="text-gray-600 text-sm">
              {feature.description}
            </p>
          </div>
        ))}
      </div>
    )
  }
  
  return (
    <div className="data-bound-component">
      {renderDataBoundContent()}
    </div>
  )
}

export default DataBoundComponent
