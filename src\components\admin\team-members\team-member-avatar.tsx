'use client'

import React, { useState } from 'react'
import { UserIcon } from '@heroicons/react/24/outline'

interface TeamMemberAvatarProps {
  name: string
  photoUrl?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full-height'
  className?: string
  style?: React.CSSProperties
}

const sizeClasses = {
  sm: 'w-8 h-8 text-xs',
  md: 'w-10 h-10 text-sm',
  lg: 'w-16 h-16 text-xl',
  xl: 'w-20 h-20 text-2xl',
  '2xl': 'w-24 h-24 text-3xl',
  '3xl': 'w-32 h-32 text-4xl',
  'full-height': 'w-40 h-full text-5xl'
}

export function TeamMemberAvatar({
  name,
  photoUrl,
  size = 'md',
  className = '',
  style = {}
}: TeamMemberAvatarProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  const initials = name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)

  const handleImageLoad = () => {
    setImageLoading(false)
  }

  const handleImageError = () => {
    setImageError(true)
    setImageLoading(false)
  }

  // Handle full-height size differently
  const isFullHeight = size === 'full-height'
  const borderRadius = isFullHeight ? 'rounded-xl' : 'rounded-full'

  // Show photo if available and not errored
  if (photoUrl && !imageError) {
    return (
      <div className={`relative ${sizeClasses[size]} ${className}`} style={style}>
        {imageLoading && (
          <div className={`absolute inset-0 bg-blue-600 ${borderRadius} flex items-center justify-center text-white font-semibold ${sizeClasses[size]}`}>
            {initials}
          </div>
        )}
        <img
          src={photoUrl}
          alt={`${name}'s profile`}
          className={`${sizeClasses[size]} ${borderRadius} object-cover border-4 border-white shadow-lg ${imageLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={style}
        />
      </div>
    )
  }

  // Fallback to initials
  return (
    <div className={`${sizeClasses[size]} bg-blue-600 ${borderRadius} flex items-center justify-center text-white font-semibold ${className}`} style={style}>
      {initials || <UserIcon className="w-1/2 h-1/2" />}
    </div>
  )
}
