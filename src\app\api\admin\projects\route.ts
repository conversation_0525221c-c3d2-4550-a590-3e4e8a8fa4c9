import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  paginatedResponse,
  successResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/projects - List all projects with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search && search.trim()) {
    const searchTerm = search.trim()
    where.OR = [
      { name: { contains: searchTerm, mode: 'insensitive' } },
      { description: { contains: searchTerm, mode: 'insensitive' } },
      { projgoals: { contains: searchTerm, mode: 'insensitive' } },
      { tags: { contains: searchTerm, mode: 'insensitive' } },
      {
        clients: {
          companyname: { contains: searchTerm, mode: 'insensitive' }
        }
      },
      {
        clients: {
          contactname: { contains: searchTerm, mode: 'insensitive' }
        }
      }
    ]
  }

  // Add filters
  if (filter) {
    try {
      const filters = JSON.parse(filter)
      if (filters.status) where.status = filters.status
      if (filters.isfeatured !== undefined) where.isfeatured = filters.isfeatured === 'true'
      if (filters.ispublic !== undefined) where.ispublic = filters.ispublic === 'true'
      if (filters.clientid) where.clientid = filters.clientid
    } catch (e) {
      // Invalid filter JSON, ignore
    }
  }

  // Build orderBy clause
  const orderBy: any = {}
  if (sortBy) {
    orderBy[sortBy] = sortOrder || 'asc'
  } else {
    orderBy.createdat = 'desc' // Default sort
  }

  const [projects, total] = await Promise.all([
    prisma.projects.findMany({
      where,
      skip,
      take,
      orderBy,
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true,
          },
        },
        orders: {
          select: {
            id: true,
            ordertitle: true,
            status: true,
            ordertotalamount: true,
          },
        },
        teammembers: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            tasks: true,
            projectdocuments: true,
            messages: true,
          },
        },
      },
    }),
    prisma.projects.count({ where }),
  ])

  return paginatedResponse(projects, total, page, limit)
})

// POST /api/admin/projects - Create a new project
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.project.create)
  const data = await validate(request)

  // Validate order exists
  const order = await prisma.orders.findUnique({
    where: { id: data.orderid },
  })

  if (!order) {
    throw new Error('Order not found')
  }

  // Validate client exists if provided
  if (data.clientid) {
    const client = await prisma.clients.findUnique({
      where: { id: data.clientid },
    })

    if (!client) {
      throw new Error('Client not found')
    }
  }

  // Prepare project data
  const projectData = {
    ...data,
  }

  const project = await prisma.projects.create({
    data: projectData,
    include: {
      clients: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
          contactemail: true,
        },
      },
      orders: {
        select: {
          id: true,
          ordertitle: true,
          status: true,
          ordertotalamount: true,
        },
      },
      teammembers: {
        select: {
          id: true,
          name: true,
        },
      },
      _count: {
        select: {
          tasks: true,
          projectdocuments: true,
          messages: true,
        },
      },
    },
  })

  return successResponse(project, 'Project created successfully', 201)
})
