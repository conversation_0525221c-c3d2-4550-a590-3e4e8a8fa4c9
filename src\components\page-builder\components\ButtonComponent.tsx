'use client'

import React from 'react'
import Link from 'next/link'
import { Component } from '@/lib/page-builder/store'
import { ComponentConfig } from '@/lib/page-builder/component-registry'

interface ButtonComponentProps {
  component: Component
  isEditing: boolean
  isSelected: boolean
  isHovered: boolean
  config: ComponentConfig
}

const ButtonComponent: React.FC<ButtonComponentProps> = ({
  component,
  isEditing,
  isSelected,
  isHovered,
  config
}) => {
  const { 
    text = 'Click me',
    href = '#',
    target = '_self',
    variant = 'primary'
  } = component.content
  
  const buttonStyles = {
    backgroundColor: component.styles.backgroundColor || '#3b82f6',
    color: component.styles.color || '#ffffff',
    padding: component.styles.padding || '12px 24px',
    borderRadius: component.styles.borderRadius || '6px',
    border: component.styles.border || 'none',
    fontSize: component.styles.fontSize || '16px',
    fontWeight: component.styles.fontWeight || '500',
    cursor: isEditing ? 'default' : 'pointer',
    textDecoration: 'none',
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    transition: 'all 0.2s ease-in-out',
    boxShadow: component.styles.boxShadow || 'none',
    transform: component.styles.transform || 'none'
  }
  
  // Apply variant-specific styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'secondary':
        return {
          backgroundColor: '#6b7280',
          color: '#ffffff'
        }
      case 'outline':
        return {
          backgroundColor: 'transparent',
          color: component.styles.backgroundColor || '#3b82f6',
          border: `2px solid ${component.styles.backgroundColor || '#3b82f6'}`
        }
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          color: component.styles.backgroundColor || '#3b82f6',
          border: 'none'
        }
      default:
        return {}
    }
  }
  
  const finalStyles = {
    ...buttonStyles,
    ...getVariantStyles()
  }
  
  const handleClick = (e: React.MouseEvent) => {
    if (isEditing) {
      e.preventDefault()
      e.stopPropagation()
    }
  }
  
  const buttonContent = (
    <span className="truncate">
      {text}
    </span>
  )
  
  if (isEditing || !href || href === '#') {
    return (
      <button
        type="button"
        style={finalStyles}
        onClick={handleClick}
        className={`
          ${isEditing ? 'pointer-events-none' : ''}
          focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
        `}
      >
        {buttonContent}
      </button>
    )
  }
  
  // Check if it's an external link
  const isExternal = href.startsWith('http') || href.startsWith('mailto:') || href.startsWith('tel:')
  
  if (isExternal) {
    return (
      <a
        href={href}
        target={target}
        rel={target === '_blank' ? 'noopener noreferrer' : undefined}
        style={finalStyles}
        className="focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        {buttonContent}
      </a>
    )
  }
  
  return (
    <Link
      href={href}
      target={target}
      style={finalStyles}
      className="focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
    >
      {buttonContent}
    </Link>
  )
}

export default ButtonComponent
