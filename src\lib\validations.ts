import { z } from 'zod'

// User schemas
export const createUserSchema = z.object({
  email: z.string().email(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  imageUrl: z.string().url().optional(),
  role: z.enum(['ADMIN', 'USER', 'CLIENT']).default('USER'),
})

export const updateUserSchema = createUserSchema.partial()

// Service schemas
export const createServiceSchema = z.object({
  categoryId: z.union([z.string(), z.number()]).transform(val => String(val)),
  name: z.string().min(1).max(255),
  slug: z.string().max(255).optional(),
  description: z.string().min(1),
  excerpt: z.string().max(500).optional(),
  iconClass: z.string().max(100).optional(),
  logoUrl: z.string().url().optional(),
  price: z.coerce.number().positive(),
  discountRate: z.coerce.number().int().min(0).max(100).optional(),
  totalDiscount: z.coerce.number().optional(),
  manager: z.string().max(50).optional(),
  duration: z.string().max(50).optional(),
  complexity: z.enum(['Simple', 'Medium', 'Complex']).optional(),
  features: z.string().optional(), // JSON string
  isActive: z.boolean().default(true),
  displayOrder: z.coerce.number().int().default(0),
})

export const updateServiceSchema = createServiceSchema.partial()

// Project schemas
export const createProjectSchema = z.object({
  orderid: z.string(),
  clientid: z.string().optional(),
  name: z.string().min(1),
  description: z.string().min(1),
  projgoals: z.string().optional(),
  projmanager: z.string().optional(),
  projstartdate: z.union([z.string(), z.date()]).optional().transform((val) => {
    if (!val || val === '') return undefined
    return typeof val === 'string' ? new Date(val) : val
  }),
  projcompletiondate: z.union([z.string(), z.date()]).optional().transform((val) => {
    if (!val || val === '') return undefined
    return typeof val === 'string' ? new Date(val) : val
  }),
  estimatecost: z.coerce.number().positive().optional(),
  estimatetime: z.string().optional(),
  estimateeffort: z.string().optional(),
  status: z.enum(['PLANNING', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD', 'CANCELLED']).default('PLANNING'),
  isfeatured: z.union([z.boolean(), z.string()]).default(false).transform((val) => {
    if (typeof val === 'string') return val === 'true'
    return val
  }),
  ispublic: z.union([z.boolean(), z.string()]).default(false).transform((val) => {
    if (typeof val === 'string') return val === 'true'
    return val
  }),
  displayorder: z.coerce.number().int().default(0),
  imageurl: z.string().optional(),
  projecturl: z.string().optional(),
  githuburl: z.string().optional(),
  tags: z.string().optional(),
})

export const updateProjectSchema = createProjectSchema.partial()

// Client schemas
export const createClientSchema = z.object({
  userId: z.string().optional(),
  companyName: z.string().min(1).max(200),
  contactName: z.string().min(1).max(100),
  contactPosition: z.string().max(100).optional(),
  contactEmail: z.string().email().max(100),
  contactPhone: z.string().max(20).optional(),
  contactFax: z.string().max(20).optional(),
  website: z.string().max(100).optional(),
  companyWebsite: z.string().max(100).optional(),
  address: z.string().max(200).optional(),
  city: z.string().max(100).optional(),
  state: z.string().max(50).optional(),
  zipCode: z.string().max(20).optional(),
  country: z.string().max(100).optional(),
  logoUrl: z.string().max(500).optional(),
  notes: z.string().optional(),
  isActive: z.union([z.boolean(), z.string()]).default(true).transform((val) => {
    if (typeof val === 'string') return val === 'true'
    return val
  }),
})

export const updateClientSchema = createClientSchema.partial()

// Blog post schemas
export const createBlogPostSchema = z.object({
  title: z.string().min(1).max(255),
  slug: z.string().min(1).max(255),
  content: z.string().min(1),
  excerpt: z.string().optional().nullable().transform(val => val || undefined),
  featuredImageUrl: z.string().optional().nullable().transform(val => val || undefined),
  authorId: z.string().optional().nullable().transform(val => val || undefined),
  isPublished: z.union([z.boolean(), z.string()]).default(false).transform((val) => {
    if (typeof val === 'string') return val === 'true'
    return val
  }),
  publishedAt: z.union([z.string(), z.date()]).optional().nullable().transform((val) => {
    if (!val || val === '') return undefined
    return typeof val === 'string' ? new Date(val) : val
  }),
  categories: z.string().optional().nullable().transform(val => val || undefined),
  tags: z.string().optional().nullable().transform(val => val || undefined),
})

export const updateBlogPostSchema = createBlogPostSchema.partial()

// Team member schemas
export const createTeamMemberSchema = z.object({
  name: z.string().min(1),
  position: z.string().min(1),
  birthDate: z.union([z.string(), z.date()]).optional().transform((val) => {
    if (!val || val === '') return undefined
    return typeof val === 'string' ? new Date(val) : val
  }),
  gender: z.string().optional().nullable().transform(val => val || undefined),
  maritalStatus: z.string().optional().nullable().transform(val => val || undefined),
  socialSecurityNo: z.string().optional().nullable().transform(val => val || undefined),
  hireDate: z.union([z.string(), z.date()]).optional().transform((val) => {
    if (!val || val === '') return undefined
    return typeof val === 'string' ? new Date(val) : val
  }),
  address: z.string().optional().nullable().transform(val => val || undefined),
  city: z.string().optional().nullable().transform(val => val || undefined),
  state: z.string().optional().nullable().transform(val => val || undefined),
  zipCode: z.string().optional().nullable().transform(val => val || undefined),
  country: z.string().optional().nullable().transform(val => val || undefined),
  phone: z.string().min(1),
  salary: z.union([z.string(), z.number()]).optional().nullable().transform((val) => {
    if (!val || val === '' || val === null) return undefined
    return typeof val === 'string' ? parseFloat(val) : val
  }),
  payrollMethod: z.string().optional().nullable().transform(val => val || undefined),
  resumeUrl: z.string().optional().nullable().transform(val => val || undefined),
  notes: z.string().optional().nullable().transform(val => val || undefined),
  bio: z.string().optional().nullable().transform(val => val || undefined),
  photoUrl: z.string().optional().nullable().transform(val => val || undefined),
  email: z.string().email().optional().nullable().transform(val => val || undefined),
  linkedinUrl: z.string().optional().nullable().transform(val => val || undefined),
  twitterUrl: z.string().optional().nullable().transform(val => val || undefined),
  githubUrl: z.string().optional().nullable().transform(val => val || undefined),
  displayOrder: z.union([z.string(), z.number()]).default(0).transform((val) => {
    return typeof val === 'string' ? parseInt(val) || 0 : val
  }),
  isActive: z.union([z.boolean(), z.string()]).default(true).transform((val) => {
    if (typeof val === 'string') return val === 'true'
    return val
  }),
})

export const updateTeamMemberSchema = createTeamMemberSchema.partial()

// Technology schemas
export const createTechnologySchema = z.object({
  name: z.string().min(1),
  description: z.string().min(1),
  iconUrl: z.string().optional().nullable().transform(val => val || undefined),
  displayOrder: z.union([z.string(), z.number()]).default(0).transform((val) => {
    return typeof val === 'string' ? parseInt(val) || 0 : val
  }),
  isActive: z.union([z.boolean(), z.string()]).default(true).transform((val) => {
    if (typeof val === 'string') return val === 'true'
    return val
  }),
})

export const updateTechnologySchema = createTechnologySchema.partial()

// Testimonial schemas
export const createTestimonialSchema = z.object({
  clientName: z.string().min(1).max(100),
  clientTitle: z.string().min(1).max(100),
  clientCompany: z.string().min(1).max(100),
  clientPhotoUrl: z.string().optional().nullable().transform(val => val || undefined),
  content: z.string().min(1),
  rating: z.union([z.string(), z.number()]).default(5).transform((val) => {
    return typeof val === 'string' ? parseInt(val) || 5 : val
  }),
  isFeatured: z.union([z.boolean(), z.string()]).default(false).transform((val) => {
    if (typeof val === 'string') return val === 'true'
    return val
  }),
  displayOrder: z.union([z.string(), z.number()]).default(0).transform((val) => {
    return typeof val === 'string' ? parseInt(val) || 0 : val
  }),
})

export const updateTestimonialSchema = createTestimonialSchema.partial()

// Contact form schemas
export const createContactFormSchema = z.object({
  name: z.string().min(1),
  email: z.string().email(),
  phone: z.string().optional(),
  subject: z.string().min(1),
  message: z.string().min(1),
  isread: z.boolean().default(false),
  readat: z.union([z.string(), z.date()]).optional().transform((val) => {
    if (!val || val === '') return undefined
    return typeof val === 'string' ? new Date(val) : val
  }),
  status: z.enum(['New', 'In Progress', 'Resolved', 'Closed']).default('New'),
})

export const updateContactFormSchema = createContactFormSchema.partial()

// Category schemas
export const createCategorySchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  parentId: z.string().optional(),
  isActive: z.boolean().default(true),
  displayOrder: z.coerce.number().int().default(0),
})

export const updateCategorySchema = createCategorySchema.partial()

// Order schemas
export const createOrderSchema = z.object({
  clientId: z.string(),
  orderNumber: z.string().min(1),
  description: z.string().optional(),
  totalAmount: z.coerce.number().positive(),
  status: z.enum(['PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).default('PENDING'),
  orderDate: z.date().default(() => new Date()),
})

export const updateOrderSchema = createOrderSchema.partial()

// Invoice schemas
export const createInvoiceSchema = z.object({
  clientId: z.string().min(1),
  projectId: z.string().optional().or(z.literal('')).transform(val => val === '' ? undefined : val),
  orderId: z.string().optional().or(z.literal('')).transform(val => val === '' ? undefined : val),
  contractId: z.string().optional().or(z.literal('')).transform(val => val === '' ? undefined : val),
  invoiceNumber: z.string().min(1),
  description: z.string().optional().or(z.literal('')).transform(val => val === '' ? undefined : val),
  subtotal: z.coerce.number().min(0),
  taxAmount: z.coerce.number().min(0).default(0),
  totalAmount: z.coerce.number().min(0),
  status: z.enum(['DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED']).default('DRAFT'),
  issueDate: z.coerce.date(),
  dueDate: z.coerce.date(),
  paidAt: z.coerce.date().optional(),
})

export const updateInvoiceSchema = z.object({
  clientId: z.string().min(1).optional(),
  projectId: z.union([z.string(), z.null(), z.literal('')]).transform(val => val === '' || val === null ? undefined : val).optional(),
  orderId: z.union([z.string(), z.null(), z.literal('')]).transform(val => val === '' || val === null ? undefined : val).optional(),
  contractId: z.union([z.string(), z.null(), z.literal('')]).transform(val => val === '' || val === null ? undefined : val).optional(),
  invoiceNumber: z.string().min(1).optional(),
  description: z.union([z.string(), z.null(), z.literal('')]).transform(val => val === '' || val === null ? undefined : val).optional(),
  subtotal: z.coerce.number().min(0).optional(),
  taxAmount: z.coerce.number().min(0).optional(),
  totalAmount: z.coerce.number().min(0).optional(),
  status: z.enum(['DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED']).optional(),
  issueDate: z.coerce.date().optional(),
  dueDate: z.coerce.date().optional(),
  paidAt: z.union([z.string(), z.null(), z.literal('')]).transform(val => val === '' || val === null ? undefined : val ? new Date(val) : undefined).optional(),
})

// Job listing schemas
export const createJobListingSchema = z.object({
  title: z.string().min(1),
  description: z.string().min(1),
  requirements: z.string().min(1),
  location: z.string().min(1),
  employmentType: z.string().min(1),
  salaryMin: z.coerce.number().positive().optional(),
  salaryMax: z.coerce.number().positive().optional(),
  salaryCurrency: z.string().default('USD'),
  isRemote: z.boolean().default(false),
  isActive: z.boolean().default(true),
  expiresAt: z.date().optional(),
})

export const updateJobListingSchema = createJobListingSchema.partial()

// Job Application schemas
export const createJobApplicationSchema = z.object({
  jobListingId: z.string(),
  applicantName: z.string().min(1),
  applicantEmail: z.string().email(),
  applicantPhone: z.string().optional(),
  resumeUrl: z.string().url().optional(),
  coverLetter: z.string().optional(),
  status: z.enum(['PENDING', 'REVIEWED', 'INTERVIEW', 'HIRED', 'REJECTED']).default('PENDING'),
  notes: z.string().optional(),
})

export const updateJobApplicationSchema = createJobApplicationSchema.partial()

// Contract schemas
export const createContractSchema = z.object({
  clientId: z.string(),
  projectId: z.string().optional(),
  orderId: z.string().optional(),
  title: z.string().min(1),
  description: z.string().optional(),
  contractNumber: z.string().min(1),
  value: z.coerce.number().positive(),
  status: z.enum(['DRAFT', 'PENDING', 'SIGNED', 'ACTIVE', 'COMPLETED', 'TERMINATED']).default('DRAFT'),
  startDate: z.coerce.date(),
  endDate: z.coerce.date(),
  signedAt: z.coerce.date().optional(),
  expiresAt: z.coerce.date().optional(),
  terms: z.string().optional(),
})

export const updateContractSchema = createContractSchema.partial()

// Additional schemas for missing models

// Hero Section schemas
export const createHeroSectionSchema = z.object({
  title: z.string().min(1),
  metaDescription: z.string().optional(),
  metaKeywords: z.string().optional(),
  pageName: z.string().min(1).default('Home'),
  mainTitle: z.string().min(1),
  mainSubtitle: z.string().min(1),
  mainDescription: z.string().optional(),
  primaryButtonText: z.string().optional(),
  primaryButtonUrl: z.string().optional(),
  secondaryButtonText: z.string().optional(),
  secondaryButtonUrl: z.string().optional(),
  enableSlideshow: z.boolean().default(true),
  slideshowSpeed: z.coerce.number().int().min(1000).max(10000).default(5000),
  autoplay: z.boolean().default(true),
  showDots: z.boolean().default(true),
  showArrows: z.boolean().default(true),
  enableFloatingElements: z.boolean().default(true),
  floatingElementsConfig: z.string().optional(),
  isActive: z.boolean().default(true),
  modifiedBy: z.string().optional(),
})

export const updateHeroSectionSchema = createHeroSectionSchema.partial()

// Hero Slide schemas
export const createHeroSlideSchema = z.object({
  heroSectionId: z.string(),
  content: z.string().min(1),
  mediaType: z.enum(['image', 'video']).default('image'),
  imageUrl: z.string().optional(),
  videoUrl: z.string().optional(),
  mediaAlt: z.string().optional(),
  videoAutoplay: z.boolean().default(true),
  videoMuted: z.boolean().default(true),
  videoLoop: z.boolean().default(true),
  videoControls: z.boolean().default(false),
  buttonText: z.string().optional(),
  buttonUrl: z.string().optional(),
  displayOrder: z.coerce.number().int().default(0),
  isActive: z.boolean().default(true),
  animationType: z.enum(['fade', 'slide', 'zoom']).default('fade'),
  duration: z.coerce.number().int().min(1000).max(10000).default(5000),
})

export const updateHeroSlideSchema = createHeroSlideSchema.partial()

// About Page schemas
export const createAboutPageSchema = z.object({
  title: z.string().min(1),
  content: z.string().min(1),
  isActive: z.boolean().default(true),
})

export const updateAboutPageSchema = createAboutPageSchema.partial()

// About Page Section schemas
export const createAboutPageSectionSchema = z.object({
  aboutPageId: z.string(),
  title: z.string().min(1),
  content: z.string().min(1),
  imageUrl: z.string().url().optional(),
  displayOrder: z.coerce.number().int().default(0),
  isActive: z.boolean().default(true),
})

export const updateAboutPageSectionSchema = createAboutPageSectionSchema.partial()

// Legal Page schemas
export const createLegalPageSchema = z.object({
  title: z.string().min(1),
  slug: z.string().min(1).optional(),
  metaDescription: z.string().optional().nullable().transform(val => val || undefined),
  content: z.string().min(1),
  isActive: z.union([z.boolean(), z.string()]).default(true).transform((val) => {
    if (typeof val === 'string') return val === 'true'
    return val
  }),
  displayOrder: z.union([z.string(), z.number()]).default(0).transform((val) => {
    return typeof val === 'string' ? parseInt(val) || 0 : val
  }),
  modifiedBy: z.string().optional().nullable().transform(val => val || 'admin'),
  lastModified: z.union([z.string(), z.date()]).optional().transform((val) => {
    if (!val || val === '') return new Date()
    return typeof val === 'string' ? new Date(val) : val
  }),
})

export const updateLegalPageSchema = createLegalPageSchema.partial()

// Legal Page Section schemas
export const createLegalPageSectionSchema = z.object({
  legalPageId: z.string(),
  title: z.string().min(1),
  content: z.string().min(1),
  displayOrder: z.coerce.number().int().default(0),
})

export const updateLegalPageSectionSchema = createLegalPageSectionSchema.partial()

// Site Setting schemas
export const createSiteSettingSchema = z.object({
  key: z.string().min(1),
  value: z.string().min(1),
  description: z.string().optional(),
  icon: z.string().optional(),
  category: z.string().optional(),
  isActive: z.boolean().default(true),
})

export const updateSiteSettingSchema = createSiteSettingSchema.partial()

// Export all schemas as a single object for easier imports
export const schemas = {
  user: { create: createUserSchema, update: updateUserSchema },
  service: { create: createServiceSchema, update: updateServiceSchema },
  project: { create: createProjectSchema, update: updateProjectSchema },
  client: { create: createClientSchema, update: updateClientSchema },
  blog: { create: createBlogPostSchema, update: updateBlogPostSchema },
  teamMember: { create: createTeamMemberSchema, update: updateTeamMemberSchema },
  technology: { create: createTechnologySchema, update: updateTechnologySchema },
  testimonial: { create: createTestimonialSchema, update: updateTestimonialSchema },
  contactForm: { create: createContactFormSchema, update: updateContactFormSchema },
  category: { create: createCategorySchema, update: updateCategorySchema },
  order: { create: createOrderSchema, update: updateOrderSchema },
  invoice: { create: createInvoiceSchema, update: updateInvoiceSchema },
  jobListing: { create: createJobListingSchema, update: updateJobListingSchema },
  jobApplication: { create: createJobApplicationSchema, update: updateJobApplicationSchema },
  contract: { create: createContractSchema, update: updateContractSchema },
  heroSection: { create: createHeroSectionSchema, update: updateHeroSectionSchema },
  heroSlide: { create: createHeroSlideSchema, update: updateHeroSlideSchema },
  aboutPage: { create: createAboutPageSchema, update: updateAboutPageSchema },
  aboutPageSection: { create: createAboutPageSectionSchema, update: updateAboutPageSectionSchema },
  legalPage: { create: createLegalPageSchema, update: updateLegalPageSchema },
  legalPageSection: { create: createLegalPageSectionSchema, update: updateLegalPageSectionSchema },
  siteSetting: { create: createSiteSettingSchema, update: updateSiteSettingSchema },
}
