import { NextRequest } from 'next/server'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successR<PERSON>ponse, 
  requireAuth,
  validate<PERSON>eth<PERSON> 
} from '@/lib/api-utils'
import { 
  parseFormData, 
  uploadFiles, 
  FileUploadError,
  getFileCategory 
} from '@/lib/file-upload'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// POST /api/upload - Upload files
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAuth(request)
  validateMethod(request, ['POST'])

  try {
    // Parse form data
    const { files, fields } = await parseFormData(request)

    if (files.length === 0) {
      throw new Error('No files provided')
    }

    // Get upload category from fields (optional)
    const category = fields.category || 'general'

    // Upload files
    const uploadedFiles = await uploadFiles(files, category, true)

    // Format response
    const response = uploadedFiles.map(file => ({
      ...file,
      category: getFileCategory(file.mimeType),
    }))
    return successResponse(
      {
        files: response,
        count: response.length,
      },
      `${response.length} file(s) uploaded successfully`
    )
  } catch (error) {
    if (error instanceof FileUploadError) {
      throw error
    }
    throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
})

// GET /api/upload - Get upload configuration (for frontend)
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAuth(request)
  
  return successResponse({
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'),
    allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/gif,image/webp,application/pdf').split(','),
    categories: [
      'general',
      'profile',
      'project',
      'blog',
      'document',
      'logo',
    ],
  })
})
