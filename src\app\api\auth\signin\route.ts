import { NextRequest, NextResponse } from 'next/server'
import { signIn } from 'next-auth/react'
import { authRateLimiter } from '@/lib/rate-limit'
import { AuditLogger } from '@/lib/audit-log'
import { z } from 'zod'

const signInSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
})

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await authRateLimiter.isAllowed(request)
    
    if (!rateLimitResult.allowed) {
      // Log rate limit exceeded
      await AuditLogger.logAuth(
        'LOGIN_FAILED',
        undefined,
        { reason: 'Rate limit exceeded' },
        request
      )
      
      return NextResponse.json(
        {
          error: 'Too many login attempts',
          message: 'Please wait before trying again',
          retryAfter: rateLimitResult.retryAfter
        },
        { 
          status: 429,
          headers: {
            'Retry-After': rateLimitResult.retryAfter?.toString() || '60',
            'X-RateLimit-Limit': '5',
            'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
            'X-RateLimit-Reset': new Date(rateLimitResult.resetTime).toISOString()
          }
        }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = signInSchema.parse(body)

    // Record the attempt (will be counted against rate limit)
    await authRateLimiter.recordAttempt(request, false) // Assume failed until proven otherwise

    // Return success response (actual authentication is handled by NextAuth)
    return NextResponse.json({
      success: true,
      message: 'Authentication request processed'
    })

  } catch (error) {
    console.error('Sign-in API error:', error)
    
    // Log the error
    await AuditLogger.logAuth(
      'LOGIN_FAILED',
      undefined,
      { reason: 'API error', error: error instanceof Error ? error.message : 'Unknown error' },
      request
    )

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
